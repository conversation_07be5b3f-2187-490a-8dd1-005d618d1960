<?php

namespace app\api\validate;

use think\Validate;
use app\common\model\MemberShareModel;

class MemberShare extends Validate
{
    protected $rule = [
        'member_id' => 'require|integer|gt:0',
        'share_type' => 'require|integer|in:1,2,3,4,5',
        'object_id' => 'require|integer|gt:0',
        'object_name' => 'max:255',
        'share_channel' => 'integer|in:1,2,3,4,5,6',
        'share_path' => 'max:255',
        'expire_time' => 'integer',
        'longitude' => 'float|between:-180,180',
        'latitude' => 'float|between:-90,90',
        'area_id' => 'max:20',
        'deviceid' => 'max:64',
        'device_model' => 'max:100',
        'platform' => 'max:50',
        'version' => 'max:20',
        'share_id' => 'require|length:32',
        'enter_path' => 'max:255',
        'referrer' => 'max:255',
        'scene' => 'max:100',
        'network_type' => 'max:20',
    ];

    protected $message = [
        'member_id.require' => '用户ID不能为空',
        'member_id.integer' => '用户ID必须为整数',
        'member_id.gt' => '用户ID必须大于0',
        'share_type.require' => '分享类型不能为空',
        'share_type.integer' => '分享类型必须为整数',
        'share_type.in' => '分享类型值无效',
        'object_id.require' => '分享对象ID不能为空',
        'object_id.integer' => '分享对象ID必须为整数',
        'object_id.gt' => '分享对象ID必须大于0',
        'object_name.max' => '分享对象名称不能超过255个字符',
        'share_channel.integer' => '分享渠道必须为整数',
        'share_channel.in' => '分享渠道值无效',
        'share_path.max' => '分享路径不能超过255个字符',
        'expire_time.integer' => '过期时间必须为整数',
        'longitude.float' => '经度必须为浮点数',
        'longitude.between' => '经度值必须在-180到180之间',
        'latitude.float' => '纬度必须为浮点数',
        'latitude.between' => '纬度值必须在-90到90之间',
        'area_id.max' => '区域ID不能超过20个字符',
        'deviceid.max' => '设备ID不能超过64个字符',
        'device_model.max' => '设备型号不能超过100个字符',
        'platform.max' => '平台信息不能超过50个字符',
        'version.max' => '版本号不能超过20个字符',
        'share_id.require' => '分享ID不能为空',
        'share_id.length' => '分享ID长度必须为32位',
        'enter_path.max' => '进入路径不能超过255个字符',
        'referrer.max' => '来源信息不能超过255个字符',
        'scene.max' => '场景值不能超过100个字符',
        'network_type.max' => '网络类型不能超过20个字符',
    ];

    protected $scene = [
        'create' => ['member_id', 'share_type', 'object_id', 'object_name', 'share_channel', 'share_path', 'expire_time', 'longitude', 'latitude', 'area_id', 'deviceid', 'device_model', 'platform', 'version'],
        'click' => ['share_id', 'member_id', 'enter_path', 'referrer', 'scene', 'longitude', 'latitude', 'area_id', 'deviceid', 'device_model', 'platform', 'network_type', 'version'],
    ];

    /**
     * 自定义验证分享类型
     */
    protected function checkShareType($value, $rule, $data = [])
    {
        $validTypes = array_keys(MemberShareModel::SHARE_TYPE_TEXT);
        return in_array($value, $validTypes);
    }

    /**
     * 自定义验证分享渠道
     */
    protected function checkShareChannel($value, $rule, $data = [])
    {
        $validChannels = array_keys(MemberShareModel::SHARE_CHANNEL_TEXT);
        return in_array($value, $validChannels);
    }
}
