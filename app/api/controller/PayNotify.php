<?php

namespace app\api\controller;

use app\common\model\OrderPaymentsModel;
use app\common\model\OrdersModel;
use app\common\service\WxPayService;
use think\facade\Event;
use think\facade\Log;

class PayNotify
{
    /**
     * 微信支付回调
     *
     * 支持新的支付表结构
     *
     * @throws \Yansongda\Pay\Exceptions\InvalidArgumentException
     * @throws \Yansongda\Pay\Exceptions\InvalidSignException
     */
    public function weChatPay()
    {
        $params = [];
        $pay = new WxPayService();
        $data = $pay->wxVerify();

        // 记录回调数据
        Log::info('WeChatPay callback data: ' . json_encode($data));

        if (!empty($data) && $data['result_code'] == 'SUCCESS') {
            $out_trade_no = $data['out_trade_no'];
            $out_trade_no_parts = explode('_', $out_trade_no);

            // 新的订单号格式：p{PAYMENT_ID}_{ORDER_ID_HASH}_{RANDOM}
            if (strpos($out_trade_no, 'p') === 0 && count($out_trade_no_parts) >= 2) {
                // 提取支付ID
                $paymentId = intval(substr($out_trade_no_parts[0], 1));

                // 查询支付计划
                $payment = OrderPaymentsModel::where('id', $paymentId)->findOrEmpty();

                if (!$payment->isEmpty()) {
                    // 构造新版支付回调参数
                    $params['payment_id'] = $payment->id;
                    $params['order_id'] = $payment->order_id;
                    $params['transaction_id'] = $data['transaction_id'];
                    $params['time_end'] = strtotime($data['time_end']);
                    $params['openid'] = $data['openid'] ?? '';
                    $params['pay_code'] = json_encode($data);
                    $params['note'] = '微信支付回调';

                    // 记录日志
                    Log::info('Using new payment format: payment_id=' . $paymentId);
                }
            }
            // 兼容旧的订单号格式：{PAY_ID}_{ORDER_ID}_{MEMBER_ID}
            else if (count($out_trade_no_parts) >= 3) {
                // 构造旧版支付回调参数
                $params['pay_id'] = $out_trade_no_parts[0];
                $params['order_id'] = $out_trade_no_parts[1];
                $params['member_id'] = $out_trade_no_parts[2];
                $params['pay_total'] = $data['total_fee'] / 100;
                $params['pay_sn'] = $data['transaction_id'];
                $params['pay_order_sn'] = $data['out_trade_no'];
                $params['time_end'] = strtotime($data['time_end']);
                $params['payment'] = 1;
                $params['pay_gateway'] = $data['attach'];
                $params['addon'] = serialize($data);

                // 记录日志
                Log::info('Using legacy payment format: order_id=' . $out_trade_no_parts[1]);
            }

            // 触发支付成功事件
            if (!empty($params)) {
                $result = Event::trigger('PaySuccess', $params);
                Log::info('PaySuccess result: ' . json_encode($result));
            } else {
                Log::error('Failed to parse out_trade_no: ' . $out_trade_no);
            }
        }

        return $pay->success();
    }

    /**
     * 模拟支付成功回调
     *
     * 使用方法：
     * 1. 指定支付ID：notify/weChatPayTest?order_id=YOUR_ORDER_ID&payment_id=PAYMENT_ID
     * 2. 自动查找支付：notify/weChatPayTest?order_id=YOUR_ORDER_ID
     * 3. 旧版支付格式：notify/weChatPayTest?order_id=YOUR_ORDER_ID&legacy=1
     *
     * @return \think\Response
     */
    public function weChatPayTest()
    {
        // 获取参数
        $orderId = $_GET['order_id'] ?? '';
        $paymentId = isset($_GET['payment_id']) ? intval($_GET['payment_id']) : 0;
        $useLegacy = isset($_GET['legacy']) && $_GET['legacy'] == 1;

        if (empty($orderId)) {
            return json(['code' => 0, 'msg' => '订单ID不能为空']);
        }

        // 记录日志
        Log::info('WeChatPayTest params: ' . json_encode(['order_id' => $orderId, 'payment_id' => $paymentId, 'legacy' => $useLegacy]));

        // 查询订单信息
        $order = OrdersModel::where('order_id', $orderId)->findOrEmpty();
        if ($order->isEmpty()) {
            return json(['code' => 0, 'msg' => '订单不存在']);
        }
        if (env('ENVMODE') != "dev") {
            if (!in_array($order->shop_id, [216, 231])) { //216 是测试门店id , 231 产品创建的测试门店
                return json(['code' => 0, 'msg' => '不可以调用哦']);
            }
        }

        // 构造支付回调参数
        $params = [];

        if ($useLegacy) {
            // 使用旧版支付格式
            $params['pay_id'] = isset($_GET['pay_id']) ? intval($_GET['pay_id']) : 42; // 默认值
            $params['order_id'] = $orderId;
            $params['member_id'] = $order->member_id;
            $params['pay_total'] = isset($_GET['amount']) ? floatval($_GET['amount']) : 0.01;
            $params['pay_sn'] = 'fake_' . $orderId . '_' . rand(1000, 9999);
            $params['pay_order_sn'] = $params['pay_id'] . '_' . $orderId . '_' . $order->member_id;
            $params['time_end'] = time();
            $params['payment'] = 1;
            $params['pay_gateway'] = '{"order_type":"normal"}';
            $params['addon'] = 'fake';

            // 记录日志
            Log::info('Using legacy payment format for test: ' . $orderId);
        } else {
            // 查询支付计划
            if (!empty($paymentId)) {
                // 如果指定了支付ID，直接查询该支付
                $payment = OrderPaymentsModel::where('id', $paymentId)
                    ->where('order_id', $orderId)
                    ->where('status', 'pending')
                    ->findOrEmpty();
            } else {
                // 如果没有指定支付ID，查找该订单的第一个待支付记录
                $payment = OrderPaymentsModel::where('order_id', $orderId)
                    ->where('status', 'pending')
                    ->order('id', 'asc')
                    ->findOrEmpty();
            }

            if ($payment->isEmpty()) {
                return json(['code' => 0, 'msg' => '支付计划不存在或已支付']);
            }

            // 构造新版支付回调参数
            $params = [
                'payment_id' => $payment->id,
                'order_id' => $payment->order_id,
                'transaction_id' => 'fake_' . $payment->order_id . '_' . rand(1000, 9999),
                'time_end' => time(),
                'openid' => 'fake_openid_' . rand(1000, 9999),
                'note' => '模拟支付成功回调',
                'out_trade_no' => 'p' . $payment->id . '_' . substr(md5($payment->order_id), 0, 10) . '_' . rand(100, 999)
            ];
        }

        // 触发支付成功事件
        $res = Event::trigger('PaySuccess', $params);

        // 返回结果
        return json([
            'code' => 200,
            'msg' => '模拟支付回调完成',
            'data' => [
                'order' => [
                    'order_id' => $order->order_id,
                    'pay_status' => $order->pay_status,
                    'has_deposit' => $order->has_deposit,
                    'deposit_amount' => $order->deposit_amount,
                    'deposit_pay_status' => $order->deposit_pay_status,
                ],
                'params' => $params,
                'result' => $res
            ]
        ]);
    }
}
