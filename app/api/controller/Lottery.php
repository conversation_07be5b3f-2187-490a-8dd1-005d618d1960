<?php

namespace app\api\controller;

use app\common\enum\Response;
use app\common\service\ActivityService;
use app\common\service\UploadService;
use app\common\service\VerificationOrderService;

class Lottery extends Base
{
    // 根据期号获取抽奖活动页面
    public function getLotteryWap()
    {
        $sourceType = '';
        if (isset($this->param['activity_id']) && $this->param['activity_id'] > 0) {
            $msg = "";
            $activityStatus = ActivityService::checkStatusById($this->param['activity_id'], $msg);
            if (!$activityStatus) {
                return json(fail(Response::ACTIVITY_ERROR, $msg));
            }
            $sourceType = 'activity';
        }
        if (empty($this->param['period'])) {
            return json(fail(Response::REQUEST_PARAM_ERROR, '期号不能为空'));
        }
        $data = [
            'period' => $this->param['period'],
            'source_type' => $sourceType,
            'source_id' => $this->param['activity_id'] ?? 0,
        ];
        $config = config('api.services.Member');
        $url = $config['BaseURL'] . $config['Api']['LotteryWap'];
        $token = $this->request->token;
        $res = curlGetApiContentByUrl($url, $data, ['Authorization: Bearer ' . $token], false);
        if (empty($res)) {
            return json(fail(Response::REQUEST_ERROR, '请求失败'));
        }
        if ($res['code'] != 1) {
            return json(fail(Response::REQUEST_ERROR, $res['msg']));
        }
        $imageIds = [];
        foreach ($res['data']['prizes'] as $k => $v) {
            if ($v['image_id'] > 0) {
                $imageIds[] = $v['image_id'];
            }
            if ($v['win_popup_image_id'] > 0) {
                $imageIds[] = $v['image_id'];
            }
        }
        if (!empty($imageIds)) {
            $file = new UploadService();
            $files = $file->getByIds($imageIds, "43x43");
            foreach ($res['data']['prizes'] as $k => &$v) {
                if ($v['image_id'] > 0) {
                    $v['image_url'] = $files[$v['image_id']] ?? '';
                }
                if ($v['win_popup_image_id'] > 0) {
                    $v['win_popup_image_url'] = $files[$v['win_popup_image_id']] ?? '';
                }
            }
        }
        return json(success($res['data']));
    }

    // 点击抽奖
    public function doLottery()
    {
        $sourceType = '';
        if (isset($this->param['activity_id']) && $this->param['activity_id'] > 0) {
            $msg = "";
            $activityStatus = ActivityService::checkStatusById($this->param['activity_id'], $msg);
            if (!$activityStatus) {
                return json(fail(Response::ACTIVITY_ERROR, $msg));
            }
            $sourceType = 'activity';
        }
        if (empty($this->param['period'])) {
            return json(fail(Response::REQUEST_PARAM_ERROR, '期号不能为空'));
        }
        $data = [
            'period' => $this->param['period'],
            'source_type' => $sourceType,
            'source_id' => $this->param['activity_id'] ?? 0,
        ];
        $config = config('api.services.Member');
        $url = $config['BaseURL'] . $config['Api']['DoLotteryPrize'];
        $token = $this->request->token;
        $res = curlPostApiContentByUrlAndParams($url, $data, ['Authorization: Bearer ' . $token], false);
        if (empty($res)) {
            return json(fail(Response::REQUEST_ERROR, '请求失败'));
        }
        if ($res['code'] != 1) {
            return json(fail(Response::REQUEST_ERROR, $res['msg']));
        }
        return json(success($res['data']));
    }

    // 增加抽奖次数
    public function addTimes()
    {
        $sourceType = '';
        if (isset($this->param['activity_id']) && $this->param['activity_id'] > 0) {
            $msg = "";
            $activityStatus = ActivityService::checkStatusById($this->param['activity_id'], $msg);
            if (!$activityStatus) {
                return json(fail(Response::ACTIVITY_ERROR, $msg));
            }
            $sourceType = 'activity';
        }
        if (empty($this->param['period'])) {
            return json(fail(Response::REQUEST_PARAM_ERROR, '期号不能为空'));
        }
        // type: open|share
        $data = [
            'period' => $this->param['period'],
            'type' => $this->param['type'] ?? 'open',
            'source_type' => $sourceType,
            'source_id' => $this->param['activity_id'] ?? 0,
        ];
        $config = config('api.services.Member');
        $url = $config['BaseURL'] . $config['Api']['AddChance'];
        $token = $this->request->token;
        $res = curlPostApiContentByUrlAndParams($url, $data, ['Authorization: Bearer ' . $token], false);
        if (empty($res)) {
            return json(fail(Response::REQUEST_ERROR, '请求失败'));
        }
        if ($res['code'] != 1) {
            return json(fail(Response::REQUEST_ERROR, $res['msg']));
        }
        return json(success($res['data']));
    }

    // 获取抽奖次数列表
    public function getTimes()
    {
        $sourceType = '';
        if (isset($this->param['activity_id']) && $this->param['activity_id'] > 0) {
            $msg = "";
            $activityStatus = ActivityService::checkStatusById($this->param['activity_id'], $msg);
            if (!$activityStatus) {
                return json(fail(Response::ACTIVITY_ERROR, $msg));
            }
            $sourceType = 'activity';
        }
        if (empty($this->param['period'])) {
            return json(fail(Response::REQUEST_PARAM_ERROR, '期号不能为空'));
        }
        $data = [
            'period' => $this->param['period'],
            'source_type' => $sourceType,
            'source_id' => $this->param['activity_id'] ?? 0,
        ];
        $config = config('api.services.Member');
        $url = $config['BaseURL'] . $config['Api']['GetChance'];
        $token = $this->request->token;
        $res = curlGetApiContentByUrl($url, $data, ['Authorization: Bearer ' . $token], false);
        if (empty($res)) {
            return json(fail(Response::REQUEST_ERROR, '请求失败'));
        }
        if ($res['code'] != 1) {
            return json(fail(Response::REQUEST_ERROR, $res['msg']));
        }
        return json(success($res['data']));
    }

    // 领取奖品
    public function receivePrize()
    {
        if (isset($this->param['activity_id']) && $this->param['activity_id'] > 0) {
            $msg = "";
            $activityStatus = ActivityService::checkStatusById($this->param['activity_id'], $msg);
            if (!$activityStatus) {
                return json(fail(Response::ACTIVITY_ERROR, $msg));
            }
        }
        $id = $this->param['id'] ?? 0;
        if (empty($id)) {
            return json(fail(Response::REQUEST_PARAM_ERROR, '奖品ID不能为空'));
        }
        $url = config('api.services.Member')['BaseURL'] . config('api.services.Member')['Api']['PrizeClaim'];
        $url = sprintf($url, $id);
        $token = $this->request->token;
        $res = curlPostApiContentByUrlAndParams($url, ['id' => $id], ['Authorization: Bearer ' . $token], false);
        if (empty($res)) {
            return json(fail(Response::REQUEST_ERROR, '请求失败'));
        }
        if ($res['code'] != 1) {
            return json(fail(Response::REQUEST_ERROR, $res['msg']));
        }
        return json(success($res['data']));
    }

    // 获取中奖记录
    public function getPrizeRecord()
    {
        $sourceType = '';
        if (isset($this->param['activity_id']) && $this->param['activity_id'] > 0) {
            $sourceType = 'activity';
        }
        if (empty($this->param['period'])) {
            return json(fail(Response::REQUEST_PARAM_ERROR, '期号不能为空'));
        }
        $data = [
            'period' => $this->param['period'],
            'source_type' => $sourceType,
            'source_id' => $this->param['activity_id'] ?? 0,
        ];
        $config = config('api.services.Member');
        $url = $config['BaseURL'] . $config['Api']['GetMyPrizes'];
        $token = $this->request->token;
        $res = curlGetApiContentByUrl($url, $data, ['Authorization: Bearer ' . $token], false);
        if (empty($res)) {
            return json(fail(Response::REQUEST_ERROR, '请求失败'));
        }
        if ($res['code'] != 1) {
            return json(fail(Response::REQUEST_ERROR, $res['msg']));
        }
        $service =  new VerificationOrderService();
        foreach ($res['data'] as $key => &$value) {
            $value['verification_qrcode']  = "";
            if ($value['express_info'] != '') {
                $express_info = json_decode($value['express_info'], true);
                if (isset($express_info['verification_code'])) {
                    $value['verification_code'] = $express_info['verification_code'];
                    $value['verification_qrcode']  = $service->generateQrCode($express_info['verification_code']);
                }
            }
        }
        return json(success($res['data']));
    }
}
