<?php

namespace app\api\controller;

use app\api\service\BaseSer;
use app\api\service\Staff\StaffSer;
use app\common\enum\staff\Worker;
use app\common\service\StaffService;
use think\response\Json;

class Staff extends Base
{
    public function index(): Json
    {
        $workerList = Worker::getList();
        $staff = [];
        $workerId = intval($this->param['worker_id'] ?? 0);
        $areaId = intval(BaseSer::$position['area_id'] ?? 0);
        if ($workerId > 0) {
            $query = [
                'worker_id' => $workerId,
                'area_id' => $areaId,
                'work_sort' => $this->param['work_sort'] ?? '', //work_sort:'',//经验排序 asc正序，desc倒序
                'age_sort' => $this->param['age_sort'] ?? '', //age_sort:'',//年龄排序 asc正序，desc倒序
                'skill_sort' => $this->param['skill_sort'] ?? '', //skill_sort:'',//证书排序 asc正序，desc倒序
                'not_search_shop' => 1, //not_search_shop:0,//是否不搜索店铺 0否 1是
            ];
            $query = array_merge($query, $this->param);
            $staff = (new StaffSer('List', $query))->getData();
        }
        return json(success(['workerList' => $workerList, 'staff' => $staff]));
    }

    public function detail(): Json
    {
        $res = [];
        $staff_id = intval($this->param['staff_id'] ?? 0);
        $member_id = intval($this->param['member_id'] ?? 0);
        if ($staff_id > 0) {
            $res = (new StaffSer('Detail', ['staff_id' => $staff_id, 'member_id' => $member_id]))->getData();
        }
        return json(success($res[0] ?? []));
    }

    public function getWorkCalendar(): Json
    {
        $service = new StaffService();
        $list = $service->getWorkCalendar($this->param);
        $list = empty($list) ? [] : array_values($list);
        return json(success($list));
    }
}
