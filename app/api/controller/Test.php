<?php

namespace app\api\controller;

use app\api\service\Goods\GoodsSer;
use app\api\service\Order\OrderSer;
use app\api\service\Staff\StaffSer;
use app\common\enum\staff\Worker;
use app\common\library\MailTool;
use app\common\model\GoodsCategoryBasicModel;
use app\common\model\MemberAccount;
use app\common\model\MemberAccountModel;
use app\common\model\OrdersModel;
use app\common\model\StaffModel;
use app\common\model\TransactionModel;
use app\common\model\VerificationCodeModel;
use app\common\service\OrderService;
use app\common\service\ShopService;
use app\common\service\TransactionService;
use app\common\service\VerificationOrderService;
use app\common\service\WxPayService;
use think\facade\Cache;
use think\facade\Db;
use think\facade\Event;
use MongoDB\Client;

class Test extends Base
{
    public function stock()
    {
        $goodsList = [];
        $goodsId = 195;
        $skuIds = [3923, 3921, 3922, 3924, 3925, 3926];
        foreach ($skuIds as $skuId) {
            $goodsList[] = ['goods_id' => $goodsId, 'goods_sku_id' => $skuId, 'stock' => 1000];
        }
        $redis = Cache::store('redis')->handler();
        foreach ($goodsList as $goods) {
            $key = "ysa:pstock:{$goods['goods_id']}";
            $redis->hsetnx($key, $goods['goods_sku_id'], $goods['stock']);
        }
    }

    public function index()
    {
        // 订单奖励
        // Event::trigger('OrderRewards', ['order_id' => '20250423515252109227']);
        // // 异步解锁
        // $orderId = '20250415571004851911';
        // $tmpAction = [
        //     'method' => 'app\common\job\Order::settlement',
        //     'param'  => [$orderId],
        // ];
        // $cRet      = call_user_func_array(str_replace(['/'], ['\\'], $tmpAction['method']), $tmpAction['param']);
        // dump($cRet);
        exit;

        // $wx = new ShopService();
        // $msg = '';
        // $res = $wx->addReceiverByShopId(120, '1646477834', '易生爱母婴护理中心', $msg);
        // echo $msg;
        // dump($res);

        // // 调用微信支付服务添加分账
        // $msg = "";
        // $wxPayService = new WxPayService();
        // $res = $wxPayService->addPersonalReceiver("ofBLr5KvXw91YwnMNO3rTm-qYavk", "王宇昕", $msg);
        // echo $msg;
        // dump($res);

        exit;
        // $service = new VerificationOrderService();
        // $code = $service->generateQrCode("1234");
        return json(['code' => 200, 'msg' => "ok"]);
        $mail = new MailTool();
        $mail->sendMailByFile('<EMAIL>', '测试标题', '<span color="red">测试内容</span>', root_path() . "README.md");
        return json(['code' => 200, 'msg' => 'ok']);
        // 异步解锁
        $orderId = '20241219529910019376';
        $tmpAction = [
            'method' => 'app\common\job\Order::settlement',
            'param'  => [$orderId],
        ];
        $cRet      = call_user_func_array(str_replace(['/'], ['\\'], $tmpAction['method']), $tmpAction['param']);
        dump($cRet);
        exit;
        $rows = Db::table('order_service')->where('order_id',  $orderId)->select();
        $order = new OrderService();
        foreach ($rows as $row) {
            $rule = $order->getSingleSalary($row['order_id'], $row['staff_id']);
            Db::table('order_service')->where('id', $row['id'])->update(['rule' => serialize($rule), 'status' => 0]);
        }
        exit;
        $row = Db::table('orders')->where('order_id',  $orderId)->find();
        $addon = unserialize($row['addon']);
        foreach ($addon['goods'] as &$goods) {
            if ($goods['clerk_money'] <= 0) {
                $goods['clerk_money'] = 0.03;
            }
        }
        unset($goods);
        Db::table('orders')->where('order_id',  $orderId)->update(['addon' => serialize($addon)]);
        dump($addon['goods']);
        exit;

        // $orders = OrdersModel::where('order_id', '20241220495598564839')->find()->toArray();
        // $orders['addon'] = unserialize($orders['addon']);
        // dump($orders);
        // // $res = (new StaffSer('Cart', ['staff_id' => 198]))->getData();
        // // dump($res);
        // exit;
        // $data = [
        //     // // 会员7， 现金，消费3.3
        //     // [7, 3.3, 'Cash', 'Purchase'],
        //     // // 会员7， 现金，退款3.3
        //     // [7, 3.3, 'Cash', 'Refund'],

        //     // 会员7， 积分，消费3
        //     // [7, 3, 'Points', 'Purchase'],

        //     // // 会员7， 余额，充值3.3
        //     [7, 3.3, 'Balance', 'Deposit'],
        //     // // 会员7， 余额，提现3.3
        //     // [7, 3.3, 'Balance', 'Withdraw'],
        //     // // 会员7， 余额，消费3.3
        //     [7, 3.3, 'Balance', 'Purchase'],
        //     // // 会员7， 余额，退款3.3
        //     [7, 3.3, 'Balance', 'Refund'],
        // ];
        // foreach ($data as $v) {
        //     $msg = '';
        //     MemberAccountModel::changeAccount($v[0], $v[1], $v[2], $v[3], $msg);
        //     echo $msg . PHP_EOL;
        // }
        // exit;
        // $data = [
        //     ['goods_id' => 1, 'goods_sku_id' => 11, 'stock' => -2],
        //     // ['goods_id' => 1, 'goods_sku_id' => 22, 'stock' => 10],
        //     // ['goods_id' => 2, 'goods_sku_id' => 33, 'stock' => 10],
        //     // ['goods_id' => 2, 'goods_sku_id' => 44, 'stock' => 10],
        // ];
        // Event::trigger('SaveStock', $data);
        // exit;
        // $data = [
        //     // ['goods_id' => 11, 'goods_sku_id' => 3846, 'stock' => 100],
        //     ['goods_id' => 11, 'goods_sku_id' => 963, 'stock' => 100],
        // ];
        // Event::trigger('SaveStock', $data);
        // exit;

        // $goodsId = 11;
        // $skuId = 963;
        // $order_id = '20241115515698109175';
        // $num = 10;
        // $script   = file_get_contents(getLuaFile('freeze'));
        // $redis = Cache::store('redis')->handler();
        // $res = $redis->eval($script, [$goodsId, $skuId, $order_id, $num]);
        // dump($res);
        // $luaError = $redis->getLastError();
        // dump($luaError);
        // exit;

        // $goodsId = 11;
        // $skuId = 963;
        // $order_id = "20241115515698109174";
        // $num = 7;

        // $script   = file_get_contents(getLuaFile('unfreeze'));
        // $redis = Cache::store('redis')->handler();
        // $res = $redis->eval($script, [$goodsId, $skuId, $order_id, $num, 2]);
        // dump($res);
        // $luaError = $redis->getLastError();
        // dump($luaError);

        $script   = file_get_contents(getLuaFile('stock'));
        $redis = Cache::store('redis')->handler();
        $goodsList = [
            ['goods_id' => 11, 'goods_sku_id' => 962],
            ['goods_id' => 11, 'goods_sku_id' => 963],
            ['goods_id' => 12, 'goods_sku_id' => 963],
        ];
        $res = $redis->eval($script, [json_encode($goodsList, JSON_UNESCAPED_UNICODE)]);
        dump(json_decode($res, true));
        $luaError = $redis->getLastError();
        dump($luaError);
        exit;
        // $data = (new GoodsSer('Cart', ['goods_id' => [102], 'sku_id' => [3836]]))->getData();
        // dump($data);
        // exit;
        // $info = StaffModel::where('id', 201)->with('relService')->find();
        // dump($info->relService);
        // echo $info->getLastSql();
        // exit;
        // $serviceIds = [1, 2, 55];
        // $clarkIds = GoodsCategoryBasicModel::where('c_id', 'in', $serviceIds)->field('clark_type_ids')->select()->column('clark_type_ids');
        // if (!empty($clarkIds)) {
        //     $clarkId_str = "";
        //     foreach ($clarkIds as $k => $v) {
        //         $clarkId_str .= $v;
        //     }
        //     $clarkArray = explode(',', $clarkId_str);
        //     // 去空去重
        //     $codes = array_filter(array_unique($clarkArray));
        // }
        // dump($clarkArray);
        // exit;
        $data = [
            ['goods_id' => 108, 'goods_sku_id' => 3846, 'stock' => 100],
            // ['goods_id' => 11, 'goods_sku_id' => 963, 'stock' => 100],
        ];
        Event::trigger('SaveStock', $data);
        exit;

        // $mongo = new Client('mongodb://172.20.0.8:27017');

        // $database = $mongo->selectDatabase('ysa-shop');

        // $collection = $database->selectCollection('api');

        // $insertOneResult = $collection->insertOne(['name' => 'shrimp', 'age' => 3]);
        // dump($insertOneResult);
        // exit;


        // 选择一个集合
        $collection = Db::connect('mongodb')->name('api');

        // 插入一条数据
        $collection->insert(['name' => 'shrimp', 'age' => 3]);

        $data = $collection->where('name', 'shrimp')->select();

        dump($data);

        // self::stock();
        // (new StaffSer('List', [
        //     'start_age' => 30,
        //     'end_age' => 35,
        //     'language' => [1, 3],
        //     'is_collect' => 1,
        //     'member_id' => 1,
        // ]))->getData();
        exit;
        // Event::trigger('StaffSkillTotal', 153);
        // exit;
        // $goodsId = 11;
        // $skuId = 963;
        // $order_id = '20241115515698109174';
        // $num = 10;
        // $script   = file_get_contents(getLuaFile('freeze'));
        // $redis = Cache::store('redis')->handler();
        // $res = $redis->eval($script, [$goodsId, $skuId, $order_id, $num]);
        // dump($res);
        // $luaError = $redis->getLastError();
        // dump($luaError);

        // exit;

        // $this->stock();
        // exit;

        // $script   = file_get_contents(getLuaFile('freeze'));
        // $redis = Cache::store('redis')->handler();

        // $goodsId = 1;
        // $skuId = 11;
        // $order_id = "20241113529748495278";
        // $num = 3;

        // $script   = file_get_contents(getLuaFile('unfreeze'));
        // $redis = Cache::store('redis')->handler();

        // $goodsId = 1;
        // $skuId = 11;
        // $order_id = "20241113529748495278";
        // $num = 1;
        // $result = $redis->eval($script, [$goodsId, $skuId, $order_id, $num]);

        $script   = file_get_contents(getLuaFile('stock'));
        $redis = Cache::store('redis')->handler();

        $goodsList = [
            ['goods_id' => 1, 'goods_sku_id' => 11],
            ['goods_id' => 2, 'goods_sku_id' => 33],
        ];

        $result = $redis->eval($script, [json_encode($goodsList, JSON_UNESCAPED_UNICODE)]);
        $luaError = $redis->getLastError();

        dump($luaError);
        dump($result);
    }
    // public function stock()
    // {
    //     // $goodsList = [
    //     //     ['goods_id' => 1, 'goods_sku_id' => 11, 'stock' => 10],
    //     //     ['goods_id' => 2, 'goods_sku_id' => 22, 'stock' => 10],
    //     //     ['goods_id' => 3, 'goods_sku_id' => 33, 'stock' => 10],
    //     //     ['goods_id' => 4, 'goods_sku_id' => 44, 'stock' => 10],
    //     // ];

    //     // $redis = Cache::store('redis')->handler();
    //     // foreach ($goodsList as $goods) {
    //     //     $key = "ysa:pstock:{$goods['goods_id']}:{$goods['goods_sku_id']}";
    //     //     $redis->hsetnx($key, 'stock', $goods['stock']);
    //     //     $redis->hsetnx($key, 'freeze', 0);
    //     // }
    // }

    // public function index()
    // {
    //     // self::stock();
    //     // $script   = file_get_contents(getLuaFile('getGoodsStock'));

    //     // $goodsList = [
    //     //     ['goods_id' => 1, 'goods_sku_id' => 11],
    //     //     ['goods_id' => 4, 'goods_sku_id' => 44],
    //     // ];

    //     $script   = file_get_contents(getLuaFile('goodsFreeze'));
    //     $goodsList = [
    //         ['goods_id' => 1, 'goods_sku_id' => 11, 'order_id' => 111, 'num' => 1],
    //         ['goods_id' => 4, 'goods_sku_id' => 44, 'order_id' => 111, 'num' => 1],
    //     ];

    //     $redis = Cache::store('redis')->handler();
    //     $result = $redis->eval($script, [json_encode($goodsList, JSON_UNESCAPED_UNICODE)]);
    //     $luaError = $redis->getLastError();

    //     dump($luaError);
    //     dump($result);
    // }
}
