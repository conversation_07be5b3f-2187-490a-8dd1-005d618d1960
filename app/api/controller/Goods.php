<?php

namespace app\api\controller;

use app\api\service\Goods\GoodsSer;
use think\response\Json;

class Goods extends Base
{
    public function detail(): Json
    {
        $res = [];
        $goods_id = intval($this->param['goods_id'] ?? 0);
        $sku_id = intval($this->param['sku_id'] ?? 0);
        $member_id = intval($this->param['member_id'] ?? 0);
        if ($goods_id > 0) {
            $res = (new GoodsSer('Detail', ['goods_id' => $goods_id, 'sku_id' => $sku_id,'member_id'=>$member_id]))->getData();
        }
        return json(success($res));
    }
}
