<?php

namespace app\api\controller;

use app\api\service\CollectSer;
use app\api\service\Goods\GoodsSer;
use app\api\service\MemberSer;
use app\api\service\ShopSer;
use app\api\service\Staff\StaffSer;
use app\common\service\CouponService;
use think\response\Json;
use app\api\validate\MemberCollect as MemberCollectValidate;
use app\api\validate\MemberFetalMovementRecord;
use app\common\enum\Response;

class Member extends Base
{
    public function my(): Json
    {
        $memberId = intval($this->param['member_id']);
        if ($memberId == 0) {
            return json(fail(Response::MEMBER_NOT_LOGIN));
        }
        $token  = $this->request->token;
        $service = new MemberSer();
        $res     = $service->info($memberId, $token);
        //获取可用优惠券数量
        $coupon = CouponService::getCouponCountByMemberId(['id' => $memberId], $token, "api");
        if ($coupon['code'] == 1) {
            $res['data']['coupon']['count'] = $coupon['data'];
        }
        return json($res);
    }

    public function edit(): Json
    {
        if ($this->param['member_id'] == 0) {
            return json(fail(Response::MEMBER_NOT_LOGIN));
        }
        $msg     = '';
        $service = new MemberSer();
        $res     = $service->myEdit($this->param, $msg);
        if ($res === false) {
            return json(fail(Response::MEMBER_EDIT_FAIL, $msg));
        }
        return json(success());
    }

    public function collectEdit(): Json
    {
        $validate = new MemberCollectValidate();
        $result   = $validate->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }
        if ($this->param['member_id'] == 0) {
            return json(fail(Response::MEMBER_NOT_LOGIN));
        }
        $res = CollectSer::Edit($this->param);
        if ($res) {
            return json(success());
        }
        return json(fail(Response::MEMBER_COLLECT_FAIL));
    }

    public function collectIndex(): Json
    {
        $memberId = intval($this->param['member_id'] ?? 0);
        $typeId   = intval($this->param['type'] ?? 1);
        $lastTime = isset($this->param['last_time']) ? strtotime($this->param['last_time']) : 0;
        $list     = CollectSer::getList($memberId, $typeId, $lastTime);
        $ids      = array_column($list, 'info_id');
        if (empty($ids)) {
            return json(success([]));
        }
        $rows = [];
        switch ($typeId) {
            case 1: //商品
                $rows = (new GoodsSer('List', ['goods_id' => $ids]))->getData();
                $rows = array_column($rows, null, 'goods_id');
                break;
            case 2:
                // 获取店铺列表
                $shops = ShopSer::getShopList($ids, count($ids));
                // 获取店铺员工
                $shopIds = array_column($shops, 'id');
                $staffList = (new StaffSer('List', ['shop_id' => $shopIds]))->getData();
                // 组合数据
                foreach ($shops as $key => &$row) {
                    $row['staff'] = array_values(array_filter($staffList, function ($item) use ($row) {
                        return $item['shop_id'] == $row['id'];
                    }));
                    // $row['auth_tag']     = ShopSer::handleAuthTags($row['auth_tag']); //店铺认证标签
                    $row['manage_tag']   = ShopSer::getManageTag($row['id']); //店铺经营范围标签
                }
                unset($row);
                $rows = array_column($shops, null, 'id');
                break;
            case 3:
                //app\api\service\Staff\baseData\DbYsaShop().addSimple 针对员工的where：audit_status=1 and status=1 and delete=0;
                //如果有数据但此处没有返回数据，针对数据库查看一下where条件的字段的值
                $rows = (new StaffSer('List', ['staff_id' => $ids, 'member_id' => $memberId]))->getData();
                $rows = array_column($rows, null, 'id');
                break;
        }
        // 重整数据，排序+分页
        $res = [];
        if ($rows) {
            foreach ($list as $row) {
                $item = $rows[$row['info_id']] ?? [];
                if (!empty($item)) {
                    $item['collect_time'] = $row['create_time'];
                    array_push($res, $item);
                }
            }
        }
        return json(success($res));
    }

    // 拉取胎动记录
    public function GetRecordList(): Json
    {
        $memberId = intval($this->param['member_id'] ?? 0);
        $last_id  = intval($this->param['last_id'] ?? 0);
        $rows     = MemberSer::GetRecordList($memberId, $last_id);
        $data     = [];
        foreach ($rows as $row) {
            $data[] = [
                'id'          => $row['id'],
                'member_id'   => $row['member_id'],
                'num'         => $row['movement_count'],
                'run_time'    => $row['duration_sec'],
                'create_time' => $row['created_at'],
                'last_time'   => strtotime($row['created_at']),
            ];
        }
        return json(success($data));
    }
    // 记录胎动记录
    public function SaveRecord(): Json
    {
        $validate = new MemberFetalMovementRecord();
        $result   = $validate->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }
        $res = MemberSer::SaveRecord($this->param);
        if ($res) {
            return json(success());
        }
        return json(fail(Response::ADDRESS_ADD_FAIL));
    }

    // 用户分享小程序
    public function share(): Json
    {
        return json(success());
    }

    // 用户点击分享链接
    public function share(): Json
    {
        return json(success());
    }
}
