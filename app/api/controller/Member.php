<?php

namespace app\api\controller;

use app\api\service\CollectSer;
use app\api\service\Goods\GoodsSer;
use app\api\service\MemberSer;
use app\api\service\ShopSer;
use app\api\service\Staff\StaffSer;
use app\common\service\CouponService;
use think\response\Json;
use app\api\validate\MemberCollect as MemberCollectValidate;
use app\api\validate\MemberFetalMovementRecord;
use app\api\validate\MemberShare as MemberShareValidate;
use app\api\service\MemberShareService;
use app\common\enum\Response;

class Member extends Base
{
    public function my(): Json
    {
        $memberId = intval($this->param['member_id']);
        if ($memberId == 0) {
            return json(fail(Response::MEMBER_NOT_LOGIN));
        }
        $token  = $this->request->token;
        $service = new MemberSer();
        $res     = $service->info($memberId, $token);
        //获取可用优惠券数量
        $coupon = CouponService::getCouponCountByMemberId(['id' => $memberId], $token, "api");
        if ($coupon['code'] == 1) {
            $res['data']['coupon']['count'] = $coupon['data'];
        }
        return json($res);
    }

    public function edit(): Json
    {
        if ($this->param['member_id'] == 0) {
            return json(fail(Response::MEMBER_NOT_LOGIN));
        }
        $msg     = '';
        $service = new MemberSer();
        $res     = $service->myEdit($this->param, $msg);
        if ($res === false) {
            return json(fail(Response::MEMBER_EDIT_FAIL, $msg));
        }
        return json(success());
    }

    public function collectEdit(): Json
    {
        $validate = new MemberCollectValidate();
        $result   = $validate->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }
        if ($this->param['member_id'] == 0) {
            return json(fail(Response::MEMBER_NOT_LOGIN));
        }
        $res = CollectSer::Edit($this->param);
        if ($res) {
            return json(success());
        }
        return json(fail(Response::MEMBER_COLLECT_FAIL));
    }

    public function collectIndex(): Json
    {
        $memberId = intval($this->param['member_id'] ?? 0);
        $typeId   = intval($this->param['type'] ?? 1);
        $lastTime = isset($this->param['last_time']) ? strtotime($this->param['last_time']) : 0;
        $list     = CollectSer::getList($memberId, $typeId, $lastTime);
        $ids      = array_column($list, 'info_id');
        if (empty($ids)) {
            return json(success([]));
        }
        $rows = [];
        switch ($typeId) {
            case 1: //商品
                $rows = (new GoodsSer('List', ['goods_id' => $ids]))->getData();
                $rows = array_column($rows, null, 'goods_id');
                break;
            case 2:
                // 获取店铺列表
                $shops = ShopSer::getShopList($ids, count($ids));
                // 获取店铺员工
                $shopIds = array_column($shops, 'id');
                $staffList = (new StaffSer('List', ['shop_id' => $shopIds]))->getData();
                // 组合数据
                foreach ($shops as $key => &$row) {
                    $row['staff'] = array_values(array_filter($staffList, function ($item) use ($row) {
                        return $item['shop_id'] == $row['id'];
                    }));
                    // $row['auth_tag']     = ShopSer::handleAuthTags($row['auth_tag']); //店铺认证标签
                    $row['manage_tag']   = ShopSer::getManageTag($row['id']); //店铺经营范围标签
                }
                unset($row);
                $rows = array_column($shops, null, 'id');
                break;
            case 3:
                //app\api\service\Staff\baseData\DbYsaShop().addSimple 针对员工的where：audit_status=1 and status=1 and delete=0;
                //如果有数据但此处没有返回数据，针对数据库查看一下where条件的字段的值
                $rows = (new StaffSer('List', ['staff_id' => $ids, 'member_id' => $memberId]))->getData();
                $rows = array_column($rows, null, 'id');
                break;
        }
        // 重整数据，排序+分页
        $res = [];
        if ($rows) {
            foreach ($list as $row) {
                $item = $rows[$row['info_id']] ?? [];
                if (!empty($item)) {
                    $item['collect_time'] = $row['create_time'];
                    array_push($res, $item);
                }
            }
        }
        return json(success($res));
    }

    // 拉取胎动记录
    public function GetRecordList(): Json
    {
        $memberId = intval($this->param['member_id'] ?? 0);
        $last_id  = intval($this->param['last_id'] ?? 0);
        $rows     = MemberSer::GetRecordList($memberId, $last_id);
        $data     = [];
        foreach ($rows as $row) {
            $data[] = [
                'id'          => $row['id'],
                'member_id'   => $row['member_id'],
                'num'         => $row['movement_count'],
                'run_time'    => $row['duration_sec'],
                'create_time' => $row['created_at'],
                'last_time'   => strtotime($row['created_at']),
            ];
        }
        return json(success($data));
    }
    // 记录胎动记录
    public function SaveRecord(): Json
    {
        $validate = new MemberFetalMovementRecord();
        $result   = $validate->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }
        $res = MemberSer::SaveRecord($this->param);
        if ($res) {
            return json(success());
        }
        return json(fail(Response::ADDRESS_ADD_FAIL));
    }

    // 用户分享小程序
    public function MemberShare(): Json
    {
        // 参数验证
        $validate = new MemberShareValidate();
        if (!$validate->scene('create')->check($this->param)) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }

        // 验证用户登录
        $memberId = intval($this->param['member_id'] ?? 0);
        if ($memberId == 0) {
            return json(fail(Response::MEMBER_NOT_LOGIN, '请先登录'));
        }

        // 构建分享参数
        $shareParam = [
            'sharer_id' => $memberId,
            'share_type' => intval($this->param['share_type']),
            'object_id' => intval($this->param['object_id']),
            'object_name' => $this->param['object_name'] ?? '',
            'share_channel' => intval($this->param['share_channel'] ?? 6), // 默认其他渠道
            'share_path' => $this->param['share_path'] ?? '/pages/index/index',
            'share_params' => $this->param['share_params'] ?? [],
            'expire_time' => !empty($this->param['expire_time']) ? intval($this->param['expire_time']) : null,
            'longitude' => $this->param['longitude'] ?? '',
            'latitude' => $this->param['latitude'] ?? '',
            'area_id' => $this->param['area_id'] ?? '',
            'deviceid' => $this->param['deviceid'] ?? '',
            'device_model' => $this->param['device_model'] ?? '',
            'platform' => $this->param['platform'] ?? 'OTHER',
            'version' => $this->param['version'] ?? '',
        ];

        $shareService = new MemberShareService();
        $result = $shareService->createShare($shareParam);

        return json($result);
    }

    // 用户点击分享链接
    public function MemberShareClicks(): Json
    {
        // 参数验证
        $validate = new MemberShareValidate();
        if (!$validate->scene('click')->check($this->param)) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }

        // 构建点击参数
        $clickParam = [
            'share_id' => $this->param['share_id'],
            'member_id' => intval($this->param['member_id'] ?? 0), // 可以为0（未登录用户）
            'enter_path' => $this->param['enter_path'] ?? '',
            'referrer' => $this->param['referrer'] ?? '',
            'scene' => $this->param['scene'] ?? '',
            'longitude' => $this->param['longitude'] ?? '',
            'latitude' => $this->param['latitude'] ?? '',
            'area_id' => $this->param['area_id'] ?? '',
            'deviceid' => $this->param['deviceid'] ?? '',
            'device_model' => $this->param['device_model'] ?? '',
            'platform' => $this->param['platform'] ?? 'OTHER',
            'network_type' => $this->param['network_type'] ?? '',
            'version' => $this->param['version'] ?? '',
        ];

        $shareService = new MemberShareService();
        $result = $shareService->recordClick($clickParam);

        return json($result);
    }

    // 获取分享统计信息
    public function getShareStats(): Json
    {
        if (empty($this->param['share_id'])) {
            return json(fail(Response::REQUEST_PARAM_ERROR, '分享ID不能为空'));
        }

        $shareService = new MemberShareService();
        $result = $shareService->getShareStats($this->param['share_id']);

        return json($result);
    }

    // 获取用户分享列表
    public function getMyShares(): Json
    {
        $memberId = intval($this->param['member_id'] ?? 0);
        if ($memberId == 0) {
            return json(fail(Response::MEMBER_NOT_LOGIN, '请先登录'));
        }

        $page = intval($this->param['page'] ?? 1);
        $limit = intval($this->param['limit'] ?? 20);

        $shareService = new MemberShareService();
        $result = $shareService->getUserShares($memberId, $page, $limit);

        return json($result);
    }

    // 设置分享转化
    public function setShareConversion(): Json
    {
        $memberId = intval($this->param['member_id'] ?? 0);
        if ($memberId == 0) {
            return json(fail(Response::MEMBER_NOT_LOGIN, '请先登录'));
        }

        if (empty($this->param['share_id']) || empty($this->param['conversion_type'])) {
            return json(fail(Response::REQUEST_PARAM_ERROR, '缺少必要参数'));
        }

        $shareService = new MemberShareService();
        $result = $shareService->setConversion(
            $this->param['share_id'],
            $memberId,
            $this->param['conversion_type'],
            $this->param['conversion_id'] ?? null
        );

        return json($result);
    }
}
