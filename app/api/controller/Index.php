<?php

namespace app\api\controller;

use app\api\service\CategorySer;
use app\api\service\Goods\GoodsSer;
use app\api\service\ShopSer;
use app\api\service\Staff\StaffSer;
use app\common\enum\Response;
use app\common\enum\staff\Constellation;
use app\common\enum\staff\Cook;
use app\common\enum\staff\Degree;
use app\common\enum\staff\Language;
use app\common\enum\staff\Nation;
use app\common\enum\staff\Worker;
use app\common\enum\staff\Zodiac;
use app\common\service\GoodsService;
use think\facade\Cache;
use think\facade\Db;
use think\facade\Env;
use think\response\Json;

class Index extends Base
{
    public function config(): Json
    {
        // 只获取一级分类
        $categories = CategorySer::getCategoryList(0, [1]);
        $newMember = self::_newMember();

        $version = $this->param['version'] ?? '';
        $config = [
            // 关闭面试
            'interview' => true,
            'close_wishshop' => false, //true-关闭心愿单；false-开启心愿单
            'question_closed' => $version == '3.1.38' ? true : false, //true-允许跳过；false-不允许跳过
            'total_member' => $newMember['total'],
            'yesterday_member' => $newMember['yesterday'],
            'native' => changeEnumKeyValue(Nation::NationCode),
            'education' => changeEnumKeyValue(Degree::DegreeCode),
            'language' => changeEnumKeyValue(Language::LanguageCode),
            'cook' => changeEnumKeyValue(Cook::CookCode),
            'zodiac' => changeEnumKeyValue(Zodiac::ZodiacCode),
            'constellation' => changeEnumKeyValue(Constellation::ConstellationCode),
            'hot_city' => [360100, 320600, 230100, 210200, 310100, 320100, 110100, 440100, 440300],
            'activity_search' => [
                [
                    'key' => 'ppt',
                    'value' => 'https://micro-site.jiangsuyishengai.com/web/#/pages/pdf/index',
                ],
            ], // 活动搜索
            // 公司联系方式
            "contact" => [
                "phone" => "4006691776",
                "wx" => "Ysa4128855",
                "qrcode" => "https://oss.jiangsuyishengai.com/img/join_qrcode.png",
                "baby_phone" => "4006691776",
                "baby_qrcode" => "https://oss.jiangsuyishengai.com/img/join_qrcode.png",
            ],
            // 大类
            'first_category' => $categories,
            'service_object' => [
                [
                    'key' => 'adult',
                    'value' => '女性',
                    'img' => 'https://oss.jiangsuyishengai.com/img/question_women.png',
                ],
                [
                    'key' => 'baby',
                    'value' => '婴幼儿',
                    'img' => 'https://oss.jiangsuyishengai.com/img/question_baby.png',
                ],
                [
                    'key' => 'old',
                    'value' => '老人',
                    'img' => 'https://oss.jiangsuyishengai.com/img/question_old.png',
                ],
            ],  // 服务对象
        ];

        return json(success($config));
    }

    // 假的用户数量
    private function _newMember(): array
    {
        $redis = Cache::store('redis')->handler();
        $key = "ysa:newMember";
        $data = $redis->get($key);
        if ($data == false) {
            $retData = [
                'total' => 888,
                'today' => 0,
                'yesterday' => 36,
                'last_time' => time(),
            ];
            $redis->set($key, serialize($retData));
            return $retData;
        }
        $retData = unserialize($data);
        if ($retData['last_time'] + 3600 < time()) {
            $rand = rand(10, 20);
            // 如果las_time的日期时昨天的
            if (date('Ymd', $retData['last_time']) <= date('Ymd', strtotime('-1 day'))) {
                $retData['yesterday'] = $retData['today'];
                $retData['today'] = $rand;
            } else {
                $retData['today'] += $rand;
            }
            $retData['total'] += $rand;
            $retData['last_time'] = time();
            $redis->set($key, serialize($retData));
        }
        return $retData;
    }

    public function index(): Json
    {
        // 只获取一级分类
        $categories = CategorySer::getCategoryList(0, [1]);

        $workerList = Worker::getList('index');
        // 获取店铺列表
        $shops = ShopSer::getShopList([], 3);
        // 获取店铺员工
        $shopIds = array_column($shops, 'id');
        $staffList = (new StaffSer('List', ['shop_id' => $shopIds]))->getData();
        $staffListKey = array_column($staffList, null, 'id');

        // 获取分享用户
        $staffIds = array_column($staffList, 'id');
        $shareList = Db::table('shop_share_staff')
            ->where('staff_id', 'in', $staffIds)
            ->where('to_shop_id', 'in', $shopIds)
            ->where('status', 1)
            ->field('staff_id, to_shop_id')
            ->select()
            ->toArray();
        $staffArray = [];
        if ($shareList) {
            foreach ($shareList as $row) {
                $staffArray[$row['to_shop_id']][] = $row['staff_id'];
            }
        }

        // 组合数据
        foreach ($shops as $key => &$row) {
            $row['staff'] = array_values(array_filter($staffList, function ($item) use ($row) {
                return $item['shop_id'] == $row['id'];
            }));

            if (isset($staffArray[$row['id']])) {
                foreach ($staffArray[$row['id']] as $staffId) {
                    $row['staff'][] = $staffListKey[$staffId] ?? [];
                }
            }
        }
        unset($row);
        // if (isset($this->param['version']) && $this->param['version'] === '3.1.32') {
        //     $workerList = [];
        // }
        $res = [
            'categories' => $categories,
            'worker_list' => $workerList,
            'shops' => $shops,
        ];
        return json(success($res));
    }

    public function search(): Json
    {
        $keyword = trim($this->param['keyword'] ?? '');
        $api = Env::get('SEARCH_API', '');
        if (empty($api)) {
            return json(fail(Response::ERROR, '搜索接口未配置'));
        }
        $data = curlPost($api, json_encode(['keyword' => $keyword, 'page' => 1, 'page_size' => 100]), true);
        $json = json_decode($data, true);
        if (empty($json)) {
            return json(fail(Response::ERROR, '搜索接口返回数据格式错误'));
        }
        if ($json['status'] != 1) {
            return json(fail(Response::ERROR, $json['message']));
        }
        $goods = $json['data']['goods'] ?? [];
        $goodsList = [];
        if (!empty($goods)) {
            $goodsIds = array_column($goods, 'goods_id');
            $goodsList = (new GoodsSer('List', ['id' => $goodsIds]))->getData();
        }
        $staff = $json['data']['staff'] ?? [];
        $staffList = [];
        if (!empty($staff)) {
            $staffIds = array_column($staff, 'staff_id');
            $staffList = (new StaffSer('List', ['id' => $staffIds]))->getData();
        }
        $res = [
            'goods' => $goodsList,
            'staff' => $staffList,
        ];
        return json(success($res));
    }

    public function toEs()
    {
        $ids = $this->request->param('ids');
        if (empty($ids)) {
            return json(fail(Response::ERROR, '参数错误'));
        }
        $ids = explode(',', $ids);
        $url = "http://101.34.56.150:8999/api/goods/save";
        $service = new GoodsService();
        $data = $service->getListForEs(['goods_ids' => $ids]);
        $data = $data['data'] ?? [];
        if (empty($data)) {
            return json(fail(Response::ERROR, '数据为空'));
        }
        foreach ($data as $item) {
            $res = curlPost($url, json_encode($item), true);
            $json = json_decode($res, true);
            if (empty($json)) {
                return json(fail(Response::ERROR, '搜索接口返回数据格式错误'));
            }
            if ($json['status'] != 1) {
                return json(fail(Response::ERROR, $json['message']));
            }
        }
        return json(success([]));
    }
}
