<?php

declare(strict_types=1);

namespace app\api\controller;

use app\common\enum\Response;
use app\common\model\ActivityShareRecordModel;
use app\common\model\MemberModel;
use app\common\model\OpenBdidRecordModel;
use app\common\service\ActivityService;
use app\common\validate\ActivityValidate;
use think\facade\Request;
use think\response\Json;

class Activity extends Base
{

    /**
     * 活动列表
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function index()
    {
        $param    = $this->param;
        $lastSort = $this->param['last_sort'] ?? ''; //滚动分页使用
        $lastId   = $this->param['last_id'] ?? ''; //滚动分页使用
        if (empty($param['member_id'])) {
            return json(fail(Response::MEMBER_NOT_LOGIN));
        }
        $list = (new ActivityService())->getList($param, $lastSort, $lastId);
        return json(success($list));
    }

    /**
     * 获取活动城市列表（上架活动）
     * @return \think\response\Json
     */
    public function getCityList()
    {
        return json(success((new ActivityService())->getCityList()));
    }

    /**
     * 活动报名
     * @return \think\response\Json
     */
    public function signup(): Json
    {
        try {
            $param = $this->param;
            if (empty($param['member_id'])) {
                return json(fail(Response::MEMBER_NOT_LOGIN));
            }
            if (empty($param['contact_name']) && empty($param['contact_mobile']) && empty($param['contact_wechat'])) {
                // 获取用户手机号
                $memberInfo = MemberModel::where('id', $param['member_id'])->field('nickname,mobile')->findOrEmpty();
                if ($memberInfo->isEmpty()) {
                    return json(fail(Response::MEMBER_NOT_LOGIN));
                }
                $param['contact_name'] = empty($memberInfo->nickname) ? $memberInfo->mobile : $memberInfo->nickname;
                $param['contact_mobile'] = $memberInfo->mobile;
                $param['contact_wechat'] = $memberInfo->mobile;
            }
            $validate = new ActivityValidate();
            if (!$validate->scene('signup')->check($param)) {
                return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
            }
            $activity = (new ActivityService())->signup($param);
            if ($activity) {
                $config = config('api.services.Member');
                $url = $config['BaseURL'] . $config['Api']['MemberActivity'];
                $token = $this->request->token;
                $res = curlPostApiContentByUrlAndParams($url, $this->param, ['Authorization: Bearer ' . $token], false);
                if (empty($res)) {
                    return json(fail(Response::NOT_LOGIN));
                }
                if ($res['code'] != 1) {
                    return json(fail(Response::REQUEST_ERROR, $res['msg']));
                }
                return json(success(['活动报名成功']));
            }
            return json(fail());
        } catch (\Exception $e) {
            return json(fail(Response::DATA_NOT_FOUND, $e->getMessage()));
        }
    }

    /**
     * 活动详情
     * @return \think\response\Json
     */
    public function detail(): Json
    {
        $param = $this->param;
        // if (empty($param['member_id'])) {
        //     return json(fail(Response::MEMBER_NOT_LOGIN));
        // }
        $ip = Request::ip();
        $validate = new ActivityValidate();
        if (!$validate->scene('detailApi')->check($param)) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }
        $activity = (new ActivityService())->detail($param);
        ActivityShareRecordModel::addOneData($param['id'], $param['pid'], $param['member_id'], $ip, $param['deviceid'] ?? '', $this->request->token);
        return json(success($activity));
    }
}
