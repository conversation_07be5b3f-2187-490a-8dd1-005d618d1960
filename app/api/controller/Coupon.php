<?php
declare(strict_types=1);

namespace app\api\controller;

use app\common\enum\Response;
use app\common\service\CouponService;
use app\common\validate\Coupon as CouponValidate;
use think\facade\Db;
use think\response\Json;

class Coupon extends Base
{

    /**
     * 优惠券添加 （用户领取优惠券）
     * @return Json
     */
    public function createCoupon(): Json
    {
        $params = $this->param;
        $data   = [
            'template_id'  => $params['template_id'], //关联模板ID
            'member_id'    => $params['member_id'] ?? '', //用户ID
            'receive_type' => 1, //领取方式：1-用户领取 ，2-客服手工发放
        ];
        //添加参数验证器
        $validate = new CouponValidate();
        if (!$validate->scene('createCoupon')->check($data)) {
            return json(fail(Response::REQUEST_ERROR, $validate->getError()));
        }
        $token  = $this->request->token;
        $result = CouponService::createCoupon($data, $token, "api");
        if ($result['code'] != 1) {
            return json(fail(Response::REQUEST_ERROR, $result['msg']));
        }

        return json(success($result['data'] ?? []));
    }

    /**
     * 优惠券模板列表（通过用户ID获取列表（我的））
     * @return Json
     */
    public function couponList(): Json
    {
        $params = $this->param;
        $data   = [
            'page_size' => $params['page_size'] ?? 10,
            'last_time' => $params['last_time'] ?? date('Y-m-d H:i:s'),
            'member_id' => $params['member_id'] ?? '', //用户ID
            'type'      => $params['type'] ?? '0', //类型：0-全部，1-未使用（UNUSED-未使用），2-已使用（LOCKED-下单锁定，USED-已核销），3-已过期（EXPIRED-已过期，INVALID-已作废）
        ];
        //添加参数验证器
        $validate = new CouponValidate();
        if (!$validate->scene('memberId')->check($data)) {
            return json(fail(Response::REQUEST_ERROR, $validate->getError()));
        }
        $token  = $this->request->token;
        $result = CouponService::couponList($data, $token, "api");
        if ($result['code'] != 1) {
            return json(fail(Response::REQUEST_ERROR, $result['msg']));
        }
        $couponList = $result['data'];
        if (empty($couponList)) {
            return json(success($couponList ?? []));
        }
        foreach ($couponList['coupon_list'] as $key => $value) {
            if (empty($value['shop_id'])) {
                $couponList['coupon_list'][$key]['shop_name'] = '';
            } else {
                $where                         = [
                    'id'     => $value['shop_id'],
                    'status' => 1,
                ];
                $couponList['coupon_list'][$key]['shop_name'] = Db::name('shop')->where($where)->value('shop_name');
            }
        }
        return json(success($couponList ?? []));
    }

    /**
     * 优惠券列表（通过用户ID获取列表（下单））
     * @return Json
     */
    public function couponListByOrder(): Json
    {
        $params = $this->param;
        $data   = [
            'member_id' => $params['member_id'] ?? '', // 用户ID
            'shop_id'   => $params['shop_id'] ?? '',// 商品ID
            'coupon_id' => $params['coupon_id'] ?? '', // 优惠券ID
            'price'     => $params['price'] ?? '', // 商品总价格（未使用优惠）
            'goods_ids' => $params['goods_ids'] ?? '',//商品ID，英文逗号分隔，字符串类型（预留字段，暂不使用）
        ];
        //添加参数验证器
        $validate = new CouponValidate();
        if (!$validate->scene('couponListByOrder')->check($data)) {
            return json(fail(Response::REQUEST_ERROR, $validate->getError()));
        }
        $token  = $this->request->token;
        $result = CouponService::couponOrderList($data, $token, "api");
        if ($result['code'] != 1) {
            return json(fail(Response::REQUEST_ERROR, $result['msg']));
        }
        $couponList = $result['data'];
        if (empty($couponList)) {
            return json(success($couponList ?? []));
        }
        foreach ($couponList as $key => $value) {
            if (empty($value['shop_id'])) {
                $couponList[$key]['shop_name'] = '';
            } else {
                $where                         = [
                    'id'     => $value['shop_id'],
                    'status' => 1,
                ];
                $couponList[$key]['shop_name'] = Db::name('shop')->where($where)->value('shop_name');
            }
        }
        return json(success($couponList ?? []));
    }

    /**
     * 优惠券更新（状态变更）
     * @return Json
     */
    public function updateCoupon()
    {
        $params = $this->param;
        $data   = [
            'id'        => $params['id'],
            'member_id' => $params['member_id'],
            'order_id'  => $params['order_id'],
            'status'    => $params['status'] ?? '',
        ];
        //添加参数验证器
        $validate = new CouponValidate();
        if (!$validate->scene('updateCoupon')->check($params)) {
            return json(fail(Response::REQUEST_ERROR, $validate->getError()));
        }
        $token  = $this->request->token;
        $result = CouponService::updateCoupon($data, $token, "api");
        if ($result['code'] != 1) {
            return json(fail(Response::REQUEST_ERROR, $result['msg']));
        }

        return json(success($result['data'] ?? []));
    }

    /**
     * 使用优惠券（优惠券更新）
     * @return Json
     */
    public function updateCouponToUsed()
    {
        $params = $this->param;
        $data   = [
            'id'        => $params['id'],
            'member_id' => $params['member_id'],
            'order_id'  => $params['order_id'],
            'status'    => 'USED',
        ];
        //添加参数验证器
        $validate = new CouponValidate();
        if (!$validate->scene('updateCoupon')->check($params)) {
            return json(fail(Response::REQUEST_ERROR, $validate->getError()));
        }
        $token  = $this->request->token;
        $result = CouponService::updateCoupon($data, $token, "api");
        if ($result['code'] != 1) {
            return json(fail(Response::REQUEST_ERROR, $result['msg']));
        }

        return json(success($result['data'] ?? []));
    }

    /**
     * 取消使用优惠券，状态回滚（优惠券更新）
     * @return Json
     */
    public function updateCouponToUnused()
    {
        $params = $this->param;
        $data   = [
            'id'        => $params['id'],
            'member_id' => $params['member_id'],
            //'order_id'  => $params['order_id'],
            'status'    => 'UNUSED',
        ];
        //添加参数验证器
        $validate = new CouponValidate();
        if (!$validate->scene('updateCoupon')->check($params)) {
            return json(fail(Response::REQUEST_ERROR, $validate->getError()));
        }
        $token  = $this->request->token;
        $result = CouponService::updateCoupon($data, $token, "api");
        if ($result['code'] != 1) {
            return json(fail(Response::REQUEST_ERROR, $result['msg']));
        }

        return json(success($result['data'] ?? []));
    }

    /**
     * 获取指定用户可用优惠券的数量
     * @return Json
     */
    public function getCouponCountByMemberId()
    {
        $data = [
            "id" => $this->param['member_id'] ?? 0,
        ];
        //添加参数验证器
        $validate = new CouponValidate();
        if (!$validate->scene('id')->check($data)) {
            return json(fail(Response::REQUEST_ERROR, $validate->getError()));
        }
        $token  = $this->request->token;
        $result = CouponService::getCouponCountByMemberId($data, $token, "api");
        if ($result['code'] != 1) {
            return json(fail(Response::REQUEST_ERROR, $result['msg']));
        }
        return json(success([$result['data']] ?? []));
    }
}