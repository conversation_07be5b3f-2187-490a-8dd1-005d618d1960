<?php

namespace app\api\controller;

use app\api\service\CollectSer;
use app\api\service\Goods\GoodsSer;
use app\api\service\ShopSer;
use app\api\service\Staff\StaffSer;
use app\common\model\OpenBdidRecordModel;
use app\common\service\UploadService;
use think\facade\Db;
use think\facade\Request;
use think\response\Json;

class Shop extends Base
{
    public function getList(): Json
    {
        // 获取店铺列表
        $shops = ShopSer::getShopList();
        // 获取店铺员工
        $shopIds   = array_column($shops, 'id');
        $staffList = (new StaffSer('List', ['shop_id' => $shopIds]))->getData();
        $staffListKey = array_column($staffList, null, 'id');

        // 获取分享用户
        $staffIds = array_column($staffList, 'id');
        $shareList = Db::table('shop_share_staff')
            ->where('staff_id', 'in', $staffIds)
            ->where('to_shop_id', 'in', $shopIds)
            ->where('status', 1)
            ->field('staff_id, to_shop_id')
            ->select()
            ->toArray();
        $staffArray = [];
        if ($shareList) {
            foreach ($shareList as $row) {
                $staffArray[$row['to_shop_id']][] = $row['staff_id'];
            }
        }

        // 组合数据
        foreach ($shops as $key => &$row) {
            $row['staff'] = array_values(array_filter($staffList, function ($item) use ($row) {
                return $item['shop_id'] == $row['id'];
            }));
            if (isset($staffArray[$row['id']])) {
                foreach ($staffArray[$row['id']] as $staffId) {
                    $row['staff'][] = $staffListKey[$staffId] ?? [];
                }
            }
        }
        unset($row);
        return json(success($shops));
    }

    /**
     * 店铺详情
     * @return Json
     * @throws \Exception
     */
    public function detail(): Json
    {
        //店铺id
        $shopId               = intval($this->param['shop_id'] ?? 0);
        $memberId             = intval($this->param['member_id'] ?? 0);
        // 增加bdid记录
        if ($memberId > 0 && isset($this->param['bdid']) && $this->param['bdid'] != "") {
            OpenBdidRecordModel::addOneData($this->param['bdid'], $this->param['member_id'], Request::ip(), $this->param['deviceid'] ?? '');
        }
        $shop                 = ShopSer::getShopDetailByShopId($shopId);
        $shop['shop_collect'] = CollectSer::get($memberId, 2, $shopId);
        // 获取店铺商品
        $goodsList = (new GoodsSer('List', ['shop_id' => $shopId, 'limit' => 8]))->getData();
        // $goodsList = array_slice($goodsList, 0, 4);
        // 获取店铺员工
        $staffList = (new StaffSer('List', ['shop_id' => $shopId]))->getData();
        $res = [
            'shop'       => $shop,
            'goods_list' => $goodsList,
            'staff_list' => $staffList,
        ];
        return json(success($res));
    }

    public function website(): Json
    {
        $shopId = intval($this->param['shop_id'] ?? 0);
        $shop = ShopSer::getShopDetailForWebsite($shopId);
        return json(success($shop));
    }
}
