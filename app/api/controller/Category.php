<?php

namespace app\api\controller;

use app\api\service\CategorySer;
use app\api\service\Goods\GoodsSer;
use app\api\service\Shop\ShopSer;
use app\common\enum\Response;
use app\common\model\GoodsCategoryModel;
use app\common\model\MemberModel;
use app\common\model\StaffServiceModel;
use think\response\Json;

class Category extends Base
{
    public function index(): Json
    {
        $id = intval($this->param['staff_id'] ?? 0);
        if ($id > 0) {
            $res = CategorySer::getCategoryListByStaffId($id);
            return json(success($res));
        }
        $categories = CategorySer::getCategoryList();

        $goods             = [];
        $firstCategoriesId = intval($this->param['first_category_id'] ?? 0);
        if ($firstCategoriesId == 0) {
            $firstCategoriesId = $categories[0]['c_id'];
        }
        $categoryId = intval($this->param['category_id'] ?? 0);
        $memberId = $this->param['member_id'] ?? 0;
        $third_category_id = 0;
        if ($memberId > 0) {
            $service_object = MemberModel::where('id', $memberId)->field('service_object')->find()->service_object ?? '';
            $catIds =  CategorySer::getCatIdsByServiceObject($service_object, $firstCategoriesId);
            if (!empty($catIds)) {
                if ($firstCategoriesId == 0) {
                    $firstCategoriesId = $catIds[0] ?? 0;
                }
                if ($categoryId == 0) {
                    $categoryId = $catIds[1] ?? 0;
                    $third_category_id = $catIds[2] ?? 0;
                }
            }
        }
        if ($firstCategoriesId == 0) {
            $firstCategoriesId = $categories[0]['c_id'];
        }
        if ($categoryId == 0) {
            $second             = CategorySer::getCategoryList($firstCategoriesId);
            $categoryId = $second[0]['children'][0]['c_id'];
        }

        if ($firstCategoriesId > 0) {
            $second     = CategorySer::getCategoryList($firstCategoriesId);

            if ($categoryId == 0) {
                $categoryId = $second[0]['children'][0]['c_id'];
            }

            if ($categoryId > 0) {
                $three = CategorySer::getCategoryList($categoryId);
                // 获取三级下所有分类id
                $ids = array_column($three[0]['children'], 'c_id');
                if ($ids) {
                    // 获取三级下所有商品
                    $goodsCategoryQuery    = GoodsCategoryModel::where('third_category_id', 'in', $ids)->field('c_id,third_category_id')->select()->toArray();
                    $cids                  = array_column($goodsCategoryQuery, 'c_id');
                    $goodsCategoryQueryMap = array_column($goodsCategoryQuery, 'third_category_id', 'c_id');
                    $temp                  = [];
                    $rows                  = (new GoodsSer('List', ['category_id' => $cids]))->getData();
                    foreach ($rows as $v) {
                        $baseId = $goodsCategoryQueryMap[$v['category_id']] ?? 0;
                        if ($baseId > 0) {
                            $temp[$baseId][] = $v;
                        }
                    }
                    foreach ($three[0]['children'] as $children) {
                        $id   = $children['c_id'];
                        $item = [
                            'first_cid'  => $firstCategoriesId,
                            'second_cid' => $categoryId,
                            'three_cid'  => $id,
                            'three_name' => $children['name'],
                            // 'children' => isset($temp[$id]) ? array_slice($temp[$id], 0, 3) : [],
                            'children'   => isset($temp[$id]) ? $temp[$id] : [],
                        ];
                        array_push($goods, $item);
                    }
                }
            }
        }
        $res = [
            'categories' => $categories,
            'goods'      => $goods,
            'selected' => [
                'first_category_id' => $firstCategoriesId,
                'second_category_id' => $categoryId,
                'third_category_id' => $third_category_id,
            ],
        ];
        return json(success($res));
    }

    public function indexV2(): Json
    {
        $memberId = intval($this->param['member_id'] ?? 0);
        $serviceObject = trim($this->param['service_object'] ?? 'adult');
        if ($memberId > 0) {
            if (empty($serviceObject)) {
                $serviceObject = MemberModel::where('id', $memberId)->field('service_object')->value('service_object');
            }
        }
        $categories = CategorySer::getCategoryList(0, [], $serviceObject);
        $staffId = intval($this->param['staff_id'] ?? 0);
        if ($staffId > 0) {
            $serviceIds = StaffServiceModel::where('staff_id', $staffId)->field('service_id')->column('service_id');
            $newCategory = CategorySer::filterCategories($categories, $serviceIds);
        } else {
            $newCategory = $categories;
        }
        $firstCategoriesId = intval($this->param['first_category_id'] ?? 0);
        $secondCategoryId = intval($this->param['second_category_id'] ?? 0);
        $thirdCategoryId = intval($this->param['third_category_id'] ?? 0);
        if ($firstCategoriesId == 0) {
            $firstCategoriesId = $newCategory[0]['c_id'];
        }
        // $memberId = intval($this->param['member_id'] ?? 0);
        // if ($memberId > 0) {
        //     $serviceObject = trim($this->param['service_object'] ?? '');
        //     if (empty($serviceObject)) {
        //         $serviceObject = MemberModel::where('id', $memberId)->field('service_object')->value('service_object');
        //     }
        //     $catIds =  CategorySer::getCatIdsByServiceObject($serviceObject, $firstCategoriesId);
        //     if (!empty($catIds)) {
        //         if ($secondCategoryId == 0) {
        //             $secondCategoryId = $catIds[1] ?? $secondCategoryId;
        //         }
        //         if ($thirdCategoryId == 0) {
        //             $thirdCategoryId = $catIds[2] ?? $thirdCategoryId;
        //         }
        //     }
        // }
        // 再次处理分类
        if ($secondCategoryId == 0) {
            $second = CategorySer::getCategoryList($firstCategoriesId, [], $serviceObject);
            $secondCategoryId = $second[0]['children'][0]['c_id'];
        }
        // 取三级分类
        $third = CategorySer::getCategoryList($secondCategoryId, [], $serviceObject);
        if (empty($third)) {
            return json(['code' => 0, 'msg' => '分类不存在']);
        }
        $thirdCids = array_column($third[0]['children'], 'c_id');
        if ($thirdCategoryId == 0) {
            $thirdCategoryId = $third[0]['children'][0]['c_id'];
        }
        // 获取cid
        $cid = [];
        if ($thirdCategoryId > 0) {
            $getCids = GoodsCategoryModel::where('third_category_id', $thirdCategoryId)->field('c_id')->select()->toArray();
            $cid = array_column($getCids, 'c_id');
        }
        // 获取三公里的门店id
        // $lastDistance: 上一页最后一条记录的距离
        $lastDistance = null;
        if (isset($this->param['last_distance']) && $this->param['last_distance'] > 0) {
            $lastDistance = $this->param['last_distance'];
        }
        // $lastGoodsSort: 上一页最后一条记录的商品排序值
        $lastGoodsSort = null;
        if (isset($this->param['last_goods_sort']) && $this->param['last_goods_sort'] > 0) {
            $lastGoodsSort = $this->param['last_goods_sort'];
        }
        // $lastId: 上一页最后一条记录的商品ID（确保唯一性）
        $lastId = null;
        if (isset($this->param['last_id']) && $this->param['last_id'] > 0) {
            $lastId = $this->param['last_id'];
        }
        $goodsList = [];
        if (!empty($cid)) {
            $goods = CategorySer::getCategoryGoodsForShop($cid, 20000, 10, $lastDistance, $lastGoodsSort, $lastId);
            if (!empty($goods['list'])) {
                $ids = array_column($goods['list'], 'goods_id');
                $goodsList  = (new GoodsSer('List', ['id' => $ids]))->getData();
            }
            // 获取推荐商品
            $recommendedGoods = CategorySer::getRecommendedGoods($cid, 5, 0);
            if (!empty($recommendedGoods)) {
                $ids = array_column($recommendedGoods, 'goods_id');
                $recommendedGoodsList = (new GoodsSer('List', ['id' => $ids]))->getData();
            }
        }
        $res = [
            'categories' => $newCategory,
            'goods' =>   $goodsList,
            'recommended_goods' => $recommendedGoodsList ?? [],
            'next_cursor' => $goods['next_cursor'] ?? [],
            'selected' => [
                'first_category_id' => $firstCategoriesId,
                'second_category_id' => $secondCategoryId,
                'third_category_id' => $thirdCategoryId,
            ],
        ];
        return json(success($res));
    }

    /**
     * 通过三级分类查询对应门店列表
     * @return Json
     * @throws \Exception
     */
    public function shopList(): Json
    {
        $categories         = CategorySer::getCategoryList();
        $first_category_id  = intval($this->param['first_category_id'] ?? 0);
        $second_category_id = intval($this->param['second_category_id'] ?? 0);
        $third_category_id = 0;

        $memberId = $this->param['member_id'] ?? 0;
        if ($memberId > 0) {
            $service_object = MemberModel::where('id', $memberId)->field('service_object')->find()->service_object ?? '';
            $catIds =  CategorySer::getCatIdsByServiceObject($service_object, $first_category_id);
            if (!empty($catIds)) {
                if ($first_category_id == 0) {
                    $first_category_id = $catIds[0] ?? 0;
                }
                if ($second_category_id == 0) {
                    $second_category_id = $catIds[1] ?? 0;
                    $third_category_id = $catIds[2] ?? 0;
                }
            }
        }
        if ($first_category_id == 0) {
            $first_category_id = $categories[0]['c_id'];
        }
        if ($second_category_id == 0) {
            $second             = CategorySer::getCategoryList($first_category_id);
            $second_category_id = $second[0]['children'][0]['c_id'];
        }

        $shops = [];
        $three = CategorySer::getCategoryList($second_category_id);
        // 获取三级下所有分类id
        $ids = array_column($three[0]['children'], 'c_id');
        if ($ids) {
            // 获取三级下所有店铺
            $goodsCategoryQuery    = GoodsCategoryModel::where('third_category_id', 'in', $ids)->field('c_id,third_category_id')->select()->toArray();
            $cids                  = array_column($goodsCategoryQuery, 'third_category_id');
            $goodsCategoryQueryMap = array_column($goodsCategoryQuery, 'third_category_id', 'c_id');
            $temp                  = [];
            $rows                  = (new ShopSer('List', ['category_id' => $cids, 'limit' => 8]))->getData();
            foreach ($rows as $v) {
                $baseId = 0;
                if (!empty($v['goods_list'])) {
                    $baseId = $goodsCategoryQueryMap[$v['goods_list'][0]['category_id']] ?? 0;
                }
                if ($baseId > 0) {
                    $temp[$baseId][] = $v;
                }
            }
            foreach ($three[0]['children'] as $children) {
                $id   = $children['c_id'];
                $item = [
                    'first_cid'  => $first_category_id,
                    'second_cid' => $second_category_id,
                    'three_cid'  => $id,
                    'three_name' => $children['name'],
                    'children'   => isset($temp[$id]) ? array_slice($temp[$id], 0, 8) : [],
                ];
                array_push($shops, $item);
            }
        }
        $result = [
            'categories' => $categories,
            'shops'      => $shops,
            'selected' => [
                'first_category_id' => $first_category_id,
                'second_category_id' => $second_category_id,
                'third_category_id' => $third_category_id,
            ],
        ];
        return json(success($result));
    }

    /**
     * 通过三级分类查询对应门店列表-查看全部
     * @return Json
     * @throws \Exception
     */
    public function getThirdCategoryIdList(): Json
    {
        $third_category_id = intval($this->param['third_category_id'] ?? 0);
        if (empty($third_category_id)) {
            return json(fail(Response::REQUEST_PARAM_ERROR));
        }
        $shops = (new ShopSer('List', ['category_id' => $third_category_id]))->getData();
        return json(success($shops));
    }
}
