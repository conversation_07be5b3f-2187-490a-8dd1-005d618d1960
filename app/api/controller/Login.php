<?php

namespace app\api\controller;

use app\api\service\LoginSer;
use app\common\service\LoginService;
use think\response\Json;

class Login extends Base
{
    public function login(): Json
    {
        $member_id = $this->param['login_id'];
        $service = new LoginSer();
        return json($service->setLogin($member_id));
    }

    public function miniappLogin(): J<PERSON>
    {
        $this->param['token'] = $this->request->token;
        $service = new LoginSer();
        return json($service->miniappLogin($this->param));
    }

    public function logout(): Json
    {
        $service = new LoginService();
        $service->loginOut('member', $this->request->memberAuth['id'], $this->request->token);
        return json(success());
    }
}
