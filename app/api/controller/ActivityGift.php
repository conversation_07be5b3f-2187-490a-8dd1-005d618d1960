<?php

namespace app\api\controller;

use app\common\enum\Response;
use app\common\model\ActivityGiftAddressModel;
use app\common\model\MemberModel;
use think\response\Json;

class ActivityGift extends Base
{
    //供应商送礼品活动报名
    public function signup(): Json
    {
        try {
            $param = $this->param;
            $memberId = intval($param['member_id'] ?? 0);
            if (empty($memberId)) {
                return json(fail(Response::MEMBER_NOT_LOGIN));
            }
            // 判断用户注册时间
            $member = MemberModel::where('id', $memberId)->field('id,mobile,register_time')->findOrEmpty();
            if ($member->isEmpty()) {
                return json(fail(Response::MEMBER_NOT_EXIST));
            }
            // 注册时间是大于24小时，则不能报名
            if (time() - $member->register_time > 24 * 60 * 60) {
                return json(fail(Response::ACTIVITY_TIMEOUT));
            }
            if (empty($param['name']) || empty($param['phone']) || empty($param['address'])) {
                return json(fail(Response::REQUEST_PARAM_ERROR));
            }
            // 正则验证手机号
            if (!preg_match('/^1[3456789]\d{9}$/', $param['phone'])) {
                return json(fail(Response::REQUEST_PARAM_ERROR));
            }
            // 判断用户是否已经报名
            $getByMemberId = ActivityGiftAddressModel::getGiftAddressByMemberId($memberId);
            if ($getByMemberId->isEmpty()) {
                // 判断手机号是否已经报名
                $getByPhone = ActivityGiftAddressModel::getGiftAddressByPhone($param['phone']);
                if ($getByPhone->isEmpty()) {
                    $data = [
                        'member_id' => $memberId,
                        'name' => $param['name'] ?? '',
                        'phone' => $param['phone'] ?? '',
                        'province_id' => intval($param['province_id'] ?? 0),
                        'city_id' => intval($param['city_id'] ?? 0),
                        'area_id' => intval($param['area_id'] ?? 0),
                        'address' => $param['address'] ?? '',
                        'longitude' => '',
                        'latitude' => '',
                        'create_time' => time(),
                    ];
                    $insertId = ActivityGiftAddressModel::insertGetId($data);
                    return json(success([], "报名成功"));
                }
            }
            return json(fail(Response::ACTIVITY_REPEAT_SIGN_UP));
        } catch (\Exception $e) {
            return json(fail(Response::DATA_NOT_FOUND, $e->getMessage()));
        }
    }
}
