<?php

namespace app\api\controller;

use app\api\service\Carts\CartsDirectorSer;
use app\api\service\Order\DoneOrderSer;
use app\api\service\Order\OrderSer;
use app\common\enum\Response;
use app\common\validate\OrderRefundApplyValidate;
use think\response\Json;
use app\api\validate\CreateOrder as CreateOrderValidate;
use app\api\validate\CreateTrainingOrder as CreateTrainingOrderValidate;
use app\api\validate\CancelOrder as CancelOrderValidate;
use app\api\validate\ChangeStaffForOrder as ChangeStaffForOrderValidate;
use app\common\service\VerificationOrderService;

/**
 * 订单状态
 * 1. 创建未支付(pay_status=0 | status=active)
 * 2. 已支付未确认(pay_status=1,2,3 | confirm=0 | status=active)
 * 3. 已支付已确认待服务(pay_status=1,2,3 | confirm=1 | status=active｜ start_service_time=NULL )
 * 4. 已支付已确认已服务(pay_status=1,2,3 | confirm=1 | status=active｜ start_service_time!=NULL )
 * 5. 已取消,只有未支付可以取消(pay_status=0 | status=dead)
 * 6. 已完成(pay_status=1,2,3 | confirm=1 | status=finish)
 * 7. 是否需要发货,根据订单类型判断 （promotion_type = ? | is_delivery=1)
 * 8. 未发货(pay_status=1,2,3 | confirm=1 | status=active | ship_status=0)
 * 9. 已发货(pay_status=1,2,3 | confirm=1 | status=active | ship_status=1,2,3)
 * 10. 已收货(pay_status=1,2,3 | confirm=1 | status=finish | ship_status=1,2,3)
 * 11. 已完成待支付尾款(pay_status=3 | confirm=1 | status=finish ) end_service_time!=NULL 服务结束
 */
class Order extends Base
{
    public function index(): Json
    {
        // 选择项 全部 待开始 服务中 已完成
        $option   = [
            'all'          => '全部',
            'wait_pay'     => '待支付',
            'wait_confirm' => '待确认',
            'wait_service' => '待服务',
            'service'      => '服务中',
            'finish'       => '已完成',
        ];
        $selected = $this->param['selected'] ?? 'all';
        $lasttime = $this->param['lasttime'] ?? '';
        $list     = OrderSer::orderList($this->param['member_id'], $selected, $lasttime);
        return json(success(['options' => $option, 'list' => $list]));
    }

    public function detail(): Json
    {
        $orderId = $this->param['order_id'] ?? '';
        if (empty($orderId)) {
            return json(fail(Response::REQUEST_PARAM_ERROR, '订单id不能为空'));
        }
        $ret = OrderSer::orderDetail($this->param['member_id'], $orderId);
        // 获取核销码
        $ver = (new VerificationOrderService())->getVerificationCodeByOrder($orderId);
        if (!empty($ver)) {
            $ret['verification'] = $ver;
        }
        return json(success($ret));
    }

    //创建订单
    public function create(): Json
    {
        if ($this->param['cartType'] == CartsDirectorSer::CART_MODE_TRAINING) {
            $validate = new CreateTrainingOrderValidate();
        } else {
            $validate = new CreateOrderValidate();
        }
        $result   = $validate->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }
        $token = $this->request->token;
        $this->param['token'] = $token; // 用于请求服务
        $worker = DoneOrderSer::worker($this->param);
        if (is_numeric($worker)) {
            return json(fail($worker));
        }

        //返回订单详情信息
        $ret = OrderSer::orderDetail($this->param['member_id'], $worker->orderId());
        return json(success($ret));
    }

    /**
     * 取消订单
     * 未支付订单可以取消
     */
    public function cancel(): Json
    {
        $validate = new CancelOrderValidate();
        $result   = $validate->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }
        $eMsg = '';
        $this->param['token'] = $this->request->token;
        $ret  = OrderSer::orderCancel($this->param, $eMsg);
        if ($ret) {
            return json(success());
        }
        return json(fail(Response::ORDER_CANCEL_FAIL, $eMsg));
    }

    /**
     * 面试不满意，可以换人
     */
    public function changeStaff(): Json
    {
        $validate = new ChangeStaffForOrderValidate();
        $result   = $validate->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }
        $eMsg = '';
        $ret  = OrderSer::changeStaffByOrder($this->param, $eMsg);
        if ($ret) {
            return json(success());
        }
        return json(fail(Response::ORDER_CANCEL_FAIL, $eMsg));
    }

    public function confirm(): Json
    {
        $eMsg = '';
        $ret  = OrderSer::orderConfirm($this->param, $eMsg);
        if ($ret) {
            return json(success());
        }
        return json(fail(Response::ORDER_CONFIRM_FAIL, $eMsg));
    }

    public function contractConfirm(): Json
    {
        $eMsg = '';
        $ret  = OrderSer::orderContractConfirm($this->param, $eMsg);
        if ($ret) {
            return json(success());
        }
        return json(fail(Response::ORDER_CONFIRM_FAIL, $eMsg));
    }

    /**
     * 服务时间确定
     * @return Json
     */
    public function serviceTimeConfirm(): Json
    {
        $eMsg = '';
        $ret  = OrderSer::orderServiceTimeConfirm($this->param, $eMsg);
        if ($ret) {
            return json(success());
        }
        return json(fail(Response::ORDER_CONFIRM_FAIL, $eMsg));
    }

    /**
     * 未支付尾款订单数量
     * @return Json
     */
    public function unpayFinalCount(): Json
    {
        $eMsg = '';
        $ret  = OrderSer::unpayFinalCount($this->param, $eMsg);
        return json(success(['count' => $ret]));
    }


    /**
     * 用户订单申请退款
     * @return Json
     */
    public function orderRefundApply(): Json
    {
        $validate = new OrderRefundApplyValidate();
        $result   = $validate->scene('orderRefundApply')->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }
        $eMsg = '';
        $ret  = OrderSer::orderRefundApply($this->param, $eMsg);
        if ($ret) {
            return json(success());
        }
        return json(fail(Response::ORDER_REFUND_APPLY_FAIL, $eMsg));
    }

    /**
     * 用户订单申请退款详情
     * @return Json
     */
    public function orderRefundApplyDetail(): Json
    {
        $validate                = new OrderRefundApplyValidate();
        $this->param['order_id'] = $this->param['id'];
        //dump($this->param);
        $result = $validate->scene('orderRefundApplyDetail')->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }
        $eMsg = '';
        $ret  = OrderSer::orderRefundApplyDetail($this->param, $eMsg);
        return json(success($ret));
    }
}
