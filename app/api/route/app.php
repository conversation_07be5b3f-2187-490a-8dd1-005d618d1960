<?php
/**
 * #############【小程序端】#############
 */

use think\facade\Route;

Route::get('test', 'Test/index');
// auth
Route::middleware(\app\api\middleware\Auth::class);

Route::get('config', 'Index/config');
// 问卷调查
Route::get('survey/question', 'survey/index');
Route::post('survey/question', 'survey/save');
// 区域列表
Route::get('area', 'Area/getAreaList');
// 假登录
Route::post('login', 'Login/login');
// 退出登录
Route::post('logout', 'Login/logout');
// 微信登录
Route::post('wx/login', 'Login/miniappLogin');
// 搜索
Route::get('search', 'Index/search');
Route::post('toEs', 'Index/toEs');
// 首页
Route::get('index', 'Index/index');
Route::get('shop/list', 'Shop/getList');
// 门店详情
Route::get('shop/detail', 'Shop/detail');
// 获取分类列表
Route::get('category/index', 'Category/index');
Route::get('category/index/v2', 'Category/indexV2');
Route::get('category/shopList', 'Category/shopList');
//查看
Route::get('category/getThirdCategoryIdList', 'Category/getThirdCategoryIdList');
// 获取分类列表（店铺下面显示对应的商品列表）
Route::get('category/shopList', 'Category/shopList');
// 商品详情
Route::get('goods/detail', 'Goods/detail');
// 服务团队
Route::get('staff/team', 'Staff/index');
// 服务人员详情
Route::get('staff/detail', 'Staff/detail');
// 获取工作日历
Route::get('staff/calendar', 'staff/GetWorkCalendar');
// 我的
Route::get('my', 'Member/my');
// 编辑我的信息
Route::put('my/edit', 'Member/edit');
// 添加地址
Route::post('member/address/add', 'MemberAddress/add');
// 修改地址
Route::put('member/address/edit', 'MemberAddress/edit');
// 删除地址
Route::delete('member/address/delete', 'MemberAddress/delete');
// 地址列表
Route::get('member/address/index', 'MemberAddress/index');
// 操作收藏
Route::post('member/collect', 'Member/collectEdit');
// 我的收藏列表
Route::get('member/collect', 'Member/collectIndex');
//标签列表
Route::get('tagType', 'TagType/index');
//标签通过type_id获取对应下面的标签值列表
Route::get('tag', 'Tag/index');
//订单评论（订单评论=商品评论+员工评论；员工=从业人员）
//通过ID获取评论详情
Route::get('orderComment/:id', 'OrderComment/getDetail');
//添加
Route::post('orderComment', 'OrderComment/create');
//修改
Route::put('orderComment', 'OrderComment/update');
//列表
Route::get('orderComment', 'OrderComment/index');
//商品评论列表
Route::get('goodsComment/:id', 'GoodsComment/index');
//从业人员评价列表
Route::get('staffComment/:id', 'StaffComment/index');
// 购物车
Route::get('cart', 'Cart/index');
// 获取有效服务人员
Route::get('cart/staff', 'Cart/getValidStaff');
// 获取有效服务时间
Route::get('cart/valid_service_time', 'Cart/getValidServiceTime');
// 下单
Route::post('order', 'Order/create');
//支付页
Route::post('pay', 'Pay/pay');
//微信支付
Route::post('pay/wechat', 'Pay/wechatPay');
//支付回调
Route::post('notify/weChatPay', 'PayNotify/wechatPay');
// 假的回调，测试的时候可以用
Route::get('notify/weChatPayTest', 'PayNotify/weChatPayTest');
// 订单列表
Route::get('order', 'Order/index');
// 订单详情
Route::get('order/detail', 'Order/detail');
// 取消订单
Route::put('order/cancel', 'Order/cancel');
// 获取面试列表
Route::get('interview/list', 'Interview/index');
// 获取面试时间
Route::get('interview', 'Interview/getStaffInterviewTime');
// 开始面试
Route::put('interview/start', 'Interview/start');
// 完成面试
Route::put('interview/finish', 'Interview/finish');
// 用户更换面试时间
Route::put('interview/change', 'Interview/change');
//面试房间踢出成员
Route::get('interview/kickUserOut', 'Interview/kickUserOut');
// 订单换人
Route::put('order/change_staff', 'Order/changeStaff');
// 确认订单, 待服务
Route::put('order/confirm', 'Order/confirm');
// 合同签字确认
Route::post('order/contract_confirm', 'Order/contractConfirm');
// 服务时间确定
Route::post('order/serviceTimeConfirm', 'Order/serviceTimeConfirm');
//未支付尾款订单数量
Route::get('order/unpayFinalCount', 'Order/unpayFinalCount');
// 用户订单申请退款
Route::post('refundApply', 'Order/orderRefundApply');
// 用户订单申请退款详情
Route::get('refundApply/:id', 'Order/orderRefundApplyDetail');
// 微信推送
// 获取消息模板列表
Route::get('wechatMessage/templateList', 'WechatMessage/getMessageTemplateList');
// Tencent 短信
Route::get('tencentSms/test', 'TencentSms/test');
// Tencent 视频面试test
Route::get('interview/test', 'Interview/test');
// 门店微网站
Route::get('shop/website/:shop_id', 'Shop/website');
// 声明
Route::get('html/agreement', 'Html/agreement');
Route::get('html/privacy', 'Html/privacy');
Route::get('html/service', 'Html/service');
// 用户行为埋点
Route::post('user/behavior', 'UserBehavior/create');
// eqxiu 订阅数据, 接收数据推送
Route::post('eqxiu/subscribe', 'Eqxiu/subscribe');
// 活动列表
Route::get('activity', 'Activity/index');
// 活动详情
Route::get('activity/:id', 'Activity/detail');
// 获取活动城市列表
Route::get('activityCity', 'Activity/getCityList');
// 活动报名
Route::post('activity/signup', 'Activity/signup');
// 首页banner图列表
Route::get('banner/:type', 'Banner/index');
// 适配旧版本，小程序审核通过后删除
Route::get('banner', 'Banner/index');

// 供应商送礼品活动报名提交
Route::post('activity-gift/signup', 'ActivityGift/signup');

// 胎动记录
Route::get('babyCount', 'Member/GetRecordList');
Route::post('babyCount', 'Member/SaveRecord');
// 胎动记录end

// 课程中心顶部导航
Route::get('college/index/topMenu', 'College/getIndexTopMenu');
// 课程中心首页列表
Route::get('college/course/index', 'College/getCourseIndex');
// 宝妈智库列表
Route::get('college/course/articleList/:id', 'College/getArticleList');
// 技能培训列表
Route::get('college/course/courseList', 'College/getCourseList');
// 好课试听列表
Route::get('college/course/freeCourseList', 'College/getFreeCourseList');
// 热门证书列表
Route::get('college/hotCertificateList', 'College/getHotCertificateList');
// 热门好课列表
Route::get('college/hotCourseList', 'College/getHotCourseList');
// 就业故事列表
Route::get('college/storyList', 'College/getStoryList');
// 宝妈智库-文章详情页
Route::get('college/article/:id', 'College/getCourseById');
// 文章&视频详情页，阅读数更新
Route::put('college/updateCourseViewCount/:id', 'College/updateCourseViewCount');
// 课程详情页
Route::get('college/course/:id', 'College/getCourseById');
// 文章章节列表(所有的)
Route::get('college/courseSection/:id', 'College/getCourseSectionByCourseId');
// 通过章节ID获取详情
Route::get('college/courseSection/detail/:id', 'College/getCourseSectionById');
// 添加课程学习记录
Route::post('college/memberCourseRecord', 'College/createMemberCourseRecord');
// 证书详情页
Route::get('college/certificateCourse/:id', 'College/getCertificateCourseById');
// 专家详情页
Route::get('college/expertCourse/:id', 'College/getExpertCourseById');
// 专家分类列表（全科专家团）
Route::get('college/expertCategoryList', 'College/getExpertCategoryList');
// 专家列表（全科专家团分类列表）
Route::get('college/expertCategoryTreeList', 'College/getTreeExpertList');
// 专家列表（热门专家）
Route::get('college/hotExpertList', 'College/getHotExpertList');
// 视频时长回调
Route::post('college/mediaDuration', 'College/mediaDuration');
// 返回播放（视频）权限
Route::post('college/playVerify', 'College/getPlayVerify');

// 许愿池
Route::get('wishPool', 'WishPool/index');
Route::get('wishPool/my', 'WishPool/my');
Route::post('wishPool', 'WishPool/create');
// 许愿池 end

// 合同
//获取预览合同
Route::get('contract/preview', 'Contract/preview');
// 客户端签字
Route::post('contract/sign/:order_id', 'Contract/sign');
// 签约回调用
Route::post('contract/signNotify', 'Contract/signNotify');
// 查看合同详情
Route::get('contract/detail', 'Contract/detail');
// 合同end


//查询支付状态
Route::get('pay/status', 'Pay/paymentStatus');

// 积分
// 获取积分签到页面信息
Route::get('member/integral', 'MemberIntegral/getIntegral');
// 积分签到
Route::post('member/integral/sign', 'MemberIntegral/integralSign');
// 获取积分任务列表
Route::get('member/integral/task', 'MemberIntegral/getIntegralTask');
// 获取积分兑换列表
Route::get('member/integral/exchange', 'MemberIntegral/getIntegralExchange');
// 获取积分获取明细列表
Route::get('member/integral/getRecord', 'MemberIntegral/getIntegralGetRecord');
// 获取积分消费明细列表
Route::get('member/integral/useRecord', 'MemberIntegral/getIntegralUseRecord');
// 兑换好礼
Route::post('member/integral/exchange', 'MemberIntegral/integralExchange');
// 积分end

// 抽奖活动
// 根据期号获取抽奖活动页面
Route::get('lottery/wap', 'Lottery/getLotteryWap');
// 点击抽奖
Route::post('lottery/prize', 'Lottery/doLottery');
// 增加抽奖次数
Route::post('lottery/addTimes', 'Lottery/addTimes');
// 获取抽奖次数列表
Route::get('lottery/times', 'Lottery/getTimes');
// 领取奖品
Route::post('lottery/receivePrize', 'Lottery/receivePrize');
// 获取中奖记录
Route::get('lottery/prizeRecord', 'Lottery/getPrizeRecord');
// 抽奖活动 end

// 获取健康检查报告
Route::get('member/health/reports', 'HealthCheck/getReports');
// 获取健康检查报告 end

// 优惠券添加 （用户领取优惠券）
Route::post('coupon', 'Coupon/createCoupon');
// 优惠券模板列表（通过用户ID获取列表（我的））
Route::get('coupon', 'Coupon/couponList');
// 优惠券模板列表（通过用户ID获取列表（下单））
Route::get('coupon/order', 'Coupon/couponListByOrder');
// 使用优惠券（优惠券更新）
Route::put('coupon/used/:id', 'Coupon/updateCouponToUsed');
// 取消使用优惠券，状态回滚（优惠券更新）
Route::put('coupon/cancel/:id', 'Coupon/updateCouponToUnused');
// 获取指定用户可用优惠券的数量
Route::get('coupon/count', 'Coupon/getCouponCountByMemberId');


