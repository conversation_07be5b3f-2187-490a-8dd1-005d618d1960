<?php
/**
 * 会员分享相关路由配置
 */

use think\facade\Route;

// 分享相关接口
Route::group('member', function () {
    
    // 创建分享记录
    Route::post('share/create', 'Member/MemberShare')->name('memberShareCreate');
    
    // 记录分享点击
    Route::post('share/click', 'Member/MemberShareClicks')->name('memberShareClick');
    
    // 获取分享统计信息
    Route::get('share/stats', 'Member/getShareStats')->name('memberShareStats');
    
    // 获取我的分享列表
    Route::get('share/my', 'Member/getMyShares')->name('memberMyShares');
    
    // 设置分享转化
    Route::post('share/conversion', 'Member/setShareConversion')->name('memberShareConversion');
    
})->middleware(['Auth']);

/*
API 使用示例：

1. 创建分享记录
POST /api/member/share/create
{
    "member_id": 123,
    "share_type": 2,           // 1-文章 2-门店 3-商品 4-活动 5-其他
    "object_id": 456,          // 被分享对象的ID
    "object_name": "测试门店",   // 被分享对象的名称
    "share_channel": 1,        // 1-微信好友 2-朋友圈 3-QQ 4-微博 5-二维码 6-其他
    "share_path": "/pages/shop/detail",
    "share_params": {          // 额外参数
        "shop_id": 456
    },
    "expire_time": 1735689600, // 过期时间戳，可选
    "longitude": 121.31370859286623,
    "latitude": 31.208279632847784,
    "area_id": "310112",
    "deviceid": "17483616910589908390",
    "device_model": "iPhone 12",
    "platform": "iOS",
    "version": "3.1.37"
}

2. 记录分享点击
POST /api/member/share/click
{
    "share_id": "abc123def456...",  // 32位分享ID
    "member_id": 789,               // 点击用户ID，可选（未登录用户为空）
    "enter_path": "/pages/shop/detail",
    "referrer": "https://example.com",
    "scene": "1001",                // 微信场景值
    "longitude": 121.31370859286623,
    "latitude": 31.208279632847784,
    "area_id": "310112",
    "deviceid": "17483616910589908390",
    "device_model": "iPhone 12",
    "platform": "iOS",
    "network_type": "WiFi",
    "version": "3.1.37"
}

3. 获取分享统计
GET /api/member/share/stats?share_id=abc123def456...

4. 获取我的分享列表
GET /api/member/share/my?member_id=123&page=1&limit=20

5. 设置分享转化
POST /api/member/share/conversion
{
    "member_id": 123,
    "share_id": "abc123def456...",
    "conversion_type": "order",     // order-下单 register-注册 contact-留资 collect-收藏 view-浏览
    "conversion_id": 789            // 转化关联的业务ID，可选
}

响应格式：
{
    "time_taken": "0.12345678",
    "code": 1,
    "msg": "操作成功",
    "data": {
        // 具体数据
    }
}
*/
