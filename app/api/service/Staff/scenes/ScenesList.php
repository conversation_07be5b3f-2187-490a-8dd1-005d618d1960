<?php

namespace app\api\service\Staff\scenes;

use app\api\service\Staff\StaffSer;
use app\common\model\MemberCollectModel;
use app\common\model\ServiceZoneModel;
use app\common\model\ShopShareStaffModel;
use app\common\model\StaffModel;
use ServiceZone;
use think\facade\Db;

class ScenesList extends ScenesBase implements InterfaceScenes
{
    public function name(): array
    {
        return [
            'name' => 'List',
            'type' => 'list'
        ];
    }

    public function cacheConf(): array
    {
        return [
            'switch'  => false,
            'timeout' => 60, //秒
        ];
    }

    /**
     * @return array
     * 该场景下要展示的字段
     */
    public function attrsName(): array
    {
        return [
            'id'            => 'id', // 员工id
            'shop_id'       => 'shop_id',
            'avatar'        => 'avatar', // 员工头像
            'level_id'      => 'level', // 星级 => 双钻
            'level_tags'    => 'level_tags', // 级别标签 => 优质月嫂
            'real_name'     => 'name', // 员工姓名
            'sys_tags'      => 'sys_tags', // 管理系统甄选标签
            'age'           => 'age', // 年龄
            'nation'        => 'nation', //民族
            'education'     => 'education', // 学历
            'native_city'   => 'native', // 出生城市
            'constellation' => 'constellation', // 星座
            'zodiac'        => 'zodiac', // 属相
            'height'        => 'height', // 身高
            'skill_total'   => 'skill_total', // 证书数量
            'tags'          => 'tags', // 标签
            'service_rate'  => 'service_rate', // 服务评分好评率
            'work_year'     => 'work_year', // 从业年限
            'service_times' => 'service_times', // 服务次数
            'is_collect'    => 'is_collect', // 是否收藏
        ];
    }

    /**
     * @return array
     * 数据库查询字段
     */
    public function simpleColumns(): array
    {
        return [
            'id',
            'shop_id',
            'avatar',
            'level_id',
            'real_name',
            'birthday',
            'nation',
            'education',
            'native_city',
            'constellation', //星座
            'zodiac', //生肖
            'height',
            'start_work_year',
            'skill_total'
        ];
    }

    public function loadBaseData($param = [], $cnfParam = []): bool
    {
        $filterStaffId = $param['filter_staff_id'] ?? [];
        $refStaffId    = $param['ref_staff_id'] ?? [];
        $staffId       = [];
        if (is_array($filterStaffId) && is_array($refStaffId)) {
            $staffId = array_merge($filterStaffId, $refStaffId);
        }
        if (empty($staffId)) {
            return false;
        }

        $this->baseData->addSimpleCache(['staff_id' => $staffId]);
        $this->baseData->addAvatar();
        $this->baseData->addTagsCache();
        $this->baseData->addCollect(intval(StaffSer::queryParam('member_id') ?? 0)); //是否收藏
        return true;
    }

    public function filterStaffId($staffId = [])
    {
        if (empty($staffId)) {
            $staffId = $this->filterStaffsIdByCondition();
        }
        // todo 根据经纬度获取可销售员工
        $is_sell_staff = [];
        if (!empty($is_sell_staff)) {
            // 获取交集
            return array_intersect(array_keys($is_sell_staff), $staffId);
        }
        return $staffId;
    }

    private function filterStaffsIdByCondition()
    {
        $query = StaffModel::alias('a')->field('a.id')->distinct('a.id');

        // 搜索关键词
        if (StaffSer::queryParam('id')) {
            $query->where('a.id', 'in', StaffSer::queryParam('id'));
        }

        $not_search_shop = intval(StaffSer::queryParam('not_search_shop'));
        $shopIds = StaffSer::queryParam('shop_id', '');
        if ($not_search_shop == 0 && empty($shopIds)) {
            // 获取区域门店
            $areaId = StaffSer::queryParam('area_id');
            if ($areaId > 0) {
                $zoneShopIds = ServiceZoneModel::where('district_id', $areaId)
                    ->field('shop_id')
                    ->column('shop_id');

                if (!empty($zoneShopIds)) {
                    if (is_array($shopIds)) {
                        $shopIds = array_merge($shopIds, $zoneShopIds);
                    } else {
                        array_push($zoneShopIds, $shopIds);
                        $shopIds = array_unique($zoneShopIds);
                    }
                }
            }
        }

        // 门店id
        if (!empty($shopIds)) {
            // 使用框架方法
            //SELECT DISTINCT  `a`.`id` FROM `staff` `a` WHERE ( `a`.`shop_id` IN (82,2)  or `a`.`id` IN (198,201))  AND `a`.`audit_status` = '1'  AND `a`.`status` = '1'  AND `a`.`is_delete` = '0'
            // 原生SQL拼接，使用whereRaw方法
            //SELECT DISTINCT  `a`.`id` FROM `staff` `a` WHERE  ( a.shop_id in (82,2) or  a.id in (198,201) )  AND `a`.`audit_status` = '1'  AND `a`.`status` = '1'  AND `a`.`is_delete` = '0'
            // 去空
            if (is_array($shopIds)) {
                $shopIds       = array_filter($shopIds);
                $shareStaffIds = ShopShareStaffModel::where('to_shop_id', 'in', $shopIds)
                    ->where('status', ShopShareStaffModel::STATUS_PASS)
                    ->field('staff_id')
                    ->column('staff_id');
                if (!empty($shareStaffIds)) {
                    //$query->whereOr('a.id', 'in', $shareStaffIds);
                    $query->whereRaw("a.shop_id in (" . implode(',', $shopIds) . ") or a.id in (" . implode(',', $shareStaffIds) . ")");
                } else {
                    $query->where('a.shop_id', 'in', $shopIds);
                }
            } else {
                $shareStaffIds = ShopShareStaffModel::where('to_shop_id', $shopIds)
                    ->where('status', ShopShareStaffModel::STATUS_PASS)
                    ->field('staff_id')
                    ->column('staff_id');
                if (!empty($shareStaffIds)) {
                    //$query->whereOr('a.id', 'in', $shareStaffIds);
                    $query->whereRaw("a.shop_id = " . $shopIds . " or a.id in (" . implode(',', $shareStaffIds) . ")");
                } else {
                    $query->where('a.shop_id', '=', $shopIds);
                }
            }
        }

        // 职业选择
        if (StaffSer::queryParam('worker_id')) {
            $query->rightJoin('staff_worker w', 'a.id = w.staff_id');
            $query->where('w.worker_id', '=', StaffSer::queryParam('worker_id'));
        }

        // 年龄范围, 计算birthday
        if (StaffSer::queryParam('start_age') && StaffSer::queryParam('end_age')) {
            // 年龄范围,计算开始年份和结束年份
            $startYear = date('Y') - StaffSer::queryParam('end_age');
            $endYear   = date('Y') - StaffSer::queryParam('start_age');
            $query->where('a.birthday', 'between', ["$startYear-01-01", "$endYear-12-31"]);
        }

        // 籍贯
        if (StaffSer::queryParam('native_id')) {
            $nativeId = StaffSer::queryParam('native_id');
            if (is_array($nativeId)) {
                $query->where('a.native_province', 'in', $nativeId);
            } else {
                $query->where('a.native_province', '=', $nativeId);
            }
        }

        // 工作年限 n年以上
        if (StaffSer::queryParam('work_year')) {
            //计算start_work_year 开始工作年份
            $startWorkYear = date('Y') - StaffSer::queryParam('work_year');
            $query->where('a.start_work_year', '<=', $startWorkYear);
        }

        // 学历
        if (StaffSer::queryParam('education')) {
            $query->where('a.education', '<=', StaffSer::queryParam('education'));
        }

        // 语言
        if (StaffSer::queryParam('language')) {
            $language = explode(',', StaffSer::queryParam('language'));
            $query->rightJoin('staff_language_cache b', 'a.id = b.staff_id');
            $query->where('b.language', 'in', $language);
        }

        // 烹饪
        if (StaffSer::queryParam('cook')) {
            $cook = explode(',', StaffSer::queryParam('cook'));
            $query->rightJoin('staff_cooking_skill_cache c', 'a.id = c.staff_id');
            $query->where('c.cook', 'in', $cook);
        }

        // 证书
        if (StaffSer::queryParam('skill_num')) {
            $query->where('a.skill_total', '>=', StaffSer::queryParam('skill_num'));
        }

        // 属相
        if (StaffSer::queryParam('zodiac')) {
            $query->where('a.zodiac', 'in', StaffSer::queryParam('zodiac'));
        }

        // 星座
        if (StaffSer::queryParam('constellation')) {
            $query->where('a.constellation', 'in', StaffSer::queryParam('constellation'));
        }

        // 我的收藏
        if (StaffSer::queryParam('is_collect') == 1 && StaffSer::queryParam('member_id') > 0) {
            $query->rightJoin('member_collect d', 'a.id = d.info_id');
            $query->where('d.member_id', '=', StaffSer::queryParam('member_id'));
            $query->where('d.type_id', '=', MemberCollectModel::TYPE_STAFF);
        }

        // 审核通过
        $query = $query->where('a.audit_status', '=', StaffModel::CHECK_STATUS_SUCCESS);
        // 状态开启
        $query = $query->where('a.status', '=', StaffModel::STATUS_ENABLE);
        // 不删除
        $query = $query->where('a.is_delete', '=', 0);

        return $query->column('a.id');
    }

    public function filerAndSort(&$rows = [])
    {
        if (empty($rows)) {
            return true;
        }
        $work_sort  = StaffSer::queryParam('work_sort', '');
        $age_sort   = StaffSer::queryParam('age_sort', '');
        $skill_sort = StaffSer::queryParam('skill_sort', '');

        if ($work_sort != '') {
            if ($work_sort == 'asc') {
                usort($rows, function ($a, $b) {
                    if ($a['start_work_year'] == $b['start_work_year']) {
                        return 0;
                    }
                    return ($a['start_work_year'] > $b['start_work_year']) ? -1 : 1;
                });
            } else {
                usort($rows, function ($a, $b) {
                    if ($a['start_work_year'] == $b['start_work_year']) {
                        return 0;
                    }
                    return ($a['start_work_year'] < $b['start_work_year']) ? -1 : 1;
                });
            }
        }

        if ($age_sort != '') {
            if ($age_sort == 'asc') {
                usort($rows, function ($a, $b) {
                    if ($a['birthday'] == $b['birthday']) {
                        return 0;
                    }
                    return ($a['birthday'] < $b['birthday']) ? -1 : 1;
                });
            } else {
                usort($rows, function ($a, $b) {
                    if ($a['birthday'] == $b['birthday']) {
                        return 0;
                    }
                    return ($a['birthday'] > $b['birthday']) ? -1 : 1;
                });
            }
        }

        if ($skill_sort != '') {
            if ($skill_sort == 'asc') {
                usort($rows, function ($a, $b) {
                    if ($a['skill_total'] == $b['skill_total']) {
                        return 0;
                    }
                    return ($a['skill_total'] < $b['skill_total']) ? -1 : 1;
                });
            } else {
                usort($rows, function ($a, $b) {
                    if ($a['skill_total'] == $b['skill_total']) {
                        return 0;
                    }
                    return ($a['skill_total'] > $b['skill_total']) ? -1 : 1;
                });
            }
        }
        return true;
    }
}
