<?php


namespace app\api\service\Carts;

use app\api\service\BaseSer;
use app\common\enum\Response;
use app\common\model\GoodsModel;

class CartsDirectorSer extends BaseSer
{
    const CART_MODE_GOODS = 1; // 普通产品购买
    const CART_MODE_COOKING = 2; // 烹饪产品购买
    const CART_MODE_TRAINING = 3; // 培训产品购买

    // 输出格式，调整输出列的顺序，改这里就好了
    private $carts = [
        'member_id' => 0,
        'mobile' => '', //会员手机号
        'cook_mode' => 0, //制作方式 ,1-做好送上门；2-上门制作
        'service_mode' => 0, //服务方式，1-上门；2-到店
        'interview_mode' => 0, //面试设置，1-先面试后付款；2-先付款后面试；3-选择服务人员不需要面试；4-不选择服务人员不需要面试
        'use_coupon_switch' => 0, //是否使用优惠券
        'coupon_id' => 0, //使用优惠券id
        'use_point_switch' => 0, //是否使用积分
        'point' => 0, //使用积分
        'shipRules' => [], //配送规则
        'all_invalid' => 0, // 全部失效
        'future_lock_days' => [], //未来锁单天数
        'cart' => [
            'address' => [],
            'goodsItems' => [],
            'packages' => [], //邮寄是不是有包裹
            'sum' => [],
            'error_data' => [],
            'notices' => [], //失效商品
        ],
        'goods' => [], //商品信息
        'shop_snapshot' => [], //店铺快照
        'goods_snapshot' => [], //商品快照
        'staff_snapshot' => [], //员工快照
        'work_date' => [], // 工作日历
    ];

    public function getCarts(array $param): array
    {
        $cartsSer = $this->cartSerFactory($this->cartParam($param));
        $cartsSer = $this->BuilderCarts($cartsSer);
        return $this->CartsToArray($cartsSer);
    }

    /**
     * 根据操作获取购物车状态
     *
     * @param string $operation 操作类型：cart=购物车，order_confirm=订单确认，order_create=创建订单
     * @return array [状态码, 状态消息]
     */
    public function getStatus(string $operation): array
    {
        $status = Response::SUCCESS;
        $statusTxt = '操作成功';

        // 如果购物车为空，返回成功状态
        if (empty($this->carts)) {
            return [$status, $statusTxt];
        }

        // 处理失效商品、起订量等错误
        if (!empty($this->carts['cart']['error_data'])) {
            // 错误消息映射
            $eMsg = [
                'area'           => [0 => '区域变更', 1 => '区域变更'],
                'no_stock'       => [0 => '部分商品失效', 1 => '商品全部失效'],
                'min_buy_change' => [0 => '起订量变更', 1 => '起订量变更'],
                'service_date'   => [0 => '该服务时间不可用', 1 => '该服务时间不可用'],
                'max_buy_change' => [0 => '超过最大购买数量', 1 => '超过最大购买数量'],
                'payment_error'  => [0 => '支付方式错误', 1 => '支付方式错误'],
            ];

            // 错误代码映射
            $eCode = [
                'area'           => Response::GOODS_AREA_CHANGE,
                'no_stock'       => Response::GOODS_STOCK_NOT_ENOUGH,
                'min_buy_change' => Response::GOODS_MIN_ORDER_CHANGE,
                'service_date'   => Response::GOODS_SERVICE_DATE_CHANGE,
                'max_buy_change' => Response::GOODS_MIN_ORDER_CHANGE, // 使用现有的常量
                'payment_error'  => Response::REQUEST_PARAM_ERROR, // 使用现有的常量
            ];

            // 获取错误类型和是否全部失效
            $all_invalid = $this->carts['all_invalid'];
            $eType = $this->carts['cart']['error_data']['type'] ?? '';

            // 如果错误类型存在，设置相应的状态码和消息
            if (isset($eCode[$eType]) && isset($eMsg[$eType][$all_invalid])) {
                $status = $eCode[$eType];
                $statusTxt = $eMsg[$eType][$all_invalid];
            }
        }

        // 处理通知信息
        if (!empty($this->carts['cart']['notices'])) {
            // 处理积分错误
            if (isset($this->carts['cart']['notices']['points_error'])) {
                // 如果是订单创建操作，则返回错误状态
                if ($operation == 'order_create') {
                    $status = Response::REQUEST_PARAM_ERROR; // 使用现有的参数错误常量
                    $statusTxt = $this->carts['cart']['notices']['points_error'];
                }
                // 如果是购物车或订单确认操作，则只显示通知，不返回错误状态
            }
        }

        // 根据操作类型进行特定处理
        if ($operation == 'order_create' && $status != Response::SUCCESS) {
            // 如果是创建订单操作且有错误，可以进行特殊处理
            // 目前仅作为占位，可根据需求扩展
        }

        return [$status, $statusTxt];
    }

    // 处理参数
    public function cartParam(array $param): array
    {
        if (isset($param['service_date'])) {
            //处理日期，格式化Y-m-d
            $param['service_date'] = date('Y-m-d', strtotime($param['service_date']));
        }
        if (isset($param['service_time'])) {
            //处理时间，格式化H:i:s
            $param['service_time'] = date('H:i:00', strtotime($param['service_time']));
        }
        if (isset($param['service_mode'])) {
            //服务方式，1-上门；2-到店
            if ($param['service_mode'] == GoodsModel::SERVICE_MODE_SHOP) {
                $param['addr_id'] = 0;
            }
        }
        if (isset($param['additional_services'])) {
            //增值服务
            $param['additional_services'] = json_decode($param['additional_services'], true);
        }
         if (isset($param['coupon_id']) && $param['coupon_id'] > 0) {
            $param['use_coupon_switch'] = 1;
        }
        if (isset($param['point']) && $param['point'] > 0) {
            $param['use_point_switch'] = 1;
        }
        return $param;
    }

    private function cartSerFactory(array $param): CartsSer
    {
        switch ($param['cartType']) {
            case self::CART_MODE_COOKING:
                $cartsSer = new CookingCartSer($param);
                break;
            case self::CART_MODE_TRAINING:
                $cartsSer = new TrainingCartSer($param);
                break;
            default:
                $cartsSer = new NormalCartSer($param);
                break;
        }
        return $cartsSer;
    }


    /**
     * 实例化一个购物车对象
     *
     * @param CartsSer $cartsSer 购物车对象
     * @return CartsSer 处理后的购物车对象
     */
    private function BuilderCarts(CartsSer $cartsSer): CartsSer
    {
        // 直接使用通用的购物车构建方法
        return $this->comBuilderCarts($cartsSer);
    }

    private function comBuilderCarts(CartsSer $cartsSer): CartsSer
    {
        // 初始化购物车，查找会员信息
        $cartsSer->initCarts();
        // 收货地址
        $cartsSer->fillMemberAddress();
        // 根据商品id获取商品信息
        $cartsSer->fillGoodsFromDB();
        // 根据门店id获取门店信息
        $cartsSer->fillShopFromDB();
        // 根据服务人员id获取服务人员信息
        $cartsSer->fillStaffFromDB();
        // 获取服务人员可工作日期
        $cartsSer->fillStaffWorkDate();
        // 检查是否下架,最小购,最大购
        $cartsSer->checkGoodsMarketable();
        // 检查库存
        $cartsSer->checkGoodsStock();
        // 计算运费
        $cartsSer->calShipment();
        // 计算金额（包含优惠券处理）
        $cartsSer->calSum();

        $cartsSer->applyGoodsIds();
        return $cartsSer;
    }

    /**
     * 将购物车对象转换为数组
     *
     * @param CartsSer $cartsSer 购物车对象
     * @return array 转换后的数组
     */
    public function CartsToArray(CartsSer $cartsSer): array
    {
        // 设置基本信息
        $this->carts['member_id'] = $cartsSer->member_id;
        $this->carts['mobile'] = $cartsSer->member['mobile'] ?? '';
        $this->carts['service_mode'] = $cartsSer->service_mode;
        $this->carts['interview_mode'] = $cartsSer->interviewMode ?? 0;
        $this->carts['use_coupon_switch'] = $cartsSer->use_coupon_switch ?? 0;
        $this->carts['coupon_id'] = $cartsSer->coupon_id;
        $this->carts['use_point_switch'] = $cartsSer->use_point_switch ?? 0;
        $this->carts['point'] = $cartsSer->point;
        $this->carts['payment_type'] = $cartsSer->payment_type ?? 1;
        $this->carts['member_integral'] = $cartsSer->GetMemberIntegral;

        // 设置购物车内容
        $this->carts['cart']['address'] = $cartsSer->shipAddress;
        $this->carts['cart']['goodsItems'] = $cartsSer->goodsItems;
        $this->carts['cart']['sum'] = $cartsSer->sum;

        // 处理积分错误
        if (isset($cartsSer->sum['points_error'])) {
            if (!isset($this->carts['cart']['notices'])) {
                $this->carts['cart']['notices'] = [];
            }
            $this->carts['cart']['notices']['points_error'] = $cartsSer->sum['points_error'];
        }

        // 设置快照信息
        $this->carts['shop_snapshot'] = $cartsSer->shop_snapshot;
        $this->carts['staff_snapshot'] = $cartsSer->staff_snapshot;
        $this->carts['future_lock_days'] = $cartsSer->future_lock_days;
        $this->carts['work_date'] = $cartsSer->staffWorkDate ?? [];

        if (count($cartsSer->invalid_ids) > 0) {
            $type = 'area';
            $temp = $cartsSer->goodsItems[$cartsSer->invalid_ids[0]];
            if ($temp['stock'] < $temp['quantity']) {
                $type = 'no_stock';
            }
            if ($temp['quantity'] < $temp['min_buy'] || $temp['quantity'] > $temp['max_buy']) {
                $type = 'min_buy_change';
            }
            $invalidGoods = [];
            foreach ($cartsSer->invalid_ids as $id) {
                $invalidGoods[] = $cartsSer->goodsItems[$id];
            }
            $this->carts['cart']['error_data'] = [
                'type' => $type,
                'invalid_goods' => $invalidGoods,
            ];
        }
        if (empty($cartsSer->goods)) {
            $this->carts['all_invalid'] = 1;
            $this->carts['cart']['error_data'] = [
                'type' => 'no_stock',
                'invalid_goods' => [],
            ];
        }
        if ($cartsSer->staff_id > 0 && !empty($cartsSer->service_date)) {
            if (!in_array($cartsSer->service_date, $cartsSer->staffWorkDate)) {
                $this->carts['cart']['error_data'] = [
                    'type' => 'service_date',
                    'invalid_goods' => [],
                ];
            }
        }

        // 设置商品信息
        $this->carts['goods'] = $cartsSer->goods;

        // 处理商品快照
        $this->carts['goods_snapshot'] = $cartsSer->goods_snapshot;

        // 处理烹饪模式
        if ($cartsSer instanceof CookingCartSer) {
            $this->carts['cook_mode'] = 1; // 设置烹饪模式
        }

        return $this->carts;
    }
}
