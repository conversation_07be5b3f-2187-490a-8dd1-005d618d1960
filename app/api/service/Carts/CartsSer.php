<?php

namespace app\api\service\Carts;

use app\api\service\BaseSer;
use app\api\service\Goods\GoodsSer;
use app\api\service\Staff\StaffSer;
use app\common\model\MemberAddressModel;
use app\common\model\MemberModel;
use app\common\model\ShopModel;
use app\common\service\RegionService;
use app\common\service\StaffService;
use app\common\service\UploadService;
use app\common\service\CouponService;
use think\facade\Cache;

abstract class CartsSer extends BaseSer
{
    // 参数
    public $area_id = 0;
    public $cartType = 1;
    public $goods_id = 0;
    public $sku_ids = "";
    public $quantities = "";
    public $reqPage = 2; //1:cart(暂时不存购物车), 2:order_confirm, 3:order_create
    public $addr_id = 0;
    public $service_date = "";
    public $staff_id = 0;
    public $coupon_id = 0;
    public $service_mode = 1;
    public $service_time = "";
    public $cook_mode = 1; //制作方式 ,1-做好送上门；2-上门制作
    public $additional_services = []; //增值服务"[{"service_name": "加急服务", "price": 300.00, "quantity": 1}, {"service_name": "高级包装",  "price": 150.00,"quantity": 2}]"
    public $payment_type = 1; //1全额支付，2选择商品设置方式支付(如果商品的is_deposit为1，则定金deposit，尾款goods_price-deposit+杂费，使用final_payment_type+final_payment_days)
    public $point = 0;
    public $member_id = 0;
    public $token = "";
    public $time;

    // 处理数据
    public $use_coupon_switch = 0;
    public $use_point_switch = 0; // 是否使用积分抵扣，1-使用，0-不使用
    public $member = []; //会员详情
    public $GetMemberIntegral = []; //根据token获取积分和积分规则
    public $shipAddress = []; //收货地址
    public $goods = []; //商品信息
    public $goodsItems = []; //商品信息
    public $festivalPricingRules = []; //获取商品的节假日收费（[{"date": "2025-05-01", "festival_name": "五一", "price_adjustment": "200", "price_adjustment_type": "fixed"}, {"date": "2025-05-02", "festival_name": "五一", "price_adjustment": "200", "price_adjustment_type": "fixed"}]）
    public $extraData = []; //获取商品的增值收费服务
    public $lockDays = 0; //锁定天数
    public $serviceTimes = 0;  //服务时长
    public $interviewMode = 0; //面试设置，1-先面试后付款；2-先付款后面试；3-选择服务人员不需要面试；4-不选择服务人员不需要面试
    public $shop_id = 0; //店铺id
    public $staffWorkDate; //服务人员工作日
    public $future_lock_days = []; //设置锁定天数信息
    public $sum = []; //计算金额
    public $shop_snapshot = []; //店铺快照
    public $goods_snapshot = []; //商品快照
    public $staff_snapshot = []; //员工快照
    public $coupons = []; //优惠券信息

    public $invalid_ids = []; // 失效sku_id
    public $valid_ids = []; // 有效sku_id

    const CART_MODE = [
        CartsDirectorSer::CART_MODE_GOODS => '普通购物车',
        CartsDirectorSer::CART_MODE_COOKING => '烹饪购物车',
        CartsDirectorSer::CART_MODE_TRAINING => '培训购物车',
    ];

    public function __construct($config = [])
    {
        if (!empty($config)) {
            foreach ($config as $name => $value) {
                if ($name == 'bdid' || $name == 'version') {
                    continue;
                }
                if (property_exists($this, $name)) {
                    $this->$name = $value;
                }
            }
        }
        $this->time = time();
    }

    public static function operation($name)
    {
        $refs = [
            '1' => 'cart',
            '2' => 'order_confirm',
            '3' => 'order_create'
        ];
        return $refs[$name] ?? 'cart';
    }

    /**
     * 查询一些购物车需要的数据，避免以后重复查询
     */
    public function initCarts(): bool
    {
        if (is_numeric($this->member_id)) {
            $member = MemberModel::where('id', $this->member_id)->findOrEmpty();
            if ($member->isEmpty()) {
                return false;
            }
            $this->member = $member->toArray();

            // 获取会员积分
            $config = config('api.services.Member');
            $url = $config['BaseURL'] . $config['Api']['GetMemberIntegral'];
            $res = curlGetApiContentByUrl($url, [], ['Authorization: Bearer ' . $this->token], false);
            if (empty($res) || $res['code'] != 1) {
                return false;
            }
            $this->GetMemberIntegral = $res['data'];
            $this->member['points'] = $res['data']['available_points'] ?? 0; //会员可用积分
        }
        return true;
    }

    //填充购物车收货地址
    public function fillMemberAddress(): bool
    {
        if (is_numeric($this->member_id) && $this->member_id > 0 && is_numeric($this->addr_id) && $this->addr_id > 0) {
            $shipAddress = MemberAddressModel::where('id', $this->addr_id)->where('member_id', $this->member_id)->findOrEmpty();
            if ($shipAddress->isEmpty()) {
                return false;
            }
            $this->shipAddress = $shipAddress->toArray();
            $this->area_id = $shipAddress->area_id; //地区id, 不使用传值id
        }
        return true;
    }

    //加载商品信息
    public abstract function fillGoodsFromDB();

    //加载门店信息,根据shop_id获取门店信息，并格式化地址和logo
    public function fillShopFromDB(): bool
    {
        // 检查shop_id是否有效
        if (!is_numeric($this->shop_id) || $this->shop_id <= 0) {
            return true; // 无需处理，直接返回成功
        }

        // 只查询需要的字段，减少数据传输量
        $fields = ['id', 'shop_name', 'logo_id', 'area_id', 'address', 'contact_name', 'contact_phone'];

        // 查询门店信息
        $shop = ShopModel::field($fields)
            ->where('id', '=', $this->shop_id)
            ->where('status', '=', ShopModel::STATUS_ENABLE)
            ->where('audit_status', '=', ShopModel::STATUS_PASS)
            ->where('expire_time', '>', $this->time)
            ->findOrEmpty();

        // 如果门店不存在或已过期，返回失败
        if ($shop->isEmpty()) {
            return false;
        }

        // 获取区域信息并格式化地址
        if ($shop->area_id) {
            $region = (new RegionService())->getRegionDetail($shop->area_id);
            if (!empty($region)) {
                // 格式化地址：如果省名和市名相同，则省略市名
                $shop->format_address = $region['province_name'];
                if ($region['province_name'] != $region['city_name']) {
                    $shop->format_address .= $region['city_name'];
                }
                $shop->format_address .= $region['district_name'] . $shop->address;
            }
        }

        // 获取并格式化logo
        $shop->format_log = $shop->logo_id > 0 ? formatLogo(UploadService::get($shop->logo_id)) : '';
        $shop_snapshot = $shop->toArray(); // 保存门店信息
        if (isset($this->GetMemberIntegral['point_value_rule']['id']) && $this->GetMemberIntegral['point_value_rule']['id'] > 0) {
            //currency_value元 = points_required积分， 计算积分比例
            $shop_snapshot['point_exchange_rate'] = $this->GetMemberIntegral['point_value_rule']['currency_value'] / $this->GetMemberIntegral['point_value_rule']['points_required'];
            $shop_snapshot['min_redeem_amount']  = $this->GetMemberIntegral['point_value_rule']['min_redeem_amount'];
            $shop_snapshot['max_redeem_amount'] =   $this->GetMemberIntegral['point_value_rule']['max_redeem_amount'];
        }
        // 保存门店信息
        $this->shop_snapshot =  $shop_snapshot;

        return true;
    }

    public function fillStaffFromDB()
    {
        if (!is_numeric($this->staff_id)) {
            $this->staff_snapshot = [];
            return false;
        }
        $staff = (new StaffSer('Cart', ['staff_id' => $this->staff_id]))->getData();
        $this->staff_snapshot =  $staff[0] ?? [];
        return true;
    }

    /**
     * 获取服务人员工作日期
     *
     * 根据服务日期获取服务人员的工作日期
     * 注意：烹饪购物车(CookingCartSer)会重写此方法，处理特定的时间逻辑
     *
     * @return bool 操作是否成功
     */
    public function fillStaffWorkDate()
    {
        // 检查基本参数
        if (!is_numeric($this->staff_id) || empty($this->service_date)) {
            $this->staffWorkDate = [];
            return false;
        }

        // 其他购物车类型的处理逻辑
        // 服务日期时间戳
        $serviceTime = strtotime($this->service_date);

        // 计算锁定时间（天数转换为秒）
        $lockDaysInSeconds = $this->lockDays * 24 * 3600;

        // 计算实际开始日期（服务日期前的锁定天数）
        $startTime = $serviceTime - $lockDaysInSeconds;

        // 如果开始时间已过，从明天开始
        if ($startTime <= time()) {
            $startTime = strtotime(date('Y-m-d', strtotime('+1 day')));
        }

        // 计算预期开始日期（服务日期）
        $expectedStartDate = $serviceTime;

        // 计算预期结束日期（服务日期 + 服务天数 - 1）
        // 注意：服务天数包含开始日期，所以需要减1
        $expectedEndDate = $serviceTime + (($this->serviceTimes - 1) * 24 * 3600);

        // 计算实际结束日期（预期结束日期 + 锁定天数）
        $endTime = $expectedEndDate + $lockDaysInSeconds;

        // 格式化日期
        $startDate = date('Y-m-d', $startTime);
        $endDate = date('Y-m-d', $endTime);

        // 获取工作日期列表
        $this->staffWorkDate = (new StaffService())->getWorkList([
            'staff_id' => $this->staff_id,
            'start_date' => $startDate,
            'end_date' => $endDate,
        ]);

        // 设置锁定天数信息
        $this->future_lock_days = [
            // 锁定天数
            'lock_days' => $this->lockDays,
            // 服务时长
            'service_times' => $this->serviceTimes,
            // 实际锁定开始日期
            'start_date' => $startDate,
            // 预期锁定开始日期（服务开始日期）
            'expected_start_date' => date('Y-m-d', $expectedStartDate),
            // 预期锁定结束日期（服务结束日期）
            'expected_end_date' => date('Y-m-d', $expectedEndDate),
            // 实际锁定结束日期
            'end_date' => $endDate,
        ];

        return true;
    }

    //检查 产品 是否上架，如果下架 产品，直接移出
    public function checkGoodsMarketable(): bool
    {
        if (empty($this->goodsItems)) {
            return false;
        }
        foreach ($this->goodsItems as $k => $v) {
            // 上下架
            if ($v['marketable'] == 0) {
                $this->goodsItems[$k]['selected'] = 0;
            }
            // 限购
            if ($v['quantity'] < $v['min_buy'] || $v['quantity'] > $v['max_buy']) {
                $this->goodsItems[$k]['selected'] = 0;
            }
        }
        return true;
    }
    //查验商品库存数量，如果库存不足，直接移出
    public function checkGoodsStock(): bool
    {
        if (empty($this->goodsItems)) {
            return false;
        }

        $getCacheStock = [];
        foreach ($this->goodsItems as $k => $v) {
            array_push($getCacheStock, ['goods_id' => $v['goods_id'], 'goods_sku_id' => $v['goods_sku_id']]);
        }

        $script   = file_get_contents(getLuaFile('stock'));
        $redis = Cache::store('redis')->handler();
        $result = $redis->eval($script, [json_encode($getCacheStock, JSON_UNESCAPED_UNICODE)]);
        $result = json_decode($result, true);
        foreach ($this->goodsItems as $k => $v) {
            $stock = $result[$v['goods_id']][$v['goods_sku_id']]['stock'] ?? 0;
            $this->goodsItems[$k]['stock'] = $stock; // 更新库存
            if ($stock < $v['quantity']) {
                $this->goodsItems[$k]['selected'] = 0;
            }
        }
        return true;
    }

    //计算运费,本处运费仅根据位置获取运费
    public function calShipment(): bool
    {
        $this->sum['cost_freight'] = 0;
        return true;
    }

    //加载购物车使用的优惠券
    public function fillCoupon(): bool
    {
        // 初始化优惠券信息
        $this->coupons = [];
        
        // 如果不使用积分抵扣，直接返回
        if (empty($this->use_coupon_switch) || $this->use_coupon_switch != 1) {
            return false;
        }

        // 如果没有指定优惠券ID，直接返回成功
        if (!is_numeric($this->coupon_id) || $this->coupon_id <= 0) {
            return false;
        }

        // 如果没有会员ID或token，无法获取优惠券
        if (!is_numeric($this->member_id) || $this->member_id <= 0 || empty($this->token)) {
            return false;
        }

        // 准备获取优惠券的参数
        $goodsIds = [];
        if (!empty($this->goodsItems)) {
            foreach ($this->goodsItems as $item) {
                if ($item['selected'] == 1) {
                    $goodsIds[] = $item['goods_id'];
                }
            }
        }

        // 计算当前商品总价（用于优惠券验证）
        $totalPrice = 0;
        if (!empty($this->sum['total_amount'])) {
            $totalPrice = floatval($this->sum['total_amount']);
        } else {
            // 如果sum还没有计算，动态计算商品总价
            if (!empty($this->goodsItems)) {
                foreach ($this->goodsItems as $item) {
                    if ($item['selected'] == 1) {
                        $price = floatval($item['goods_price']);
                        $quantity = intval($item['quantity']);
                        $totalPrice += $price * $quantity;
                    }
                }
            }
        }

        $data = [
            'member_id' => $this->member_id,
            'shop_id'   => $this->shop_id,
            'price'     => $totalPrice,
            'goods_ids' => implode(',', array_unique($goodsIds)),
            'coupon_id' => $this->coupon_id,
        ];

        // 调用优惠券服务获取优惠券信息
        $result = CouponService::couponOrderList($data, $this->token, "api");
        if ($result['code'] != 1) {
            return false;
        }

        // 获取优惠券列表
        $couponList = $result['data'] ?? [];
        if (empty($couponList)) {
            return false;
        }

        // 查找指定的优惠券
        $selectedCoupon = null;
        foreach ($couponList as $coupon) {
            if (isset($coupon['id']) && $coupon['id'] == $this->coupon_id) {
                $selectedCoupon = $coupon;
                break;
            }
        }

        // 如果没有找到指定的优惠券
        if (empty($selectedCoupon)) {
            return false;
        }

        // 验证优惠券状态
        if (isset($selectedCoupon['active_state']) && $selectedCoupon['active_state'] != 1) {
            return false;
        }

        // 验证优惠券时间
        $currentTime = time();
        if (isset($selectedCoupon['start_time']) && strtotime($selectedCoupon['start_time']) > $currentTime) {
            return false;
        }
        if (isset($selectedCoupon['expire_time']) && strtotime($selectedCoupon['expire_time']) < $currentTime) {
            return false;
        }

        // 验证最小使用金额
        $minOrderAmount = floatval($selectedCoupon['min_order_amount'] ?? 0);
        if ($minOrderAmount > 0 && $totalPrice < $minOrderAmount) {
            return false;
        }

        // 验证门店限制
        $couponShopId = intval($selectedCoupon['shop_id'] ?? 0);
        if ($couponShopId > 0 && $couponShopId != $this->shop_id) {
            return false;
        }

        // 保存优惠券信息
        $this->coupons = $selectedCoupon;

        return true;
    }

    /**
     * 计算订单金额
     *
     * 支持以下功能：
     * 1. 定金和多次尾款支付
     * 2. 全额支付
     * 3. 积分支付（1000积分=10元）
     *
     * @return bool 操作是否成功
     */
    public function calSum(): bool
    {
        if (empty($this->goodsItems)) {
            return false;
        }

        // 初始化金额
        $this->initSumValues();

        // 计算商品总价
        $this->calculateTotalAmount();

        // 计算节假日定价调整
        $this->calculateFestivalPricing();

        // 计算增值服务费用
        $this->calculateAdditionalServices();

        // 获取优惠券信息（需要在计算商品总价之后）
        $this->fillCoupon();

        // 计算优惠金额
        $this->calculateDiscounts();

        // 预计算最终金额（不包含积分抵扣）
        $this->preFinalAmount();

        // 计算定金和尾款
        $this->calculateDepositAndRemainingAmount();

        // 计算积分抵扣
        $this->calculatePointsDiscount();

        // 计算最终支付金额
        $this->calculateFinalAmount();

        return true;
    }

    /**
     * 初始化金额计算相关的值
     *
     * 该方法初始化所有与计算购物车金额相关的参数
     */
    protected function initSumValues(): void
    {
        // ===== 数量统计 =====
        $this->sum['total_quantity'] = 0;      // 购物车中商品的总数量
        $this->sum['selected_quantity'] = 0;   // 购物车中选中的商品数量

        // ===== 基本金额统计 =====
        $this->sum['total_amount'] = '0.000';  // 商品总价（不包含运费、杂费等额外费用）
        $this->sum['final_amount'] = '0.000';  // 最终支付金额（包含所有费用和折扣）
        $this->sum['cost_item'] = '0.000';     // 商品原价总和（未打折前的价格）

        // ===== 折扣相关 =====
        $this->sum['discount'] = '0.000';          // 总折扣金额（商品折扣+订单折扣+优惠券折扣）
        $this->sum['discount_goods'] = '0.000';    // 商品折扣金额（商品原价 - 实际价格）
        $this->sum['discount_order'] = '0.000';    // 订单折扣金额（订单级别的折扣）
        $this->sum['discount_coupon'] = '0.000';   // 优惠券折扣金额
        $this->sum['cost_payment'] = '0.000';      // 商品成本支付费用（商品原价 - 总折扣）

        // ===== 额外费用 =====
        $this->sum['cost_freight'] = '0.000';      // 运费
        $this->sum['other_fee'] = '0.000';         // 杂费（餐饮的专属费用）
        $this->sum['festival_fee'] = '0.000';      // 节假日调整费用（节假日加价）

        // ===== 积分相关 =====
        if (isset($this->member['points'])) {
            $this->sum['points'] = intval($this->member['points']);  // 会员总积分
        }
        $this->sum['point_exchange_rate'] = 0.010;  // 积分兑换比例（1000积分=10元）
        $this->sum['score_u'] = 0;                 // 实际使用的积分
        $this->sum['score_u_max'] = 0;             // 最大可用积分
        $this->sum['score_u_amount'] = '0.000';    // 积分抵扣金额
        $this->sum['score_g'] = 0;                 // 可得积分（订单完成后获得的积分）

        // ===== 余额相关 =====
        if (isset($this->member['balance'])) {
            $this->sum['balance'] = $this->member['balance'];  // 会员余额
        }
        $this->sum['can_use_advance'] = 'true';    // 是否可以使用余额
        $this->sum['advance_max'] = '0.000';       // 最大可用余额
        $this->sum['advance'] = '0.000';           // 实际使用的余额

        // ===== 支付相关 =====
        $this->sum['payed'] = '0.000';             // 实际需要支付的金额（最终金额 - 积分抵扣 - 余额支付）
        $this->sum['deposit_amount'] = '0.000';    // 定金金额（如果商品支持定金+尾款支付）
        $this->sum['remaining_amount'] = '0.000';   // 尾款金额（总金额 - 定金）
        $this->sum['amount'] = '0.000';            // 支付金额（等同于 payed）

        // ===== 增值服务相关 =====
        $this->sum['additional_services_fee'] = '0.000';  // 增值服务费用总额
        $this->sum['additional_services_items'] = [];     // 增值服务项目列表
        $this->sum['payment_type'] = $this->payment_type;
        $this->sum['final_payment_type'] = 0;            // 尾款支付类型（1=服务开始前支付；2=服务完成前支付）
        $this->sum['final_payment_days'] = 0;            // 尾款支付天数（相对于服务开始或结束的天数）

        // ===== 优惠券相关 =====
        $this->sum['coupon_info'] = [];                  // 优惠券信息
        $this->sum['can_use_coupon'] = false;            // 是否可以使用优惠券
        $this->sum['coupon_amount'] = '0.000';           // 优惠券金额
    }

    /**
     * 计算商品总价
     */
    protected function calculateTotalAmount(): void
    {
        $totalAmount = 0;
        $costItem = 0;
        $totalQuantity = 0;
        $selectedQuantity = 0;
        $otherFee = 0; // 杂费，餐饮的专属费用

        // 获取第一个商品的信息，用于获取杂费
        $firstSku = reset($this->goods);
        if ($firstSku && isset($firstSku['other_fee'])) {
            $otherFee = floatval($firstSku['other_fee']);
        }

        // 获取节假日定价规则
        if ($firstSku && isset($firstSku['festival_pricing_rules'])) {
            $this->festivalPricingRules = json_decode($firstSku['festival_pricing_rules'], true);
        }

        foreach ($this->goodsItems as $item) {
            $quantity = intval($item['quantity']);
            $totalQuantity += $quantity;

            // 计算选中的商品
            if ($item['selected'] == 1) {
                $selectedQuantity += $quantity;
                $price = floatval($item['goods_price']);
                $linePrice = floatval($item['line_price']);

                // 累加商品总价
                $totalAmount += $price * $quantity;

                // 累加商品原价
                $costItem += $linePrice * $quantity;
            }
        }

        // 更新统计数据
        $this->sum['total_quantity'] = $totalQuantity;
        $this->sum['selected_quantity'] = $selectedQuantity;
        $this->sum['total_amount'] = number_format($totalAmount, 3, '.', '');
        $this->sum['cost_item'] = number_format($costItem, 3, '.', '');
        $this->sum['other_fee'] = number_format($otherFee, 3, '.', ''); // 设置杂费
    }

    /**
     * 计算节假日定价调整
     *
     * 检查服务时间是否包含节假日，并调整价格
     */
    protected function calculateFestivalPricing(): void
    {
        // 初始化节假日调整费用
        $this->sum['festival_fee'] = '0.000';
        $this->sum['festival_days'] = [];

        // 如果没有节假日定价规则或服务日期，直接返回
        if (empty($this->festivalPricingRules) || empty($this->service_date)) {
            return;
        }

        // 获取服务开始和结束日期
        $startDate = $this->service_date;
        $endDate = $startDate;

        // 如果有服务天数，计算结束日期
        if ($this->serviceTimes > 1) {
            $endDate = date('Y-m-d', strtotime($startDate . ' + ' . ($this->serviceTimes - 1) . ' days'));
        }

        // 如果有锁定天数信息，使用锁定天数信息中的日期
        if (!empty($this->future_lock_days)) {
            if (isset($this->future_lock_days['start_date'])) {
                $startDate = $this->future_lock_days['start_date'];
            }
            if (isset($this->future_lock_days['end_date'])) {
                $endDate = $this->future_lock_days['end_date'];
            }
        }

        // 生成服务日期范围内的所有日期
        $serviceDates = [];
        $currentDate = $startDate;
        while (strtotime($currentDate) <= strtotime($endDate)) {
            $serviceDates[] = $currentDate;
            $currentDate = date('Y-m-d', strtotime($currentDate . ' + 1 day'));
        }

        // 检查每一天是否是节假日
        $festivalDays = [];
        $totalAdjustment = 0;

        foreach ($serviceDates as $date) {
            foreach ($this->festivalPricingRules as $rule) {
                if ($rule['date'] == $date) {
                    // 计算调整金额
                    $adjustment = 0;
                    if ($rule['price_adjustment_type'] == 'fixed') {
                        // 固定金额调整
                        $adjustment = floatval($rule['price_adjustment']);
                    } elseif ($rule['price_adjustment_type'] == 'percentage') {
                        // 百分比调整
                        $baseAmount = floatval($this->sum['total_amount']) / count($serviceDates);
                        $adjustment = $baseAmount * floatval($rule['price_adjustment']) / 100;
                    }

                    // 累加调整金额
                    $totalAdjustment += $adjustment;

                    // 记录节假日信息
                    $festivalDays[] = [
                        'date' => $date,
                        'festival_name' => $rule['festival_name'],
                        'adjustment' => number_format($adjustment, 3, '.', '')
                    ];

                    // 每天只应用一个节假日规则
                    break;
                }
            }
        }

        // 更新节假日调整费用
        $this->sum['festival_fee'] = number_format($totalAdjustment, 3, '.', '');
        $this->sum['festival_days'] = $festivalDays;

        // 注意：节假日调整费用不应该包含在total_amount中
        // 它将在calculateFinalAmount方法中被加到final_amount中
    }

    /**
     * 计算优惠金额
     *
     * 注意：折扣应该是正数，表示从原价中减去的金额
     */
    protected function calculateDiscounts(): void
    {
        // 商品优惠 = 商品原价 - 商品实际价格
        $discountGoods = floatval($this->sum['cost_item']) - floatval($this->sum['total_amount']);

        // 确保折扣为正数或零
        $discountGoods = max(0, $discountGoods);    
        $this->sum['discount_goods'] = number_format($discountGoods, 3, '.', '');

        // 订单优惠
        $discountOrder = max(0, floatval($this->sum['discount_order']));

        // 计算优惠券优惠
        $discountCoupon = $this->calculateCouponDiscount();

        // 总优惠 = 商品优惠 + 订单优惠 + 优惠券优惠
        $totalDiscount = $discountGoods + $discountOrder + $discountCoupon;
        $this->sum['discount'] = number_format($totalDiscount, 3, '.', '');

        // 商品总成本支付费用 = 商品原价 - 总优惠
        // 注意：如果总优惠大于商品原价，则成本支付费用为0
        $costPayment = max(0, floatval($this->sum['cost_item']) - $totalDiscount);
        $this->sum['cost_payment'] = number_format($costPayment, 3, '.', '');
    }

    /**
     * 计算优惠券折扣金额
     *
     * @return float 优惠券折扣金额
     */
    protected function calculateCouponDiscount(): float
    {
        // 初始化优惠券折扣金额
        $discountCoupon = 0;

        // 如果没有优惠券信息，直接返回0
        if (empty($this->coupons)) {
            $this->use_coupon_switch = 0;
            $this->sum['discount_coupon'] = '0.000';
            $this->sum['can_use_coupon'] = false;
            $this->sum['coupon_info'] = [];
            $this->sum['coupon_amount'] = '0.000';
            return 0;
        }

        // 获取优惠券金额
        $couponAmount = floatval($this->coupons['amount'] ?? 0);
        if ($couponAmount <= 0) {
            $this->use_coupon_switch = 0;
            $this->sum['discount_coupon'] = '0.000';
            $this->sum['can_use_coupon'] = false;
            $this->sum['coupon_info'] = [];
            $this->sum['coupon_amount'] = '0.000';
            return 0;
        }

        // 获取当前商品总价（用于验证最小使用金额）
        $totalAmount = floatval($this->sum['total_amount']);

        // 验证最小使用金额
        $minOrderAmount = floatval($this->coupons['min_order_amount'] ?? 0);
        if ($minOrderAmount > 0 && $totalAmount < $minOrderAmount) {
            $this->use_coupon_switch = 0;
            $this->sum['discount_coupon'] = '0.000';
            $this->sum['can_use_coupon'] = false;
            $this->sum['coupon_info'] = $this->coupons;
            $this->sum['coupon_amount'] = number_format($couponAmount, 3, '.', '');
            return 0;
        }

        // 优惠券折扣金额不能超过商品总价
        $discountCoupon = min($couponAmount, $totalAmount);

        // 更新优惠券相关信息
        $this->use_coupon_switch = 1;
        $this->sum['discount_coupon'] = number_format($discountCoupon, 3, '.', '');
        $this->sum['can_use_coupon'] = true;
        $this->sum['coupon_info'] = $this->coupons;
        $this->sum['coupon_amount'] = number_format($couponAmount, 3, '.', '');

        return $discountCoupon;
    }

    /**
     * 获取商品的积分抵扣设置
     *
     * 从已加载的商品数据中获取 is_points_discount 和 points_discount_max 字段
     *
     * @return array [是否允许积分抵扣, 积分抵扣上限(元)]
     */
    protected function fillGoodsPointsDiscount(): array
    {
        // 默认值：允许积分抵扣，无上限
        $isPointsDiscount = 1;
        $pointsDiscountMax = 0;

        // 如果没有商品数据，返回默认值
        if (empty($this->goods)) {
            return [$isPointsDiscount, $pointsDiscountMax];
        }

        // 获取第一个商品的信息，因为所有商品都来自同一个 goods_id
        $firstSku = reset($this->goods);
        if ($firstSku) {
            // 如果商品数据中有积分抵扣相关字段，使用这些字段
            if (isset($firstSku['is_points_discount'])) {
                $isPointsDiscount = $firstSku['is_points_discount'];
            }
            if (isset($firstSku['points_discount_max'])) {
                $pointsDiscountMax = $firstSku['points_discount_max'];
            }
        }

        return [$isPointsDiscount, $pointsDiscountMax];
    }

    /**
     * 计算积分抵扣
     *
     * 使用 $this->member['points'] 作为会员总积分
     * 使用 $this->points 作为当前使用的积分
     */
    protected function calculatePointsDiscount(): void
    {
        // 如果不使用积分抵扣，直接返回
        if (empty($this->use_point_switch) || $this->use_point_switch != 1) {
            return;
        }

        // 获取商品的积分抵扣设置
        list($isPointsDiscount, $pointsDiscountMax) = $this->fillGoodsPointsDiscount();

        // 如果商品不允许积分抵扣，直接返回
        if ($isPointsDiscount != 1) {
            return;
        }

        // 获取积分兑换比例和限制，优先使用店铺设置
        $exchangeRate = 0.010; // 默认比例：1000积分=10元
        $minRedeemAmount = 0; // 最低兑换积分数
        $maxRedeemAmount = 0; // 最高兑换积分数

        // 从店铺快照中获取积分设置
        if (isset($this->shop_snapshot['point_exchange_rate']) && $this->shop_snapshot['point_exchange_rate'] > 0) {
            $exchangeRate = floatval($this->shop_snapshot['point_exchange_rate']);
        }
        if (isset($this->shop_snapshot['min_redeem_amount']) && $this->shop_snapshot['min_redeem_amount'] > 0) {
            $minRedeemAmount = intval($this->shop_snapshot['min_redeem_amount']);
        }
        if (isset($this->shop_snapshot['max_redeem_amount']) && $this->shop_snapshot['max_redeem_amount'] > 0) {
            $maxRedeemAmount = intval($this->shop_snapshot['max_redeem_amount']);
        }

        // 记录积分设置
        $this->sum['point_exchange_rate'] = $exchangeRate;
        $this->sum['min_redeem_amount'] = $minRedeemAmount;
        $this->sum['max_redeem_amount'] = $maxRedeemAmount;

        // 获取会员总积分
        $memberPoints = 0;
        if (isset($this->member['points'])) {
            $memberPoints = intval($this->member['points']);
        }
        $this->sum['points'] = $memberPoints; // 记录会员总积分

        // 计算可用的最大积分
        $maxPointsAmount = 0;

        // 根据支付类型确定可抵扣的最大金额
        if ($this->payment_type == 2 && isset($this->sum['deposit_amount']) && floatval($this->sum['deposit_amount']) > 0) {
            // 定金+尾款支付方式：积分只能抵扣定金部分，尾款不参与积分抵扣
            $maxPointsAmount = floatval($this->sum['deposit_amount']);

            // 记录日志，方便调试
            // Log::info('Payment type 2: Using deposit_amount for max points: ' . $maxPointsAmount);
        } else {
            // 全额支付方式：积分可抵扣最终金额
            $maxPointsAmount = floatval($this->sum['final_amount']);

            // 记录日志，方便调试
            // Log::info('Payment type 1: Using final_amount for max points: ' . $maxPointsAmount);
        }

        // 如果设置了积分抵扣上限，则限制抵扣金额
        if ($pointsDiscountMax > 0 && $pointsDiscountMax < $maxPointsAmount) {
            $maxPointsAmount = $pointsDiscountMax;
        }

        // 计算最大可用积分
        $maxPoints = floor($maxPointsAmount / $exchangeRate); // 最大金额除以每积分的价值

        // 限制最大积分不超过用户拥有的积分
        $availablePoints = min($maxPoints, $memberPoints);

        // 如果设置了最高兑换积分数，进一步限制
        if ($maxRedeemAmount > 0) {
            $availablePoints = min($availablePoints, $maxRedeemAmount);
        }

        $this->sum['score_u_max'] = $availablePoints; // 记录最大可用积分
        $this->sum['can_use_points'] = $availablePoints > 0 && ($minRedeemAmount == 0 || $availablePoints >= $minRedeemAmount);

        // 获取当前使用的积分
        $usePoints = 0;
        $userInputPoints = 0;

        // 获取用户输入的积分
        if (isset($this->point) && $this->point > 0) {
            // 使用已存在的属性
            $userInputPoints = intval($this->point);
        }

        // 检查最低兑换积分数限制
        if ($minRedeemAmount > 0 && $userInputPoints > 0 && $userInputPoints < $minRedeemAmount) {
            // 如果用户输入的积分小于最低兑换积分数，不允许使用积分
            $this->sum['points_error'] = '积分不足最低兑换限制' . $minRedeemAmount;
            $this->sum['can_use_points'] = false; // 当有积分错误时，设置can_use_points为false
            $usePoints = 0;
        } else {
            // 限制使用的积分不超过最大可用积分
            $usePoints = min($userInputPoints, $availablePoints);

            // 如果有最低兑换积分数限制，且用户要使用积分，则必须达到最低限制
            if ($minRedeemAmount > 0 && $usePoints > 0 && $usePoints < $minRedeemAmount) {
                $usePoints = 0;
                $this->sum['points_error'] = '积分不足最低兑换限制' . $minRedeemAmount;
                $this->sum['can_use_points'] = false; // 当有积分错误时，设置can_use_points为false
            }
        }

        // 如果有使用积分
        if ($usePoints > 0) {
            $this->sum['score_u'] = $usePoints; // 记录实际使用的积分

            // 计算积分抵扣金额
            // 根据积分兑换比例，1000积分=10元，即 point_exchange_rate=0.01
            // 所以 300 积分 = 300 * 0.01 = 3 元
            $pointsAmount = $usePoints * $exchangeRate;

            // 限制积分抵扣金额不超过可抵扣的最大金额
            $pointsAmount = min($pointsAmount, $maxPointsAmount);

            $this->sum['score_u_amount'] = number_format($pointsAmount, 3, '.', '');
        } else {
            $this->sum['score_u'] = 0;
            $this->sum['score_u_amount'] = '0.000';
        }

        // 计算可得积分（按照最终支付金额的1%，即100元获10积分）
        // 注意：这里不能直接使用final_amount，因为它还没有被计算
        // 在calculateFinalAmount方法中再更新score_g
    }

    /**
     * 计算定金和尾款
     *
     * 定金金额保持固定，不按比例调整
     * 各种服务费（增值服务费、节假日调整费等）计入尾款
     */
    protected function calculateDepositAndRemainingAmount(): void
    {
        // 获取商品信息
        $goodsId = $this->goods_id;
        if (empty($goodsId) || empty($this->goods)) {
            return;
        }

        // 获取商品基本价格（不包含服务费）
        $baseAmount = floatval($this->sum['total_amount']);

        // 获取最终金额（包含所有费用）
        $finalAmount = isset($this->sum['final_amount']) ? floatval($this->sum['final_amount']) : $baseAmount;

        // 从已加载的商品数据中获取定金相关信息
        // 默认值
        $isDeposit = 0;
        $finalPaymentType = 1;
        $finalPaymentDays = 0;

        // 获取第一个商品的信息，因为所有商品都来自同一个 goods_id
        $firstSku = reset($this->goods);
        if ($firstSku) {
            // 如果商品数据中有定金相关字段，使用这些字段
            if (isset($firstSku['is_deposit'])) {
                $isDeposit = $firstSku['is_deposit'];
            }
            if (isset($firstSku['final_payment_type'])) {
                $finalPaymentType = $firstSku['final_payment_type'];
            }
            if (isset($firstSku['final_payment_days'])) {
                $finalPaymentDays = $firstSku['final_payment_days'];
            }
        }

        // 先记录支付类型相关信息，供后续使用
        $this->sum['is_deposit'] = $isDeposit;
        $this->sum['final_payment_type'] = $finalPaymentType;
        $this->sum['final_payment_days'] = $finalPaymentDays;

        // 如果用户选择全额支付，或者商品没有定金
        if ($this->payment_type == 1 || $isDeposit != 1) {
            // 全额支付
            $this->sum['deposit_amount'] = '0.000';

            // 如果是全额支付，则剩余金额应为0
            if ($this->payment_type == 1) {
                $this->sum['remaining_amount'] = '0.000';
            } else {
                // 如果是因为商品没有定金，则全额作为尾款
                $this->sum['remaining_amount'] = number_format($finalAmount, 3, '.', '');
            }

            $this->sum['final_payment_type'] = 0;
            $this->sum['final_payment_days'] = 0;
            return;
        }

        // 如果用户选择商品设置方式支付，并且商品有定金
        if ($this->payment_type == 2 && $isDeposit == 1) {
            // 初始化定金金额
            $depositAmount = 0;

            // 遍历所有选中的商品项，累加定金金额
            foreach ($this->goodsItems as $item) {
                // 只计算选中的商品
                if ($item['selected'] != 1) {
                    continue;
                }

                // 获取商品对应的SKU信息
                $skuId = $item['goods_sku_id'];
                $quantity = intval($item['quantity']);
                $skuInfo = null;

                // 在goods数组中查找对应的SKU信息
                foreach ($this->goods as $goods) {
                    if (isset($goods['goods_sku_id']) && $goods['goods_sku_id'] == $skuId) {
                        $skuInfo = $goods;
                        break;
                    }
                }

                if ($skuInfo) {
                    // 计算单个SKU的定金金额
                    $skuDepositAmount = 0;

                    if (isset($skuInfo['deposit_amount']) && !empty($skuInfo['deposit_amount'])) {
                        // 如果商品有定金金额字段，使用该字段
                        $skuDepositAmount = floatval($skuInfo['deposit_amount']);
                    } else if (isset($skuInfo['deposit']) && !empty($skuInfo['deposit'])) {
                        // 兼容旧字段名
                        $skuDepositAmount = floatval($skuInfo['deposit']);
                    } else {
                        // 如果没有定金金额字段，使用商品价格的30%作为定金
                        $depositRate = 0.3; // 定金比例，可以根据实际需求调整
                        $skuDepositAmount = floatval($item['goods_price']) * $depositRate;
                    }

                    // 将单个SKU的定金金额乘以数量，然后累加到总定金金额
                    $depositAmount += $skuDepositAmount * $quantity;
                }
            }

            // 如果没有计算出定金金额（可能是因为没有选中的商品），使用默认方式计算
            if ($depositAmount <= 0) {
                $depositRate = 0.3; // 定金比例，可以根据实际需求调整
                $depositAmount = $baseAmount * $depositRate;
            }

            // 定金金额不应超过最终金额
            $depositAmount = min($depositAmount, $finalAmount);
            $this->sum['deposit_amount'] = number_format($depositAmount, 3, '.', '');

            // 计算尾款 = 最终金额 - 定金
            // 各种服务费（增值服务费、节假日调整费等）自动计入尾款
            $remainingAmount = $finalAmount - $depositAmount;
            $this->sum['remaining_amount'] = number_format($remainingAmount, 3, '.', '');

            // 计算服务费总额（增值服务费 + 节假日调整费 + 运费 + 杂费）
            $serviceFees = floatval($this->sum['additional_services_fee']) +
                floatval($this->sum['festival_fee']) +
                floatval($this->sum['cost_freight']) +
                floatval($this->sum['other_fee']);
            $this->sum['service_fees'] = number_format($serviceFees, 3, '.', '');
        }
    }

    /**
     * 计算增值服务费用
     */
    protected function calculateAdditionalServices(): void
    {
        // 初始化增值服务费用
        $this->sum['additional_services_fee'] = '0.000';
        $this->sum['additional_services_items'] = [];

        // 如果没有增值服务，直接返回
        if (empty($this->additional_services)) {
            return;
        }

        // 计算增值服务费用
        $totalFee = 0;
        $items = [];

        foreach ($this->additional_services as $service) {
            // 验证服务数据
            if (!isset($service['service_name']) || !isset($service['price']) || !isset($service['quantity'])) {
                continue;
            }

            // 计算单项服务费用
            $price = floatval($service['price']);
            $quantity = intval($service['quantity']);
            $fee = $price * $quantity;
            $totalFee += $fee;

            // 记录服务项
            $items[] = [
                'service_name' => $service['service_name'],
                'price' => number_format($price, 3, '.', ''),
                'quantity' => $quantity,
                'fee' => number_format($fee, 3, '.', '')
            ];
        }

        // 更新增值服务费用
        $this->sum['additional_services_fee'] = number_format($totalFee, 3, '.', '');
        $this->sum['additional_services_items'] = $items;
    }

    /**
     * 预计算最终金额（不包含积分抵扣）
     */
    protected function preFinalAmount(): void
    {
        // 实收 = 商品总价 + 运费 + 杂费 + 增值服务费用 + 节假日调整费用 - 优惠券折扣
        $finalAmount = floatval($this->sum['total_amount']) +
            floatval($this->sum['cost_freight']) +
            floatval($this->sum['other_fee']) +
            floatval($this->sum['additional_services_fee']) +
            floatval($this->sum['festival_fee']) -
            floatval($this->sum['discount_coupon']);

        // 确保最终金额不为负数
        $finalAmount = max(0, $finalAmount);
        $this->sum['final_amount'] = number_format($finalAmount, 3, '.', '');
    }

    /**
     * 计算最终支付金额
     */
    protected function calculateFinalAmount(): void
    {
        // 注意：增值服务费用已经在calSum方法中计算过了
        // 实收 = 商品总价 + 运费 + 杂费 + 增值服务费用 + 节假日调整费用 - 优惠券折扣
        $finalAmount = floatval($this->sum['total_amount']) +
            floatval($this->sum['cost_freight']) +
            floatval($this->sum['other_fee']) +
            floatval($this->sum['additional_services_fee']) +
            floatval($this->sum['festival_fee']) -
            floatval($this->sum['discount_coupon']);

        // 确保最终金额不为负数
        $finalAmount = max(0, $finalAmount);
        $this->sum['final_amount'] = number_format($finalAmount, 3, '.', '');

        // 确保积分抵扣金额不超过最终金额
        if (isset($this->sum['score_u_amount']) && floatval($this->sum['score_u_amount']) > 0) {
            $scoreUAmount = floatval($this->sum['score_u_amount']);
            if ($scoreUAmount > $finalAmount) {
                // 如果积分抵扣金额超过最终金额，则限制为最终金额
                $this->sum['score_u_amount'] = number_format($finalAmount, 3, '.', '');

                // 同时需要调整使用的积分数量
                $exchangeRate = floatval($this->sum['point_exchange_rate']);
                if ($exchangeRate > 0) {
                    $this->sum['score_u'] = floor($finalAmount / $exchangeRate); // 最大金额除以每积分的价值
                }
            }
        }

        // 如果是定金支付方式，则当前只需要支付定金
        if ($this->payment_type == 2 && isset($this->sum['is_deposit']) && $this->sum['is_deposit'] == 1) {
            // 当前支付金额就是定金金额
            $depositAmount = floatval($this->sum['deposit_amount']);

            // 积分抵扣只能用于定金部分，尾款不参与积分抵扣
            $pointsAmount = floatval($this->sum['score_u_amount']);
            $advanceAmount = floatval($this->sum['advance']);

            // 限制积分抵扣不超过定金金额
            if ($pointsAmount > $depositAmount) {
                $pointsAmount = $depositAmount;
                // 更新积分抵扣金额
                $this->sum['score_u_amount'] = number_format($pointsAmount, 3, '.', '');

                // 更新使用的积分数量
                $exchangeRate = floatval($this->sum['point_exchange_rate']);
                if ($exchangeRate > 0) {
                    $this->sum['score_u'] = floor($pointsAmount / $exchangeRate);
                }
            }

            // 限制余额支付不超过定金金额减去积分抵扣后的金额
            $remainingDeposit = $depositAmount - $pointsAmount;
            if ($advanceAmount > $remainingDeposit) {
                $advanceAmount = $remainingDeposit;
            }

            $payedAmount = max(0, $depositAmount - $pointsAmount - $advanceAmount);

            $this->sum['payed'] = number_format($payedAmount, 3, '.', '');
            $this->sum['amount'] = $this->sum['payed'];

            // 计算可得积分（按照最终支付金额的1%，即100元获10积分）
            // 注意：定金支付时，积分应基于整个订单的最终金额计算（包括定金和尾款）
            // 需要减去积分抵扣和余额支付
            $totalPayedAmount = max(0, $finalAmount - floatval($this->sum['score_u_amount']) - floatval($this->sum['advance']));
            $this->sum['score_g'] = floor($totalPayedAmount / 10); // 100元获10积分，即每10元获1积分
        } else {
            // 如果不是定金支付方式，则支付总金额 = 实收金额 - 积分抵扣 - 余额支付
            $pointsAmount = floatval($this->sum['score_u_amount']);
            $advanceAmount = floatval($this->sum['advance']);

            // 限制积分抵扣和余额支付的总和不超过最终金额
            $totalDeduction = $pointsAmount + $advanceAmount;
            if ($totalDeduction > $finalAmount) {
                // 如果总抵扣超过最终金额，则按比例调整
                $ratio = $finalAmount / $totalDeduction;
                $pointsAmount = $pointsAmount * $ratio;
                $advanceAmount = $advanceAmount * $ratio;

                // 更新积分抵扣金额
                $this->sum['score_u_amount'] = number_format($pointsAmount, 3, '.', '');

                // 更新使用的积分数量
                $exchangeRate = floatval($this->sum['point_exchange_rate']);
                if ($exchangeRate > 0) {
                    $this->sum['score_u'] = floor($pointsAmount / $exchangeRate); // 最大金额除以每积分的价值
                }
            }

            $payedAmount = max(0, $finalAmount - $pointsAmount - $advanceAmount);
            $this->sum['payed'] = number_format($payedAmount, 3, '.', '');

            // 支付金额 = 支付总金额
            $this->sum['amount'] = $this->sum['payed'];

            // 计算可得积分（按照最终支付金额的1%，即100元获10积分）
            // 使用payed而不是final_amount，因为payed是实际支付金额（已经减去积分抵扣和余额支付）
            $payedAmountFloat = floatval($this->sum['payed']);
            $this->sum['score_g'] = floor($payedAmountFloat / 10); // 100元获10积分，即每10元获1积分
        }
    }

    public function applyGoodsIds(): bool
    {
        if (empty($this->goodsItems)) {
            return false;
        }
        $goodsId = [];
        $skuIds = [];
        foreach ($this->goodsItems as $item) {
            if ($item['selected'] == 0) {
                $this->invalid_ids[] = $item['goods_sku_id'];
            } else {
                $this->valid_ids[] = $item['goods_sku_id'];
                array_push($goodsId, $item['goods_id']);
                array_push($skuIds, $item['goods_sku_id']);
            }
        }

        if (empty($goodsId) || empty($skuIds)) {
            $this->goods_snapshot = [];
            return false;
        }
        // 去重复
        $goodsId = array_unique($goodsId);
        $skuIds = array_unique($skuIds);
        // 保存有效的商品快照
        $this->goods_snapshot = (new GoodsSer('Cart', ['goods_id' => $goodsId, 'sku_id' => $skuIds, 'area_id' => $this->area_id]))->getData();
        return true;
    }
}
