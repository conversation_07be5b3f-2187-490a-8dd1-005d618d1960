<?php

namespace app\api\service;

use app\api\service\Goods\GoodsSer;
use app\common\model\GoodsCategoryBasicModel;
use app\common\model\GoodsCategoryModel;
use app\common\model\GoodsModel;
use app\common\model\ShopShareStaffModel;
use app\common\model\StaffServiceModel;

class CategorySer extends BaseSer
{
    /**
     * 获取分类列表
     * @param int $cId 分类ID
     * @return array 返回包含子树
     */
    public static function getCategoryList(int $cId = 0, $levels = [], $serviceObject = null)
    {
        $map = GoodsCategoryBasicModel::getCategoryMap($levels);

        $pid = 0;
        if ($cId > 0) {
            $map = array_filter($map, function ($item) use ($cId) {
                return $item['c_id'] == $cId || $item['parent_id'] == $cId;
            });
            $pid = min(array_column($map, 'parent_id'));
        }

        // 遍历map， level>1,只展示service_object包含serviceObject
        if ($serviceObject) {
            $map = array_filter($map, function ($item) use ($serviceObject) {
                if ($item['level'] == 1) {
                    return true;
                }
                return strpos($item['service_object'] ?? '', $serviceObject) !== false;
            });
        }

        return arrayToTree($map, $pid, 'c_id', 'parent_id');
    }

    /**
     * 根据服务人员获取分类列表
     * @param int $id 服务人员id
     * @return array 返回包含子树
     */
    public static function getCategoryListByStaffId(int $id = 0): array
    {
        if ($id <= 0) {
            return [];
        }
        $map = GoodsCategoryBasicModel::getCategoryMap();
        $map = array_column($map, null, 'c_id');
        $serviceIds = StaffServiceModel::where('staff_id', $id)->field('service_id')->column('service_id');
        $rows = GoodsCategoryModel::where('c_id', 'in', $serviceIds)
            ->field('c_id,first_category_id,second_category_id,third_category_id')
            ->select();
        if ($rows->isEmpty()) {
            return [];
        }
        $categories = [];
        $third = [];
        $three = [];
        $cidEqThird = [];
        foreach ($rows as $row) {
            if (!isset($map[$row->third_category_id])) {
                continue;
            }
            $categories[$row->first_category_id] = $map[$row->first_category_id];
            $categories[$row->second_category_id] = $map[$row->second_category_id];
            $categories[$row->third_category_id] = $map[$row->third_category_id];
            $three[] = [
                'c_id' => $row->c_id,
                'first_cid' => $row->first_category_id,
                'second_cid' => $row->second_category_id,
                'three_cid' => $row->third_category_id,
                'three_name' => $map[$row->third_category_id]['name'],
                'children' => [],
            ];
            array_push($third, $row->third_category_id);
            $cidEqThird[$row->c_id] = $row->third_category_id;
        }
        $tree = arrayToTree($categories, 0, 'c_id', 'parent_id');
        // 追加分享的商品
        $shareShopIds = ShopShareStaffModel::where('staff_id', $id)->where('status', 1)->field('to_shop_id')->column('to_shop_id');
        if (!empty($shareShopIds)) {
            // 查找相等的分类
            $shareCidList = GoodsCategoryModel::where('shop_id', 'in', $shareShopIds)
                ->where('third_category_id', 'in', $third)
                ->field('c_id,third_category_id')
                ->select()
                ->toArray();
            $shareCids = [];
            foreach ($shareCidList as $v) {
                $cidEqThird[$v['c_id']] = $v['third_category_id'];
                $shareCids[] = $v['c_id'];
            }
            $serviceIds = array_merge($serviceIds, $shareCids);
        }
        // 查找商品
        $rows =  (new GoodsSer('List', ['category_id' => $serviceIds]))->getData();
        $temp = [];
        foreach ($rows as $v) {
            $thirdId =  $cidEqThird[$v['category_id']] ?? 0;
            $temp[$thirdId][] = $v;
        }
        foreach ($three as &$item) {
            // $item['children'] = isset($temp[$item['c_id']]) ? array_slice($temp[$item['c_id']], 0, 3) : [];
            $item['children'] = isset($temp[$item['three_cid']]) ? $temp[$item['three_cid']] : [];
        }
        unset($item);

        return [
            'categories' => $tree,
            'goods' => $three,
        ];
    }

    /**
     * 获取分类商品id
     *
     * 第一页请求
     * $result = YourClass::getCategoryGoodsForShop($cid, 3000, 10);

     * 第二页请求（使用返回的游标）
     * $result = YourClass::getCategoryGoodsForShop(
     *    $cid,
     *     3000,
     *     10,
     *     $previousResult['next_cursor']['last_distance'],
     *     $previousResult['next_cursor']['last_goods_sort'],
     *     $previousResult['next_cursor']['last_id']
     * );
     * $lastDistance: 上一页最后一条记录的距离
     * $lastGoodsSort: 上一页最后一条记录的商品排序值
     * $lastId: 上一页最后一条记录的商品ID（确保唯一性）
     */
    public static function getCategoryGoodsForShop(array $cid = [], $distance = 3000, $pageSize = 10, $lastDistance = null, $lastGoodsSort = null, $lastId = null)
    {
        // 计算经纬度范围
        $range = self::calculateSurroundingRange(
            self::$position['latitude'],
            self::$position['longitude'],
            $distance
        );

        // 构建基础查询
        $query = GoodsModel::alias('g')
            ->join('shop s', 'g.shop_id = s.id')
            ->field('g.goods_id,g.goods_sort,
            ROUND(6378.138 * 2 * ASIN(SQRT(
                POW(SIN((' . self::$position['latitude'] . '*PI()/180 - s.latitude*PI()/180)/2),2) +
                COS(' . self::$position['latitude'] . '*PI()/180) * COS(s.latitude*PI()/180) *
                POW(SIN((' . self::$position['longitude'] . '*PI()/180 - s.longitude*PI()/180)/2),2)
            ))*1000) AS distance')
            ->where('g.audit_status', 1)
            ->where('g.goods_status', 10)
            ->where('g.is_delete', 0)
            ->whereIn('g.category_id', $cid)
            // ->where('s.audit_status', 1) //门店审核暂时关闭
            ->where('s.status', 1)
            // ->whereBetween('s.latitude', [$range['minLat'], $range['maxLat']]) 
            // ->whereBetween('s.longitude', [$range['minLng'], $range['maxLng']])
            // ->having('distance <= ' . $distance) //距离限制暂时关闭
            ->order('distance ASC, g.goods_sort ASC, g.goods_id ASC') // 添加goods_id作为最后排序条件
            ->limit($pageSize);

        // 添加游标分页条件
        // if ($lastDistance !== null && $lastGoodsSort !== null && $lastId !== null) {
        //     $query->where(function ($q) use ($lastDistance, $lastGoodsSort, $lastId) {
        //         $q->where('distance', '>', $lastDistance)
        //             ->orWhere(function ($q2) use ($lastDistance, $lastGoodsSort) {
        //                 $q2->where('distance', '=', $lastDistance)
        //                     ->where('g.goods_sort', '>', $lastGoodsSort);
        //             })
        //             ->orWhere(function ($q3) use ($lastDistance, $lastGoodsSort, $lastId) {
        //                 $q3->where('distance', '=', $lastDistance)
        //                     ->where('g.goods_sort', '=', $lastGoodsSort)
        //                     ->where('g.goods_id', '>', $lastId);
        //             });
        //     });
        // }
        // 添加游标分页条件(限制距离打开，分页无效)
        if ($lastDistance !== null) {
            // 确保所有变量都有有效值，并转换为适当的类型
            $lastDistance = floatval($lastDistance);

            // 构建 HAVING 子句
            $havingConditions = [];

            // 条件1: distance > lastDistance
            $havingConditions[] = "distance > {$lastDistance}";

            // 条件2: distance = lastDistance AND goods_sort > lastGoodsSort
            if ($lastGoodsSort !== null) {
                $lastGoodsSort = intval($lastGoodsSort);
                $havingConditions[] = "(distance >= {$lastDistance} AND g.goods_sort > {$lastGoodsSort})";
            }

            // 条件3: distance = lastDistance AND goods_sort = lastGoodsSort AND goods_id > lastId
            if ($lastGoodsSort !== null && $lastId !== null) {
                $lastId = intval($lastId);
                $havingConditions[] = "(distance >= {$lastDistance} AND g.goods_sort >= {$lastGoodsSort} AND g.goods_id > {$lastId})";
            }

            // 将所有条件用 OR 连接起来
            if (!empty($havingConditions)) {
                $query->having(implode(' OR ', $havingConditions));
            }
        }

        $goodsList = $query->select()->toArray();
        // echo $query->getLastSql();
        // exit;

        // 获取下一页的游标
        $nextCursor = null;
        if (!empty($goodsList)) {
            $lastItem = end($goodsList);
            $nextCursor = [
                'last_distance' => $lastItem['distance'],
                'last_goods_sort' => $lastItem['goods_sort'],
                'last_id' => $lastItem['goods_id']
            ];
        }

        return [
            'list' => $goodsList,
            'next_cursor' => $nextCursor
        ];
    }

    /**
     * 猜你喜欢功能 - 获取相同分类不限制区域的商品
     *
     * @param int $categoryId 分类ID
     * @param int $limit 返回数量限制
     * @param int $excludeGoodsId 排除的商品ID（如当前正在浏览的商品）
     * @return array 商品列表
     */
    public static function getRecommendedGoods(array $categoryId = [], int $limit = 10, int $excludeGoodsId = 0)
    {
        if ($categoryId <= 0) {
            return [];
        }

        // 获取分类信息
        $categoryInfo = GoodsCategoryModel::whereIn('c_id', $categoryId)
            ->field('c_id,first_category_id,second_category_id,third_category_id')
            ->findOrEmpty();

        if ($categoryInfo->isEmpty()) {
            return [];
        }

        // 构建查询
        $query = GoodsModel::alias('g')
            ->join('shop s', 'g.shop_id = s.id')
            ->field('g.goods_id')
            ->where('g.audit_status', 1)
            ->where('g.goods_status', 10)
            ->where('g.is_delete', 0)
            ->where('s.audit_status', 1)
            ->where('s.status', 1)
            ->order('g.goods_sort ASC, g.goods_id ASC');

        // 如果有指定分类，则优先使用相同的三级分类
        if ($categoryInfo->third_category_id > 0) {
            // 查找所有使用相同三级分类的商品分类
            $sameCategoryIds = GoodsCategoryModel::where('third_category_id', $categoryInfo->third_category_id)
                ->column('c_id');

            if (!empty($sameCategoryIds)) {
                $query->where('g.category_id', 'in', $sameCategoryIds);
            } else {
                // 如果没有找到相同三级分类，则使用原始分类
                $query->where('g.category_id', $categoryId);
            }
        } else {
            // 如果没有三级分类，则使用原始分类
            $query->where('g.category_id', $categoryId);
        }

        // 排除当前商品
        if ($excludeGoodsId > 0) {
            $query->where('g.goods_id', '<>', $excludeGoodsId);
        }

        // 限制返回数量
        $query->limit($limit);

        // 执行查询
        $goodsList = $query->select()->toArray();
        return $goodsList;
    }

    public static function getCatIdsByServiceObject($serviceObject = '', $firstId = 0)
    {
        $dd = [
            'baby' => [
                ['居家营养', '婴幼儿', '体质监测评估'],
                ['居家护理', '婴幼儿', '幼儿照护'],
                // ['居家护理', '婴幼儿', '婴幼儿洗护'],
                ['居家健康', '婴幼儿', '小儿推拿'],
                ['居家教育', '早教到家', '幼儿绘艺启装课'],
                ['居家娱乐', '婴幼儿', '宝宝摄影'],
            ],
            'adult' => [
                ['居家营养', '新妈妈', '体质监测评估'],
                ['居家护理', '新妈妈', '孕期陪诊'],
                ['居家健康', '新妈妈', '孕期护理'],
                ['居家教育', '早教到家', '幼儿绘艺启装课'],
                ['居家娱乐', '新妈妈', '孕期写真'],
            ],
        ];
        $data = $dd[$serviceObject] ?? [];
        if (empty($data)) {
            return [];
        }
        $first = GoodsCategoryBasicModel::getCategoryMap([1]);
        $firstByName = array_column($first, null, 'name');
        $scend = GoodsCategoryBasicModel::getCategoryMap([2]);
        $third = GoodsCategoryBasicModel::getCategoryMap([3]);
        $newData = [];
        foreach ($data as $k => $v) {
            $one = $firstByName[$v[0]];
            $newData[$k][0] = $one['c_id'];
            $newData[$k][1] = 0;
            $newData[$k][2] = 0;
            foreach ($scend as $scendItem) {
                if ($scendItem['parent_id'] == $one['c_id'] && $scendItem['name'] == $v[1]) {
                    $newData[$k][1] = $scendItem['c_id'];
                }
            }
            foreach ($third as $thirdItem) {
                if ($thirdItem['parent_id'] == $newData[$k][1] && $thirdItem['name'] == $v[2]) {
                    $newData[$k][2] = $thirdItem['c_id'];
                }
            }
        }
        if ($firstId > 0) {
            foreach ($newData as $k => $v) {
                if ($v[0] == $firstId) {
                    return [$v][0];
                }
            }
            return [];
        }
        return $newData[0];
    }

    /**
     * 过滤分类，只保留指定ID及其父类
     *
     * @param array $categories 分类数组
     * @param array $serviceIds 要保留的服务ID数组
     * @return array 过滤后的分类数组
     */
    public static function filterCategories($categories, $serviceIds)
    {
        // 用于存储需要保留的分类ID（包括父类ID）
        $keepIds = [];

        // 递归查找指定ID及其父类
        self::findCategoryAndParents($categories, $serviceIds, $keepIds);

        // 过滤分类树，只保留需要的分类
        $result = self::filterCategoryTree($categories, $keepIds);

        return $result;
    }

    /**
     * 递归查找分类及其父类
     *
     * @param array $categories 分类数组
     * @param array $targetIds 目标ID数组
     * @param array &$keepIds 需要保留的ID数组（引用传递，会被修改）
     * @param array $path 当前路径上的分类ID
     */
    public static function findCategoryAndParents($categories, $targetIds, &$keepIds, $path = [])
    {
        foreach ($categories as $category) {
            // 当前分类的路径
            $currentPath = array_merge($path, [$category['c_id']]);

            // 如果当前分类ID在目标ID中，将整个路径添加到保留ID中
            if (in_array($category['c_id'], $targetIds)) {
                foreach ($currentPath as $id) {
                    if (!in_array($id, $keepIds)) {
                        $keepIds[] = $id;
                    }
                }
            }

            // 如果有子分类，递归处理
            if (!empty($category['children'])) {
                self::findCategoryAndParents($category['children'], $targetIds, $keepIds, $currentPath);
            }
        }
    }

    /**
     * 过滤分类树，只保留指定ID的分类及其子分类
     *
     * @param array $categories 分类数组
     * @param array $keepIds 需要保留的ID数组
     * @return array 过滤后的分类数组
     */
    public static function filterCategoryTree($categories, $keepIds)
    {
        $result = [];

        foreach ($categories as $category) {
            // 如果当前分类ID在保留列表中
            if (in_array($category['c_id'], $keepIds)) {
                // 创建当前分类的副本
                $newCategory = $category;

                // 如果有子分类，递归过滤子分类
                if (!empty($category['children'])) {
                    $newCategory['children'] = self::filterCategoryTree($category['children'], $keepIds);
                }

                $result[] = $newCategory;
            }
        }

        return $result;
    }

    /**
     * 计算指定距离范围内的经纬度边界
     *
     * @param float $lat 中心点纬度
     * @param float $lng 中心点经度
     * @param float $distance 距离(单位：米)
     * @return array 包含[minLat, maxLat, minLng, maxLng]的数组
     */
    public static function calculateSurroundingRange($lat, $lng, $distance = 3000)
    {
        // 地球半径(单位：米)
        $earthRadius = 6378137;

        // 将距离转换为弧度
        $radDist = $distance / $earthRadius;

        // 计算纬度范围
        $minLat = $lat - rad2deg($radDist);
        $maxLat = $lat + rad2deg($radDist);

        // 计算经度范围(考虑纬度变化)
        $radLat = deg2rad($lat);
        $deltaLng = asin(sin($radDist) / cos($radLat));
        $minLng = $lng - rad2deg($deltaLng);
        $maxLng = $lng + rad2deg($deltaLng);

        return [
            'minLat' => $minLat,
            'maxLat' => $maxLat,
            'minLng' => $minLng,
            'maxLng' => $maxLng
        ];
    }
}
