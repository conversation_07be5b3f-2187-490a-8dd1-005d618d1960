<?php

declare(strict_types=1);

namespace app\api\service\Order;

use app\api\service\BaseSer;
use app\api\service\Carts\CartsDirectorSer;
use app\common\enum\NotifyScene;
use app\common\model\GoodsModel;
use app\common\model\InterviewFeedbackModel;
use app\common\model\InterviewScheduleModel;
use app\common\model\InterviewSchedulesModel;
use app\common\model\MemberModel;
use app\common\model\OrderItemsModel;
use app\common\model\OrderOccupancyModel;
use app\common\model\OrderPaymentsModel;
use app\common\model\OrderRefundApplyModel;
use app\common\model\OrdersModel;
use app\common\model\ShopModel;
use app\common\model\StaffModel;
use app\common\model\TransactionModel;
use app\common\service\CouponService;
use app\common\service\OrderService;
use app\common\service\RegionService;
use app\common\service\StaffService;
use app\common\service\UploadService;
use DateTime;
use Exception;
use think\facade\Cache;
use think\facade\Db;
use think\facade\Event;
use think\facade\Log;

class OrderSer extends BaseSer
{
    //返回订单详情信息
    public static function orderDetail(int $memberId = 0, string $orderId = ""): array
    {
        $order = OrdersModel::with(['items', 'payments', 'service'])
            ->where('order_id', $orderId)
            ->where('member_id', $memberId)
            ->findOrEmpty();
        if ($order->isEmpty()) {
            return [];
        }
        $ret = self::buildOrder($order);
        return $ret;
    }

    public static function orderList(int $memberId = 0, $selected = 'all', $lasttime = ''): array
    {
        $where = [
            ['member_id', '=', $memberId],
        ];
        if ($selected == 'wait_pay') {
            $where[] = ['pay_status', '=', OrdersModel::PayStatusUnpaid];
            $where[] = ['status', '=', OrdersModel::StatusActive];
        }
        if ($selected == 'wait_confirm') {
            $where[] = ['pay_status', 'in', [OrdersModel::PayStatusPaid, OrdersModel::PayStatusPaidToGuarantee, OrdersModel::PayStatusPartPaid]];
            $where[] = ['confirm', '=', OrdersModel::ConfirmUnconfirmed];
            $where[] = ['status', '=', OrdersModel::StatusActive];
        }
        if ($selected == 'wait_service') {
            $where[] = ['pay_status', 'in', [OrdersModel::PayStatusPaid, OrdersModel::PayStatusPaidToGuarantee, OrdersModel::PayStatusPartPaid]];
            $where[] = ['confirm', '>', OrdersModel::ConfirmUnconfirmed];
            $where[] = ['status', '=', OrdersModel::StatusActive];
            $where[] = ['start_service_time', '=', null];
        }
        if ($selected == 'service') {
            $where[] = ['pay_status', 'in', [OrdersModel::PayStatusPaid, OrdersModel::PayStatusPaidToGuarantee, OrdersModel::PayStatusPartPaid]];
            $where[] = ['confirm', '=', OrdersModel::ConfirmContractConfirmed];
            $where[] = ['status', '=', OrdersModel::StatusActive];
            $where[] = ['start_service_time', '<', date('Y-m-d H:i:s')];
        }
        if ($selected == 'finish') {
            $where[] = ['status', '=', OrdersModel::StatusFinish];
        }
        if ($lasttime != '') {
            $where[] = ['create_time', '<', date('Y-m-d H:i:s', strtotime($lasttime))];
        }
        $orders = OrdersModel::where($where)
            ->order('create_time', 'desc')
            ->limit(config('api.limit'))
            ->select();
        if ($orders->isEmpty()) {
            return [];
        }

        $res = [];
        foreach ($orders as $order) {
            array_push($res, self::buildOrder($order));
        }
        return $res;
    }

    private static function buildOrder(OrdersModel $order)
    {
        $cart         = $order->addon;
        $shop         = $cart['shop_snapshot'] ?? [];
        $region       = (new RegionService)->getRegionDetail($shop['area_id'] ?? 0) ?? [];
        $shop['area'] = [
            'province_name' => $region['province_name'] ?? '',
            'city_name'     => $region['city_name'] ?? '',
            'area_name'     => $region['district_name'] ?? '',
        ];
        $formatStatus = '';
        $formatCode   = '';
        if ($order->status == OrdersModel::StatusActive) {
            $formatStatus = '进行中';
            $formatCode   = 'active';
            if ($order->pay_status == OrdersModel::PayStatusUnpaid) {
                $formatStatus = '待支付';
                $formatCode   = 'wait_pay';
            } else {
                if ($order->confirm == OrdersModel::ConfirmUnconfirmed) {
                    $formatStatus = '待确认';
                    $formatCode   = 'wait_confirm';
                } else {
                    if ($order->start_service_time == null) {
                        $formatStatus = '待服务';
                        $formatCode   = 'wait_service';
                    } else {
                        $formatStatus = '服务中';
                        $formatCode   = 'service';
                    }
                }
            }
        } elseif ($order->status == OrdersModel::StatusFinish) {
            $formatStatus = '已完成';
            $formatCode   = 'finish';
        } elseif ($order->status == OrdersModel::StatusDead) {
            $formatStatus = '已取消';
            $formatCode   = 'dead';
        } else {
            $formatStatus = '未知状态';
            $formatCode   = 'unknown';
        }
        $itemList   = [];
        $goodsItems = $cart['cart']['goodsItems'] ?? [];
        foreach ($goodsItems as $item) {
            if ($item['selected'] == 1) {
                $itemList[$item['goods_id']][$item['goods_sku_id']] = $item;
            }
        }
        $goods = $cart['goods_snapshot'] ?? [];
        if (!empty($goods)) {
            foreach ($goods as &$val) {
                $sku = $val['sku'] ?? [];
                if (!empty($sku)) {
                    foreach ($sku as $k => $v) {
                        $quantity                       = intval($itemList[$v['id']][$v['sku_id']]['quantity'] ?? 0);
                        $price                          = $itemList[$v['id']][$v['sku_id']]['goods_price'] ?? 0;
                        $val['sku'][$k]['quantity']     = $quantity;
                        $val['sku'][$k]['total_amount'] = $quantity * $price;
                    }
                }
                $val['refund_rule'] = GoodsModel::where(['goods_id' => $val['goods_id']])->value('refund_rule') ?? '';
            }
            unset($val);
        }

        // 获取最后一条面试时间
        $interview = InterviewSchedulesModel::where('order_id', $order->order_id)
            ->field('id,interview_time,start_time,start_time,end_time,room_id,room_name,status')
            ->order('create_time', 'desc')
            ->limit(1)
            ->select()
            ->toArray();

        if (!empty($interview)) {
            $interview = $interview[0];
        }
        // 获取支付金额
        $orderPayments = OrderPaymentsModel::where('order_id', $order->order_id)
            ->field('id, amount, status, payment_phase')
            ->order('create_time', 'desc')
            ->select()
            ->toArray();

        $pay   = 0; // 待支付
        $payed = 0; // 已支付

        // 根据支付方式计算待支付和已支付金额
        if ($order->payment_type == 1) {
            // 全额支付方式，直接使用支付计划中的金额
            if (!empty($orderPayments)) {
                foreach ($orderPayments as $payment) {
                    if ($payment['status'] == 'pending') {
                        $pay += $payment['amount'];
                    }
                    if ($payment['status'] == 'paid') {
                        $payed += $payment['amount'];
                    }
                }
            }
        } else if ($order->payment_type == 2) {
            // 定金+尾款支付方式
            if ($order->deposit_pay_status == 0) {
                // 定金未支付，待支付金额为定金金额
                // 查找定金支付计划
                foreach ($orderPayments as $payment) {
                    if ($payment['payment_phase'] == 'deposit' && $payment['status'] == 'pending') {
                        $pay = $payment['amount'];
                        break;
                    }
                }
            } else {
                // 定金已支付，待支付金额为尾款金额减去系统减免金额
                // 查找尾款支付计划
                foreach ($orderPayments as $payment) {
                    if ($payment['payment_phase'] == 'final' && $payment['status'] == 'pending') {
                        $pay = $payment['amount'];
                        break;
                    }
                }

                // 查找系统折扣支付计划
                $systemDiscount = 0;
                foreach ($orderPayments as $payment) {
                    if ($payment['payment_phase'] == 'discount' && $payment['status'] == 'pending') {
                        // 系统折扣金额应该从尾款中扣除
                        $systemDiscount = floatval($payment['amount']);
                        // 如果系统折扣为正数，表示减免；如果为负数，表示增加
                        if ($order->system_discount > 0) {
                            $pay = max(0, $pay - $systemDiscount);
                        } else if ($order->system_discount < 0) {
                            $pay = $pay + $systemDiscount;
                        }
                        break;
                    }
                }

                // 如果没有找到系统折扣支付计划，但订单有系统折扣，直接使用订单的系统折扣
                if ($systemDiscount == 0 && $order->system_discount != 0) {
                    $systemDiscount = abs(floatval($order->system_discount));
                    if ($order->system_discount > 0) {
                        $pay = max(0, $pay - $systemDiscount);
                    } else if ($order->system_discount < 0) {
                        $pay = $pay + $systemDiscount;
                    }
                }

                // 已支付金额为定金金额
                foreach ($orderPayments as $payment) {
                    if ($payment['status'] == 'paid') {
                        $payed += $payment['amount'];
                    }
                }
            }
        }

        // 确保支付金额不会为负数
        $pay   = max(0, $pay);
        $payed = max(0, $payed);
        $res   = [
            'order_id'                    => $order->order_id,
            'create_time'                 => $order->create_time,
            'address'                     => $cart['cart']['address'] ?? [],
            'shop'                        => $shop,
            'goods'                       => $goods,
            'staff'                       => $cart['staff_snapshot'] ?? '',
            'sum'                         => $cart['cart']['sum'] ?? [],
            'service_time'                => $order->promotion_type == 'normal' ? substr($order->service_time, 0, 10) : $order->service_time,
            'service_time_confirm_status' => $order->service_time_confirm_status, //用户服务时间确定，0-待用户确认；1-用户已确认（默认）；2-系统确认
            'service_mode'                => is_numeric($order->service_mode) ? $order->service_mode : OrdersModel::ServiceModeOld[$order->service_mode],
            'start_service_time'          => $order->start_service_time,
            'end_service_time'            => $order->end_service_time,

            'pay_status' => is_numeric($order->pay_status) ? $order->pay_status : OrdersModel::PayStatusOld[$order->pay_status],

            'is_delivery' => $order->is_delivery,

            'ship_status' => $order->ship_status,

            'status' => $order->status,

            'confirm' => $order->confirm,

            'format_status'        => $formatStatus,
            'format_code'          => $formatCode,
            'interview_time'       => $interview['interview_time'] ?? '',
            'room_id'              => $interview['room_id'] ?? '',
            'room_name'            => $interview['room_name'] ?? '',
            'interview_status'     => $interview['status'] ?? '',
            'memo'                 => $order->memo,
            'pay'                  => $pay, //待支付金额
            'payed'                => $payed, //已支付金额
            'promotion_type'       => $order->promotion_type,
            'has_deposit'          => $order->has_deposit,
            'need_contract'        => $order->need_contract,
            'contract_template_id' => $order->contract_template_id,
            'contract_status'      => $order->contract_status,
        ];
        return $res;
    }

    /**
     * 根据订单获取购物车参数
     * 用于修改订单基础数据
     */
    private static function getCartParamByOrder(OrdersModel $order): array
    {
        $addon      = $order->addon;
        $cartType   = array_flip(OrdersModel::PromotionType)[$order->promotion_type] ?? OrdersModel::NORMAL;
        $goodsId    = 0;
        $skuIds     = '';
        $quantities = '';
        foreach ($addon['cart']['goodsItems'] as $item) {
            $goodsId    = $item['goods_id'];
            $skuIds     = $skuIds . $item['goods_sku_id'] . ',';
            $quantities = $quantities . $item['quantity'] . ',';
        }
        return [
            'member_id'    => $order->member_id,
            'cartType'     => $cartType,
            'goods_id'     => $goodsId,
            'sku_ids'      => substr($skuIds, 0, -1),
            'quantities'   => substr($quantities, 0, -1),
            'reqPage'      => 2,
            'addr_id'      => $addon['cart']['address']['id'] ?? 0,
            'service_date' => substr($order->service_time, 0, 10),
            'staff_id'     => $addon['staff_snapshot']['id'] ?? 0,
        ];
    }

    // 订单取消
    public static function orderCancel(array $param = [], string &$msg = ''): bool
    {
        $param['reason'] = $param['reason'] ?? '客户自行取消';
        $order           = OrdersModel::where('order_id', $param['order_id'])->findOrEmpty();
        if ($order->isEmpty()) {
            $msg = '订单不存在';
            return false;
        }
        if ($order->pay_status != OrdersModel::PayStatusUnpaid) {
            $msg = '订单已支付，无法取消';
            return false;
        }
        Db::startTrans();
        try {
            //1.取消订单（订单状态）
            $order->status = OrdersModel::StatusDead;
            $order->save();

            //2.取消支付计划
            \app\common\model\OrderPaymentsModel::where('order_id', $param['order_id'])
                ->where('status', 'pending')
                ->update(['status' => 'cancelled', 'update_time' => date('Y-m-d H:i:s')]);

            //3.删除面试排期记录
            InterviewScheduleModel::where(['order_id' => $param['order_id'], 'status' => 'scheduled'])->delete();

            //4.删除定时任务创建视频面试房间记录
            $tag = 'InterviewService_createRoom_' . $param['order_id'];
            TransactionModel::where(['tag' => $tag, 'status' => 0])->delete();

            //5.订单用户主动取消,冻结库存释放,thaw_type=2
            if (!empty($order->items())) {
                foreach ($order->items() as $sku) {
                    self::stockUnfreeze((int)$order->goods_id, (int)$sku->goods_sku_id, (string)$order->order_id, (int)$sku->goods_num, 2);
                }
            }

            //6.取消优惠券
            if ($order->used_coupon_id > 0){
                $cCoupon   = [
                    'id'        => $order->used_coupon_id,
                    'member_id' => $order->member_id,
                    'status'    => 'UNUSED',
                ];
                $result = CouponService::updateCoupon($cCoupon, $param['token'], "api");
                if ($result['code'] != 1) {
                    throw new Exception($result['msg']);
                    return false;
                }
            }

            //7.取消积分
            if($order->score_u > 0) {
                 // 扣除积分 
                $config = config('api.services.Member');
                $url = $config['BaseURL'] . $config['Api']['OrderCancelIntegral'];
                $cCoupon = [
                    'member_id' => $order->member_id,
                    'order_id' => $order->order_id,
                    'points' => $order->score_u
                ];
                $res = curlPostApiContentByUrlAndParams($url, $cCoupon, ['Authorization: Bearer ' . $param['token']], false);
                if (empty($res) || $res['code'] != 1) {
                    throw new Exception($res['msg']);
                    return false;
                }
            }

            // 记录日志
            \think\facade\Log::info('Order cancelled: ' . $param['order_id'] . ', reason: ' . $param['reason']);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            $msg = $e->getMessage() . '，订单取消失败';
            \think\facade\Log::error('Order cancel failed: ' . $param['order_id'] . ', error: ' . $e->getMessage());
            Db::rollback();
            return false;
        }
    }

    public static function getInterviewTimeByStaff(array $param = [], string &$msg = ''): array
    {
        $staffId = $param['staff_id'] ?? 0;
        if ($staffId == 0) {
            $msg = '请选择面试者';
            return [];
        }
        $day = $param['day'] ?? '';
        if (empty($day)) {
            $msg = '请选择日期';
            return [];
        }
        $param['member_id'] = 7;

        $start = strtotime($day);
        $end   = strtotime($day . ' +1 day');
        $list  = InterviewScheduleModel::whereRaw('interviewer_id=' . $staffId . ' Or interviewer_member_id=' . $param['member_id'])
            ->whereTime('interview_time', 'between', [date('Y-m-d', $start), date('Y-m-d', $end)])
            // ->where('status', 'scheduled')
            ->field('interview_time')
            ->select()
            ->toArray();

        $hourList = [];
        if (!empty($list)) {
            foreach ($list as $item) {
                $h = date('H', strtotime($item['interview_time']));
                array_push($hourList, intval($h));
                if (intval($h) > 0) {
                    // 前后1小时
                    array_push($hourList, $h - 1);
                }
                array_push($hourList, $h + 1);
            }
        }

        //把今天过期的时间段去掉
        if (date('Y-m-d') == $day) {
            $nowHour = date('H') + 1; // 当前时间加1小时
            for ($i = 0; $i < 24; $i++) {
                if ($nowHour >= $i) {
                    array_push($hourList, $i);
                }
            }
        }
        // 去重复
        $hourList = array_unique($hourList);
        // 排序
        sort($hourList);
        return $hourList;
    }

    public static function getInterviewListByMember(array $param = [], string $selected = 'all', string &$msg = ''): array
    {
        $where = [
            ['interviewer_member_id', '=', $param['member_id']]
        ];
        switch ($selected) {
            case 'wait_interview':
                $where[] = ['status', '=', 'scheduled'];
                break;
            case 'wait_confirm':
                $where[] = ['status', '=', 'in_progress'];
                break;
            case 'finished':
                $where[] = ['status', '=', 'completed'];
                break;
        }
        if ($param['interview_time']) {
            $where[] = ['interview_time', '<', date('Y-m-d H:i:s', strtotime($param['interview_time']))];
        }
        $list = InterviewScheduleModel::where($where)
            ->field('id,order_id,interviewer_id,interview_time,start_time,end_time,room_id,room_name,status')
            ->order('interview_time', 'desc')
            ->limit(100)
            ->select()
            ->toArray();
        if (empty($list)) {
            return [];
        }

        $orderIds  = array_column($list, 'order_id');
        $orders    = OrdersModel::where('order_id', 'in', $orderIds)->field('order_id, addon, service_time, start_service_time,end_service_time')->select()->toArray();
        $orderList = array_column($orders, null, 'order_id');
        foreach ($list as &$item) {
            $order = $orderList[$item['order_id']] ?? [];
            $addon = !empty($order['addon']) ? $order['addon'] : [];
            switch ($item['status']) {
                case 'scheduled':
                    $item['format_status'] = [
                        'text' => '待面试',
                        'icon' => 'info',
                    ];
                    if ($item['interview_time'] < date('Y-m-d H:i:s')) {
                        $item['format_status'] = [
                            'text' => '面试超时',
                            'icon' => 'danger',
                        ];
                    }
                    break;
                case 'in_progress':
                    $item['format_status'] = [
                        'text' => '面试中',
                        'icon' => 'warning',
                    ];
                    break;
                case 'completed':
                    $item['format_status'] = [
                        'text' => '面试完成',
                        'icon' => 'success',
                    ];
                    break;
                case 'cancelled':
                    $item['format_status'] = [
                        'text' => '面试取消',
                        'icon' => 'danger',
                    ];
                    break;
            }
            $item['goods_snapshot'] = $addon['goods_snapshot'] ?? [];
            $item['staff_snapshot'] = $addon['staff_snapshot'] ?? [];
            $item['shop_snapshot']  = $addon['shop_snapshot'] ?? [];
            //service_time, start_service_time,end_service_time
            $item['service_time']       = $order['service_time'] ?? '';
            $item['start_service_time'] = $order['start_service_time'] ?? '';
            $item['end_service_time']   = $order['end_service_time'] ?? '';
        }
        unset($item);
        return $list;
    }

    // 订单面试
    public static function startInterview(array $param = [], string &$msg = ''): bool
    {
        $order = OrdersModel::where('order_id', $param['order_id'])->findOrEmpty();
        if ($order->isEmpty()) {
            $msg = '订单不存在';
            return false;
        }
        if ($order->requires_interview == 0) {
            $msg = '订单无需面试';
            return false;
        }

        // 查找面试者是否有预约
        $row = OrderItemsModel::where('order_id', $param['order_id'])
            ->field('interviewer_staff_id')
            ->findOrEmpty();
        if ($row->isEmpty()) {
            $msg = '订单不存在';
            return false;
        }
        if ($row->interviewer_staff_id == 0) {
            $msg = '订单无需面试';
            return false;
        }

        // 面试者与面试官，有多个面试单，合并面试
        $interviewerId       = $row->interviewer_staff_id;
        $interviewerMemberId = $order->member_id;
        $exist               = InterviewScheduleModel::where('interviewer_id', $interviewerId)
            ->where('interviewer_member_id', $interviewerMemberId)
            ->where('status', 'scheduled')
            ->findOrEmpty();

        if ($exist->isEmpty()) {
            $msg = '订单无需面试';
            return false;
        }
        //预约面试时间是通知作用
        //合并面试
        InterviewScheduleModel::where('interviewer_id', $interviewerId)
            ->where('interviewer_member_id', $interviewerMemberId)
            ->where('status', 'scheduled')
            ->update([
                'start_time' => date('Y-m-d H:i:s'),
                'room_id'    => $param['room_id'] ?? 0, //一定要有房间，门店或者管理者不用判断直接进入
                'status'     => 'in_progress',
            ]);
        return true;
    }

    // 面试结束
    public static function endInterview(array $param = [], string &$msg = ''): bool
    {
        $row = InterviewScheduleModel::where('order_id', $param['order_id'])
            ->where('status', 'in_progress')
            ->findOrEmpty(); //面试单

        if ($row->isEmpty()) {
            $msg = '面试不存在';
            return false;
        }
        Db::startTrans();
        try {
            // 面试者与面试官，有多个面试单，合并面试
            $interviewerId       = $row->interviewer_id;
            $interviewerMemberId = $row->interviewer_member_id;
            //合并结果
            InterviewScheduleModel::where('interviewer_id', $interviewerId)
                ->where('interviewer_member_id', $interviewerMemberId)
                ->where('status', 'in_progress')
                ->update([
                    'end_time' => date('Y-m-d H:i:s'),
                    'status'   => 'completed',
                ]);
            if (isset($param['feedback_status']) && in_array($param['feedback_status'], [1, 2])) {
                InterviewFeedbackModel::insert([
                    'order_id'        => $row['order_id'],
                    'feedback_text'   => $param['feedback_text'] ?? '',
                    'feedback_status' => $param['feedback_status'],
                    'created_type'    => 'member',
                    'created_by'      => $param['member_id'] ?? 0,
                ]);
            }
            // // 判断订单是否是先面后付款;面试设置，1-先面试后付款；2-先付款后面试
            // $order = OrdersModel::where('order_id', '=', $param['order_id'])
            //     ->field('order_id,member_id,shop_id,goods_id,interview_mode,has_deposit,deposit_amount,payed')
            //     ->findOrEmpty();
            // if ($param['feedback_status'] == 1) { //面试通过, 判断价格
            //     if ($order) {
            //         if ($order->interview_mode == 1) {
            //             // 出现循环拒绝，面试确认，只付款一次信息
            //             $exist = OrderPaymentsModel::where('order_id', $row['order_id'])->findOrEmpty();
            //             if ($exist->isEmpty()) {
            //                 // 面试后付款
            //                 $amount = $order->payed;
            //                 if ($order->has_deposit == 1 && $order->deposit_amount > 0) {
            //                     $amount = $order->deposit_amount;
            //                 }
            //                 // 创建支付计划
            //                 $paymentData = [
            //                     'order_id'       => $row['order_id'],
            //                     'payment_phase'  => 'deposit', // 定金
            //                     'amount'         => $amount,
            //                     'status'         => 'pending',
            //                     'payment_method' => 'wechat',
            //                     'create_time'    => date('Y-m-d H:i:s'),
            //                     'update_time'    => date('Y-m-d H:i:s')
            //                 ];

            //                 $paymentId = OrderPaymentsModel::insertGetId($paymentData);
            //                 if ($order->has_deposit == 1) {
            //                     // 这笔是定金
            //                     $order->deposit_pay_id = $paymentId;
            //                     $order->save();
            //                 }
            //             }
            //         }
            //     }
            // }
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $msg = $e->getMessage();
            return false;
        }
        $order = OrdersModel::where('order_id', '=', $param['order_id'])
            ->field('order_id,member_id,shop_id,goods_id,interview_mode,has_deposit,deposit_amount,payed')
            ->findOrEmpty();
        if ($order->isEmpty()) {
            $msg = '订单不存在';
            return false;
        }
        //通知
        //面试通过或不通过
        //短信通知
        //面试通过或不通过
        //短信通知
        $scope = $param['feedback_status'] == 1 ? NotifyScene::INTERVIEW_FINISHED : NotifyScene::INTERVIEW_MEMBER_CONFIRM_NO_PASS;
        //order_no(订单编号,必填),goods_name(商品名称,必填),amount(金额，选填)
        $orderNotify = [
            'order_no'   => $order->order_id,
            'goods_name' => GoodsModel::where(['goods_id' => $order->goods_id])->value('goods_name'),
        ];
        //uid(用户ID,必填),mobile(手机号,必填),openid(openid，选填)
        $memberNotify = [
            'uid'    => $order->member_id,
            'mobile' => (string)MemberModel::where(['id' => $order->member_id])->value('mobile'),
        ];
        $staffNotify  = [
            'uid'    => $interviewerId,
            'mobile' => (string)StaffModel::where(['id' => $interviewerId])->value('mobile'),
        ];
        $shopNotify   = [
            'uid'    => $order->shop_id,
            'mobile' => (string)ShopModel::where(['id' => $order->shop_id])->value('contact_phone'),
        ];
        Event::trigger('Notify', ['scope' => $scope, 'order' => $orderNotify, 'member' => $memberNotify, 'staff' => $staffNotify, 'shop' => $shopNotify]);

        return true;
    }

    /**
     * 用户更换面试时间
     * @param array $param
     * @param string $msg
     * @return bool
     * @throws \DateMalformedStringException
     */
    public static function changeInterview(array $param = [], string &$msg = ''): bool
    {
        $interview = InterviewSchedulesModel::where('order_id', $param['order_id'])
            ->where('interviewer_member_id', $param['member_id'])
            ->where('status', 'scheduled')
            ->findOrEmpty();
        if ($interview->isEmpty()) {
            $msg = '你没有面试记录';
            return false;
        }

        // 验证此时间是否有效
        $interviewTime  = DateTime::createFromFormat('Y-m-d H:i:s', $param['interview']); // 转换为完整格式
        $checkStartTime = (clone $interviewTime)->modify('-1 hours')->format('Y-m-d H:00:00');
        $checkEndTime   = (clone $interviewTime)->modify('+1 hours')->format('Y-m-d H:00:00');

        $row = InterviewScheduleModel::where('interviewer_id', '=', $interview->interviewer_id)
            ->where('interview_time', 'between', [$checkStartTime, $checkEndTime])
            ->whereOr('start_time', 'between', [$checkStartTime, $checkEndTime])
            ->whereOr('end_time', 'between', [$checkStartTime, $checkEndTime])
            ->findOrEmpty();
        if (!$row->isEmpty() && $row->id != $interview->id) {
            // 时间冲突
            $msg = '该面试时间已被占用';
            return false;
        }

        $interview->interview_time = date('Y-m-d H:i:s', strtotime($param['interview']));
        $interview->remark         = $param['remark'] ?? ''; //把备注信息通知给服务人员
        $interview->save();

        //异步调用，定时创建面试房间
        $action = [
            'method' => 'app\api\service\InterviewService::createRoom',
            'param'  => [$param['order_id']],
        ];
        //延迟秒数（时间戳）= 要执行时间 - 当前时间
        $delayTime = strtotime($param['interview']) - time() - 15 * 60;
        $tag       = 'InterviewService_createRoom_' . $param['order_id'];
        if (!TransactionModel::addOneData($action, $tag, "创建面试房间", $delayTime)) {
            return false;
        }
        // 通知服务人员
        // 短信通知
        $order = OrdersModel::where('order_id', $param['order_id'])->findOrEmpty();
        $scope = NotifyScene::MEMBER_MODIFY_INTERVIEW_TIME;
        //order_no(订单编号,必填),goods_name(商品名称,必填),amount(金额，选填)
        $orderNotify = [
            'order_no'   => $order->order_id,
            'goods_name' => GoodsModel::where(['goods_id' => $order->goods_id])->value('goods_name'),
        ];
        //uid(用户ID,必填),mobile(手机号,必填),openid(openid，选填)
        $memberNotify = [
            'uid'    => $order->member_id,
            'mobile' => (string)MemberModel::where(['id' => $order->member_id])->value('mobile'),
        ];
        $staffNotify  = [
            'uid'    => $interview->interviewer_id,
            'mobile' => (string)StaffModel::where(['id' => $interview->interviewer_id])->value('mobile'),
        ];
        $shopNotify   = [
            'uid'    => $order->shop_id,
            'mobile' => (string)ShopModel::where(['id' => $order->shop_id])->value('contact_phone'),
        ];
        Event::trigger('Notify', ['scope' => $scope, 'order' => $orderNotify, 'member' => $memberNotify, 'staff' => $staffNotify, 'shop' => $shopNotify]);
        return true;
    }

    //根据订单获取有效服务人员列表
    public static function getValidStaffListByOrder(string $orderId = ''): array
    {
        $order = OrdersModel::where('order_id', $orderId)->findOrEmpty();
        if ($order->isEmpty()) {
            return [];
        }
        $cartParam = self::getCartParamByOrder($order);
        return [
            'sku_id'       => $cartParam['sku_ids'],
            'addr_id'      => $cartParam['addr_id'] ?? 0,
            'service_date' => $cartParam['service_date'],
        ];
    }

    // 订单换人
    public static function changeStaffByOrder(array $param = [], string &$msg = ''): bool
    {
        $order = OrdersModel::where('order_id', $param['order_id'])->findOrEmpty();
        if ($order->isEmpty()) {
            $msg = '订单不存在';
            return false;
        }
        if ($order->confirm == OrdersModel::ConfirmConfirmed) {
            $msg = '订单已确认，无法更换';
            return false;
        }
        Db::startTrans();
        try {
            $updateOrder = 0;
            $addon       = $order->addon;
            $oldStaffId  = intval($addon['staff_snapshot']['id']);
            $newStaffId  = intval($param['staff_id']);
            if ($oldStaffId != $newStaffId) {
                // 换人
                $cartParam             = self::getCartParamByOrder($order);
                $cartParam['staff_id'] = $newStaffId; // 换人
                // 加载购物车
                $cartsDirector = new CartsDirectorSer();
                $cart          = $cartsDirector->getCarts($cartParam);
                $staffSnapshot = $cart['staff_snapshot'] ?? [];
                if (empty($staffSnapshot)) {
                    $msg = '服务人员不存在';
                    return false;
                }
                $order->staff_id         = $newStaffId; // 替换新人
                $addon['staff_snapshot'] = $staffSnapshot; // 替换新人
                $order->addon            = serialize($addon);

                // items表更新
                OrderItemsModel::where('order_id', $order->order_id)->update([
                    'interviewer_staff_id' => $newStaffId, // 新人id
                ]);

                $updateOrder = 1;
            }

            // 重新处理面试时间
            $exist = InterviewScheduleModel::where('order_id', $order->order_id)
                ->where('interviewer_id', $oldStaffId)
                ->where('status', 'scheduled')
                ->find();
            if ($exist) {
                if ($oldStaffId != $newStaffId) {
                    // 换人，删除旧人面试时间
                    $exist->delete();
                } else {
                    // 不换人，更新面试时间
                    $exist->interview_time = date('Y-m-d H:i:s', strtotime($param['interview']));
                    $exist->remark         = $param['remark'] ?? ''; //把备注信息通知给服务人员
                    $exist->save();
                }
            } else {
                if (!empty($param['interview'] ?? '')) {
                    $order->requires_interview = 1; // 不需要面试
                    InterviewScheduleModel::insert([
                        'order_id'              => $order->order_id,
                        'interviewer_id'        => $newStaffId,
                        'interviewer_member_id' => $order->member_id,
                        'interview_time'        => date('Y-m-d H:i:s', strtotime($param['interview'])),
                        'status'                => 'scheduled',
                        'remark'                => $param['remark'] ?? '', //把备注信息通知给服务人员
                    ]);
                } else {
                    $order->requires_interview = 0; // 不需要面试
                }
                $updateOrder = 1;
            }

            // 备注修改
            if ($order->memo != $param['memo']) {
                $order->memo = $param['memo'] ?? ''; // 备注
                $updateOrder = 1;
            }
            if ($updateOrder == 1) {
                $order->save();
            }
            Db::commit();
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            $msg = $e->getMessage();
            return false;
        }
        return true;
    }

    // 订单确认
    public static function orderConfirm(array $param = [], string &$msg = ''): bool
    {
        $order = OrdersModel::where('order_id', $param['order_id'])->findOrEmpty();
        if ($order->isEmpty()) {
            $msg = '订单不存在';
            return false;
        }

        if ($order->pay_status == OrdersModel::PayStatusUnpaid) {
            $msg = '订单未支付，无法确认';
            return false;
        }
        if ($order->confirm != OrdersModel::ConfirmUnconfirmed) {
            $msg = '订单已确认，无需重复确认';
            return false;
        }

        if ($order->promotion_type == 'training' && $order->staff_id == 0) {
            // 跳过
            $order->confirm = OrdersModel::ConfirmContractConfirmed;
        } else {
            $order->confirm = OrdersModel::ConfirmConfirmed;

            // 服务人员锁时间
            $addon            = $order->addon;
            $shopId           = $addon['shop_snapshot']['id'] ?? 0;
            $staffId          = $addon['staff_snapshot']['id'] ?? 0;
            $staffShopId      = $addon['staff_snapshot']['shop_id'] ?? 0;
            $future_lock_days = $addon['future_lock_days'] ?? [];
            // 再次确认日历
            $staffWorkDate = (new StaffService())->getWorkList([
                'staff_id'   => $staffId,
                'start_date' => $future_lock_days['start_date'],
                'end_date'   => $future_lock_days['end_date'],
            ]);

            // 开始日期不在日期里, 服务人员已被占用
            if (!in_array($future_lock_days['expected_start_date'], $staffWorkDate)) {
                $msg = '服务人员已被占用';
                return false;
            }

            $serviceTime = substr($order->service_time, 0, 10);
            // 锁定时间
            $rows = [];
            foreach ($staffWorkDate as $day) {
                array_push($rows, [
                    'order_id'   => $order->order_id,
                    'staff_id'   => $staffId,
                    'shop_id'    => $shopId,
                    'staff_type' => $shopId == $staffShopId ? 'self' : 'share',
                    'lock_date'  => $day,
                    'lock_time'  => isset($future_lock_days['service_time']) ? substr($future_lock_days['service_time'], -8) : '00:00:00',
                    'lock_type'  => $serviceTime > $day ? 'appointment' : 'workday',
                ]);
            }
        }

        Db::startTrans();
        try {
            $res = $order->save();
            if (!$res) {
                $msg = '订单确认失败';
                return false;
            }
            if (!empty($rows)) {
                OrderOccupancyModel::insertAll($rows);
            }
            // 以下其他操作, 比如通知
            Db::commit();
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            $msg = $e->getMessage();
            return false;
        }
        return true;
    }

    // 订单签字
    public static function orderContractConfirm(array $param = [], string &$msg = ''): bool
    {
        $order = OrdersModel::where('order_id', $param['order_id'])->findOrEmpty();
        if ($order->isEmpty()) {
            $msg = '订单不存在';
            return false;
        }
        if ($order->pay_status == OrdersModel::PayStatusUnpaid) {
            $msg = '订单未支付，无法签字';
            return false;
        }

        if ($order->confirm != OrdersModel::ConfirmShopConfirmed) {
            $msg = '未上传合同，无法签字';
            return false;
        }
        $order->confirm = OrdersModel::ConfirmContractConfirmed;
        $res            = $order->save();
        if (!$res) {
            $msg = '订单签字失败';
            return false;
        }
        return true;
    }

    /**
     * Redis冻结库存释放
     * 场景：
     * 1,支付成功,冻结库存释放，thaw_type=1 ;Code:app\api\event\PaySuccess{};
     * 2,订单用户主动取消,冻结库存释放,thaw_type=2;Code:app\api\service\Order\OrderSer{}.orderCancel();
     * 3,订单超时没有支付,冻结库存释放,thaw_type=2;Code:TODO
     * @param $goodsId 商品ID
     * @param $skuId skuID
     * @param $orderId 订单ID
     * @param $num 释放库存数量
     * @param $thawType 解冻类型
     * @return void
     */
    public static function stockUnfreeze(int $goodsId, int $skuId, string $orderId, int $num, int $thawType)
    {
        $recordFlag = __NAMESPACE__ . '\\' . __CLASS__ . '\\' . __FUNCTION__;
        Log::record($recordFlag . '====>解冻前数据:' . arrayToJson([$goodsId, $skuId, $orderId, $num, $thawType]));
        $script = file_get_contents(getLuaFile('unfreeze'));
        $redis  = Cache::store('redis')->handler();
        $result = $redis->eval($script, [$goodsId, $skuId, $orderId, $num, $thawType]);
        Log::record($recordFlag . '====>解冻结果:' . $result);
    }

    /**
     * 服务时间确定，只允许用户确认一次
     * @param array $param
     * @param string $msg
     * @return bool
     */
    public static function orderServiceTimeConfirm(array $param = [], string &$msg = ''): bool
    {
        if (empty($param['order_id'])) {
            $msg = '订单ID不能为空';
            return false;
        }
        $order = OrdersModel::where('order_id', $param['order_id'])->findOrEmpty();
        if ($order->isEmpty()) {
            $msg = '订单不存在';
            return false;
        }
        //只允许用户确认一次
        if ($order->service_time_confirm_status == OrdersModel::ServiceTimeMemberConfirmed || $order->service_time_confirm_status == OrdersModel::ServiceTimeSystemConfirmed) {
            $msg = '服务时间已经确定';
            return false;
        }
        $order->service_time_confirm_status = OrdersModel::ServiceTimeMemberConfirmed;
        if (!empty($param['service_time'])) {
            //service_time为空，则使用用户首次填写的值，此次不用更新
            $order->service_time = $param['service_time'];
        }
        $res = $order->save();
        if (!$res) {
            $msg = '确认服务时间失败';
            return false;
        }
        return true;
    }

    /**
     * 未支付尾款订单数量
     * 查询条件：
     * 1. 会员ID匹配
     * 2. 定金已支付 (deposit_pay_status = 1)
     * 3. 支付状态为部分付款 (pay_status = 'partial_paid')
     * 4. 服务已结束 (end_service_time 不为空)
     * 5. 订单状态为活动状态 (status = 'active')
     * 6. 支付方式为定金+尾款 (payment_type = 2)
     *
     * @param array $param
     * @param string $msg
     * @return int 未支付尾款的订单数量
     */
    public static function unpayFinalCount(array $param = [], string &$msg = ''): int
    {
        if (empty($param['member_id'])) {
            return 0;
        }
        $cacheKey     = "final_payment:{$param['member_id']}";
        $rateLimitKey = "rate_limit:{$param['member_id']}";

        // 频率限制验证（每天允许调用一次）
        if (Cache::store('redis')->get($rateLimitKey)) {
            return 0;
        }

        // 尝试获取缓存
        if ($cacheCount = Cache::store('redis')->get($cacheKey)) {
            return $cacheCount;
        }
        /*
        goods.goods_id = order.goods_id
        goods.final_payment_type 尾款支付类型：1=服务开始前支付；2=服务完成前支付
        goods.final_payment_type = 1 : now >= order.service_time-3days
        goods.final_payment_type = 2 : now >= order.start_service_time
         */
        // getLastSql : SELECT `order_id` FROM `orders` WHERE  `member_id` = 823  AND `has_deposit` = 1  AND `deposit_pay_status` = 1  AND `pay_status` = 'partial_paid'  AND `status` = 'active'  AND `payment_type` = 2  AND `end_service_time` IS NULL  AND EXISTS ( SELECT * FROM `goods` WHERE  ( goods.goods_id = orders.goods_id )  AND (  ( (goods.final_payment_type = 1 AND NOW() >= DATE_SUB(orders.service_time, INTERVAL 3 DAY)) ) OR (  ( (goods.final_payment_type = 2 AND NOW() >= orders.start_service_time) ) ) ) )
        // 使用闭包构建子查询获取有效订单ID
        $orderIds = OrdersModel::where([
            ['member_id', '=', $param['member_id']],
            ['has_deposit', '=', 1],
            ['deposit_pay_status', '=', 1],
            ['pay_status', '=', OrdersModel::PayStatusPartPaid],
            ['status', '=', OrdersModel::StatusActive],
            ['payment_type', '=', OrdersModel::PaymentTypeDeposit]
        ])->whereNull('end_service_time')
            ->when(true, function ($query) {
                $query->whereExists(function ($q) {
                    $q->table('goods')
                        ->whereRaw('goods.goods_id = orders.goods_id')
                        ->where(function ($sub) {
                            $sub->whereRaw('(goods.final_payment_type = 1 AND NOW() >= DATE_SUB(orders.service_time, INTERVAL 3 DAY))')
                                ->whereOr(function ($qq) {
                                    $qq->WhereRaw('(goods.final_payment_type = 2 AND NOW() >= orders.start_service_time)');
                                });
                        });
                });
            })->column('order_id');
        if (empty($orderIds)) {
            return 0;
        }

        // 查询这些订单中有尾款待支付的数量
        // 当前业务只需要待支付订单数量，不需要待支付订单的具体金额，则如下代码注释掉
        /*$count = OrderPaymentsModel::where([
            ['order_id', 'in', $orderIds],
            ['payment_phase', '=', 'final'],
            ['status', '=', 'pending']
        ])->count() ?? 0;*/

        $count = count($orderIds);
        // 写入缓存
        Cache::store('redis')->set($cacheKey, $count, 60);  // 1分钟缓存
        Cache::store('redis')->set($rateLimitKey, 1, 86400); // 24小时频率限制

        return $count;
    }

    /**
     * 用户订单申请退款
     * @param array $param
     * @param string $msg
     * @return bool
     */
    public static function orderRefundApply(array $param = [], string &$msg = ''): bool
    {
        $bool = OrderRefundApplyModel::where(['order_id' => $param['order_id']])->count() > 0;
        if ($bool) {
            return false;
        }
        return OrderRefundApplyModel::insert([
            'order_id'        => $param['order_id'], //订单ID
            'member_id'       => $param['member_id'], //用户ID
            'goods_id'        => OrdersModel::where(['order_id' => $param['order_id']])->value('goods_id') ?? '', //商品ID
            'shop_id'         => OrdersModel::where(['order_id' => $param['order_id']])->value('shop_id') ?? '', //店铺ID
            'contact_name'    => $param['contact_name'], //联系人姓名
            'contact_phone'   => $param['contact_phone'], //联系电话
            'refund_desc'     => $param['refund_desc'] ?? '', //退款描述
            'evidence_images' => $param['evidence_images'] ?? '', //凭证图片，多个逗号分隔
        ]) > 0;
    }

    /**
     * 用户订单申请退款详情
     * @param mixed $param
     * @param string $eMsg
     * @return array
     */
    public static function orderRefundApplyDetail(mixed $param, string $eMsg): array
    {
        $detail = OrderRefundApplyModel::field('order_id,contact_name,contact_phone,evidence_images,refund_desc,status,create_time')->where(['order_id' => $param['order_id']])->findOrEmpty();
        if ($detail->isEmpty()) {
            return ['订单不存在'];
        }
        if (!empty($detail->evidence_images)) {
            $images          = UploadService::getByIds(explode(',', $detail->evidence_images));
            $formattedImages = [];
            foreach ($images as $id => $url) {
                $formattedImages[] = [
                    'id'  => $id,
                    'url' => $url
                ];
            }
            // 更新evidence_images字段
            $detail->evidence_images = $formattedImages;
        }
        $detail->status_label = OrderService::refundStatusLabel($detail->status);
        return $detail->toArray();
    }
}
