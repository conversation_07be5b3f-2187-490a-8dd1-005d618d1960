<?php

namespace app\api\service\Order;

use app\admin\controller\ContractTemplate;
use app\api\service\AddressSer;
use app\api\service\BaseSer;
use app\api\service\Carts\CartsDirectorSer;
use app\common\enum\Response;
use app\common\model\ContractGoodsRelationModel;
use app\common\model\GoodsModel;
use app\common\model\InterviewScheduleModel;
use app\common\model\MemberAddressModel;

use app\common\model\OrdersModel;
use app\common\model\TransactionModel;
use app\common\service\VerificationOrderService;
use app\api\service\Order\OrderSer;
use app\common\service\CouponService;
use DateTime;
use think\facade\Cache;
use think\facade\Db;

abstract class DoneOrderSer extends BaseSer
{
    public $param = []; //请求参数
    public $interviewerStaffId = 0; //面试者

    /**
     * @var CartsSer
     */
    public $cart; //购物车对象
    public $time;

    public $modelOrderAttrs = []; //order表字段

    //终止下单时的错误
    public $stopErrCode = 0;

    // 发票
    public $invoiceParam = [];

    abstract public function generateOrderStep();

    public static function worker(array $param = [])
    {
        $cartType = $param['cartType'] ?? 1;
        switch ($cartType) {
            case CartsDirectorSer::CART_MODE_GOODS:
                $worker = new DoneNormalOrder($param);
                break;
            case CartsDirectorSer::CART_MODE_COOKING:
                $worker = new DoneCookingOrder($param);
                break;
            case CartsDirectorSer::CART_MODE_TRAINING:
                $worker = new DoneTrainingOrder($param);
                break;
            default:
                $worker = new DoneNormalOrder($param);
        }
        $worker->time = time();
        return $worker->generateOrderStep();
    }

    public function orderId()
    {
        return $this->modelOrderAttrs['order_id'];
    }

    public function executingKey()
    {
        return 'order_create_' . $this->param['member_id'];
    }

    public function setExecuting()
    {
        // 需要大于冻结库存时间, 这里缓存的过期时间设置为120s
        Cache::set($this->executingKey(), time(), 120);
    }

    public function clearExecuting()
    {
        Cache::delete($this->executingKey()); // 防止订单出错后，要等5s才能下单
    }

    public function isExecuting()
    {
        // 防止重复提交，当前时间要超过上次创建5秒
        if (time() <= (Cache::get($this->executingKey()) + 5)) {
            return true;
        }
        return false;
    }

    public function filter()
    {
        if (!$this->filterPayment()) {
            return false;
        }
        if (!$this->filterUseAdvanceKey()) {
            return false;
        }
        if (!$this->filterAddr()) {
            return false;
        }
        if (!$this->filterPackageShipTime()) {
            return false;
        }
        if (!$this->filterPlusOrder()) {
            return false;
        }
        if (!$this->filterInvoice()) {
            return false;
        }
        return true;
    }

    public function filterPayment()
    {
        $payment = $this->param['payment'] ?? '';
        if ($payment == '-1') {
            $this->stopErrCode = Response::PAY_TYPE_NOT_SUPPORT;
            return false;
        }
        return true;
    }

    public function filterUseAdvanceKey()
    {
        $useAdvanceKey = $this->param['use_advance_key'] ?? ''; //使用余额的凭证
        // chkUseAdvanceKey 检查用户余额，是不是余额不足啊。。。
        return true;
    }

    public function filterAddr()
    {
        // 默认空值
        $this->param['service_mode'] = $this->param['service_mode'] ?? '';
        if ($this->param['service_mode'] == GoodsModel::SERVICE_MODE_SHOP) {
            return true; //门店自提
        }
        $addrId = $this->param['addr_id'] ?? AddressSer::getDefaultAddressId($this->param['member_id']);
        if (!$addrId) {
            $this->stopErrCode = Response::ORDER_ADD_ADDRESS;
            return false;
        }
        $shipAddress = MemberAddressModel::where(['id' => $addrId, 'member_id' => $this->param['member_id']])->find();
        if (empty($shipAddress)) {
            $this->stopErrCode = Response::ORDER_ADD_ADDRESS;
            return false;
        }
        $this->param['addr_id'] = $addrId;
        return true;
    }

    public function filterPackageShipTime()
    {
        // $packages = json_decode($this->param['packages']);
        // 处理配送时间
        return true;
    }

    public function filterPlusOrder()
    {
        $plusOrderId = $this->param['p_order_id'] ?? '';
        if (empty($plusOrderId)) {
            return true;
        }
        // 关联订单，有时候需要加购订单
        return true;
    }

    public function filterInvoice()
    {
        $is_tax = $this->param['is_tax'] ?? 'false';
        $tax_no = strtoupper($this->param['tax_no'] ?? '');

        if ($tax_no) {
            //发票信息校验
            $this->invoiceParam['is_tax']       = $is_tax; //是否开发票
            $this->invoiceParam['tax_no']       = $tax_no; //发票税号（企业必填）
            $this->invoiceParam['tax_name']     = $this->param['tax_name'] ?? ''; //发票抬头（企业、个人必填）
            $this->invoiceParam['invoice_type'] = $this->param['invoice_type'] ?? ''; //发票类型
            $this->invoiceParam['mobile']       = $this->param['tax_mobile'] ?? ''; //手机号 （企业、个人必填）
            $this->invoiceParam['email']        = $this->param['tax_email'] ?? ''; //邮箱 （企业、个人必填）
            $this->invoiceParam['bank_name']    = $this->param['bank_name'] ?? ''; //开户行（企业必填）
            $this->invoiceParam['bank_account'] = $this->param['bank_account'] ?? ''; //银行账号（企业选填）
            $this->invoiceParam['addr']         = $this->param['tax_addr'] ?? ''; //地址（企业选填）
            $this->invoiceParam['tel']          = $this->param['tax_tel'] ?? ''; //电话（企业选填）
            $this->invoiceParam['source_type']  = $this->param['source_type'] ?? 'electronic'; //电子或纸质发票
            // TODO: 校验发票信息
            $tax_return = true;
            if (!$tax_return) {
                $this->stopErrCode = Response::ORDER_INVOICE_ERROR;
            }
        }
        return true;
    }

    public function cart()
    {
        $cartsDirector = new CartsDirectorSer();
        $this->cart    = $cartsDirector->getCarts($this->param);
    }

    public function chkCart()
    {
        if (!$this->chkMoney()) {
            return false;
        }
        if (!$this->chkCartStatus()) {
            return false;
        }
        if (!$this->chkCartUsedAdvance()) {
            return false;
        }
        if (!$this->chkCartModifiedAt()) {
            return false;
        }
        if (!$this->chkCartErrorData()) {
            return false;
        }
        if (!$this->chkCartGoods()) {
            return false;
        }
        if (!$this->chkCartShipTime()) {
            return false;
        }
        if (!$this->chkStaffId()) {
            return false;
        }
        if (!$this->chkInterviewTime()) {
            return false;
        }
        return true;
    }

    public function chkMoney()
    {
        // 检查订单金额与支付金额是否一致
        return true;
    }

    public function chkCartStatus()
    {
        // 检查购物车状态
        return true;
    }

    public function chkCartUsedAdvance()
    {
        $withdraw = $this->param['withdraw'] ?? 0.00;
        //判断输入余额与实际使用余额不相符
        return true;
    }

    public function chkCartModifiedAt()
    {
        $modifiedAt = $this->param['modifiedAt'] ?: 0;
        // 判断订单创建时间,超时太久拒绝
        return true;
    }

    public function chkCartErrorData()
    {
        // 当购物车部分失效等, 是否拒绝提交
        return true;
    }

    public function chkCartGoods()
    {
        // 检查购物车商品, 是否有有效商品
        if (empty($this->cart['goods_snapshot'])) {
            $this->stopErrCode = Response::ORDER_CART_EMPTY;
            return false;
        }
        return true;
    }

    public function chkCartShipTime()
    {
        // 检查购物车商品等配送时间、配送区域
        // 当前配送时间已不可用，请刷新后重新选择
        // 当前配送区域已不可用，请刷新后重新选择
        return true;
    }

    public function chkStaffId()
    {
        if (!empty($this->param['staff_id'] ?? '')) {
            $serviceTime = strtotime($this->param['service_date']);
            $serviceDate = date('Y-m-d', $serviceTime);

            if (empty($this->cart['work_date'])) {
                // 是否是有效员工
                $this->stopErrCode = Response::INVALID_STAFF;
                return false;
            }

            // 判断服务日期是否在可工作日期内
            if (!in_array($serviceDate, $this->cart['work_date'])) {
                // 是否是有效员工
                $this->stopErrCode = Response::INVALID_STAFF;
                return false;
            }
            $this->interviewerStaffId = $this->param['staff_id'];
        }
        return true;
    }

    public function chkInterviewTime()
    {
        if (!empty($this->param['interview'] ?? '')) {
            $interviewTime  = DateTime::createFromFormat('Y-m-d H:i:s', $this->param['interview']); // 转换为完整格式
            $checkStartTime = (clone $interviewTime)->modify('-1 hours')->format('Y-m-d H:00:00');
            $checkEndTime   = (clone $interviewTime)->modify('+1 hours')->format('Y-m-d H:00:00');

            $row = InterviewScheduleModel::where('interviewer_id', '=', $this->interviewerStaffId)
                ->where('interview_time', 'between', [$checkStartTime, $checkEndTime])
                ->whereOr('start_time', 'between', [$checkStartTime, $checkEndTime])
                ->whereOr('end_time', 'between', [$checkStartTime, $checkEndTime])
                ->findOrEmpty();
            if (!$row->isEmpty()) {
                // 时间冲突
                $this->stopErrCode = Response::INVALID_INTERVIEW_TIME;
                return false;
            }
        }
        return true;
    }

    public function generateOrder(): bool
    {
        Db::startTrans();
        try {
            $this->generateOrderId(); //生成订单号
            if (!$this->generateFreeze()) {
                $this->generateUnFreeze(); // 冻结一半的数据要退回
                Db::rollBack();
                return false;
            }
            if (!$this->generateSaveTable()) {
                Db::rollBack();
                $this->generateUnFreeze();
                return false;
            }
            // 1-上门等服务人员上门产生核销码; 2-到店生成核销码
            $service_mode = intval($this->cart['service_mode'] ?? 0);
            if ($service_mode == 2) {
                (new VerificationOrderService())->generateVerificationCode($this->modelOrderAttrs['order_id']);
            }
            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollBack();
            $this->generateUnFreeze();
            dump($e->getMessage());
            exit;
            return false;
        }
    }

    //冻结库存
    public function generateFreeze(): bool
    {
        $isOk = 0;
        foreach ($this->cart['cart']['goodsItems'] as $item) {
            if ($item['selected'] == 0) {
                continue;
            }
            $goodsId  = $item['goods_id'] ?? 0;
            $skuId    = $item['goods_sku_id'] ?? 0;
            $order_id = $this->modelOrderAttrs['order_id'] ?? '';
            $num      = $item['quantity'] ?? 0;
            if ($goodsId > 0 && $skuId > 0 && $order_id && $num > 0) {
                try {
                    $script = file_get_contents(getLuaFile('freeze'));
                    $redis  = Cache::store('redis')->handler();
                    $redis->eval($script, [$goodsId, $skuId, $order_id, $num]);
                    $isOk = 1;
                } catch (\Exception $e) {
                    echo $e->getMessage();
                    $this->stopErrCode = Response::STOCK_FREEZE_FAIL;
                    return false;
                }
            } else {
                $this->stopErrCode = Response::STOCK_FREEZE_FAIL_PARAM;
                return false;
            }
        }
        return $isOk == 0 ? false : true;
    }

    //解冻库存
    public function generateUnFreeze(): bool
    {
        $isOk = 0;
        foreach ($this->cart['cart']['goodsItems'] as $item) {
            if ($item['selected'] == 0) {
                continue;
            }
            $goodsId  = $item['goods_id'] ?? 0;
            $skuId    = $item['goods_sku_id'] ?? 0;
            $order_id = $this->modelOrderAttrs['order_id'] ?? '';
            $num      = $item['quantity'] ?? 0;
            if ($goodsId > 0 && $skuId > 0 && $order_id && $num > 0) {
                try {
                    $script = file_get_contents(getLuaFile('unfreeze'));
                    $redis  = Cache::store('redis')->handler();
                    $redis->eval($script, [$goodsId, $skuId, $order_id, $num, 2]);
                    $isOk = 1;
                } catch (\Exception $e) {
                    echo $e->getMessage();
                    $this->stopErrCode = Response::STOCK_ROLLBACK_FAIL;
                    return false;
                }
            } else {
                $this->stopErrCode = Response::STOCK_ROLLBACK_FAIL_PARAM;
                return false;
            }
        }
        return $isOk == 0 ? false : true;
    }

    public function generateSaveTable(): bool
    {
        if (!$this->generateSaveTableOrder()) {
            return false;
        }
        if (!$this->generateSaveTableOrderItems()) {
            return false;
        }
        if (!$this->generateSaveTableOrderStaffRel()) {
            return false;
        }
        if (!$this->generateSaveTableOrderPayments()) {
            return false;
        }
        if (!$this->generateSaveTableInterviewSchedules()) {
            return false;
        }
        if (!$this->generateSaveTableOrderRewards()) {
            return false;
        }
        return true;
    }

    /**
     * 生成订单号
     * @return string
     */
    public function generateOrderId()
    {
        $orderId                           = date('Ymd') . substr(implode("", array_map('ord', str_split(substr(uniqid(), 7, 13), 1))), 0, 8) . mt_rand(1000, 9999);
        $this->modelOrderAttrs['order_id'] = $orderId;
    }

    public function generateSaveTableOrder(): bool
    {
        $fristKey   = array_key_first($this->cart['goods']);
        $firstGoods = $this->cart['goods'][$fristKey] ?? [];

        // $this->modelOrderAttrs['order_id'] = self::generateOrderId();
        $this->modelOrderAttrs['member_id']          = $this->cart['member_id'];
        $this->modelOrderAttrs['shop_id']            = $firstGoods['shop_id'] ?? 0;
        $this->modelOrderAttrs['goods_id']           = $firstGoods['goods_id'] ?? 0;
        $this->modelOrderAttrs['category_id']        = $firstGoods['category_id'] ?? 0;
        $this->modelOrderAttrs['staff_id']           = $this->interviewerStaffId;
        $this->modelOrderAttrs['total_amount']       = $this->cart['cart']['sum']['total_amount'];
        $this->modelOrderAttrs['final_amount']       = $this->cart['cart']['sum']['final_amount'];
        $this->modelOrderAttrs['pay_status']         = 'unpaid';
        $this->modelOrderAttrs['is_delivery']        = empty($this->cart['cart']['shipRules']) ? 0 : 1;
        $this->modelOrderAttrs['ship_status']        = 'unshipped';
        $this->modelOrderAttrs['shipping_id']        = 0;
        $this->modelOrderAttrs['shipping']           = '';
        $this->modelOrderAttrs['promotion_type']     = OrdersModel::PromotionType[$this->param['cartType']] ?? '';
        $this->modelOrderAttrs['status']             = 'active';
        $this->modelOrderAttrs['confirm']            = '0';
        $this->modelOrderAttrs['ship_area_id']       = $this->cart['cart']['address']['area_id'] ?? 0;
        $this->modelOrderAttrs['ship_name']          = $this->cart['cart']['address']['name'] ?? '';
        $this->modelOrderAttrs['ship_addr']          = $this->cart['cart']['address']['address'] ?? '';
        $this->modelOrderAttrs['ship_tel']           = $this->cart['cart']['address']['zip'] ?? '';
        $this->modelOrderAttrs['ship_mobile']        = $this->cart['mobile'] ?? '';
        $this->modelOrderAttrs['ship_time']          = NULL;
        $this->modelOrderAttrs['weight']             = '';
        $this->modelOrderAttrs['tostr']              = '';
        $this->modelOrderAttrs['item_num']           = 1;
        $this->modelOrderAttrs['ip']                 = request()->ip();
        $this->modelOrderAttrs['cost_item']          = $this->cart['cart']['sum']['cost_item'];
        $this->modelOrderAttrs['is_tax']             = 0;
        $this->modelOrderAttrs['tax_type']           = 'false';
        $this->modelOrderAttrs['tax_content']        = '';
        $this->modelOrderAttrs['cost_tax']           = 0;
        $this->modelOrderAttrs['tax_company']        = '';
        $this->modelOrderAttrs['is_protect']         = 0;
        $this->modelOrderAttrs['cost_protect']       = 0;
        $this->modelOrderAttrs['cost_payment']       = $this->cart['cart']['sum']['cost_payment'];
        $this->modelOrderAttrs['score_u']            = isset($this->cart['cart']['sum']['score_u']) ? $this->cart['cart']['sum']['score_u'] : 0;
        $this->modelOrderAttrs['score_g']            = isset($this->cart['cart']['sum']['score_g']) ? $this->cart['cart']['sum']['score_g'] : 0;
        $this->modelOrderAttrs['discount']           = $this->cart['cart']['sum']['discount'];
        $this->modelOrderAttrs['pmt_goods']          = $this->cart['cart']['sum']['discount_goods'];
        $this->modelOrderAttrs['pmt_order']          = $this->cart['cart']['sum']['discount_order'];
        $this->modelOrderAttrs['payed']              = $this->cart['cart']['sum']['payed'];
        $this->modelOrderAttrs['memo']               = $this->param['memo'] ?? '';
        $this->modelOrderAttrs['mark_text']          = '';
        $this->modelOrderAttrs['cost_freight']       = $this->cart['cart']['sum']['cost_freight'];
        $this->modelOrderAttrs['order_refer']        = 'local';
        $this->modelOrderAttrs['last_staff_id']      = 0;

        if (isset($this->param['service_date'])) {
            $serviceTime = $this->param['service_date'] . " " . ($this->param['service_time'] ?? '00:00:00');
            $this->modelOrderAttrs['service_time'] = $serviceTime;
        }

        $this->modelOrderAttrs['start_service_time'] = NULL;
        $this->modelOrderAttrs['end_service_time']   = NULL;
        if ($this->interviewerStaffId == 0 && $this->param['cartType'] == CartsDirectorSer::CART_MODE_TRAINING) {
            $this->modelOrderAttrs['start_service_time'] = date('Y-m-d H:i:s');
            $this->modelOrderAttrs['end_service_time']   = date('Y-m-d H:i:s');
        }
        $this->modelOrderAttrs['addon']              = serialize($this->cart);
        $this->modelOrderAttrs['requires_interview'] = empty($this->param['interview'] ?? '') ? 0 : 1;

        // 制作方式 ,1-做好送上门；2-上门制作
        $this->modelOrderAttrs['cook_mode'] = intval($this->cart['cook_mode'] ?? 0);

        // 面试设置，1-先面试后付款；2-先付款后面试
        $this->modelOrderAttrs['interview_mode'] = intval($this->cart['interview_mode'] ?? 2);

        // 服务方式，home-上门；store-到店
        $this->modelOrderAttrs['service_mode'] = $this->cart['service_mode'] ?? 'home';

        // 支付方式，1-全额支付；2-定金+尾款
        $this->modelOrderAttrs['payment_type'] = $this->cart['payment_type'] ?? 1;

        // 尾款支付类型和天数
        $this->modelOrderAttrs['final_payment_type'] = $this->cart['cart']['sum']['final_payment_type'] ?? 0;
        $this->modelOrderAttrs['final_payment_days'] = $this->cart['cart']['sum']['final_payment_days'] ?? 0;

        // 定金相关
        $this->modelOrderAttrs['has_deposit'] = isset($this->cart['cart']['sum']['deposit_amount']) && $this->cart['cart']['sum']['deposit_amount'] > 0 ? 1 : 0;
        $this->modelOrderAttrs['deposit_amount'] = isset($this->cart['cart']['sum']['deposit_amount']) ? $this->cart['cart']['sum']['deposit_amount'] : 0;
        $this->modelOrderAttrs['deposit_pay_status'] = 0;
        $this->modelOrderAttrs['deposit_pay_id'] = 0;

        // 服务时间确认状态
        $this->modelOrderAttrs['service_time_confirm_status'] = 0;

        // 合同相关
        $contractTemplateId = ContractGoodsRelationModel::where('goods_id', $this->modelOrderAttrs['goods_id'])->where('is_active', 1)->value('template_id');
        if ($contractTemplateId > 0) {
            $this->modelOrderAttrs['need_contract'] = 1;
            $this->modelOrderAttrs['contract_template_id'] = $contractTemplateId;
            $this->modelOrderAttrs['contract_status'] = 0;
        }

        // 优惠券
        $this->modelOrderAttrs['used_coupon_id'] = $this->cart['cart']['sum']['can_use_coupon'] ?  ($this->cart['cart']['sum']['coupon_info']['id'] ?? 0) : 0;

        // 系统折扣
        $this->modelOrderAttrs['system_discount'] = isset($this->cart['cart']['sum']['discount_coupon']) ? $this->cart['cart']['sum']['discount_coupon'] : 0;

        // 客户评价
        $this->modelOrderAttrs['customer_rate'] = 0;
        $this->modelOrderAttrs['customer_status'] = 0;

        // 订单类型
        $this->modelOrderAttrs['order_type'] = $this->param['cartType'] ?? 1;
        Db::name('orders')->insert($this->modelOrderAttrs);
        return true;
    }

    public function generateSaveTableOrderItems(): bool
    {
        $goods = $this->cart['goods'];
        $rows  = [];
        foreach ($this->cart['cart']['goodsItems'] as $item) {
            $sku = $goods[$item['goods_sku_id']] ?? [];
            if (!empty($sku) && $item['selected'] == 1) {
                $goods_price = $sku['goods_price'] ?? 0;
                $line_price  = $sku['line_price'] ?? 0;
                $quantity    = intval($item['quantity'] ?? 0);
                $row         = [
                    'order_id'              => $this->modelOrderAttrs['order_id'],
                    'shop_id'               => $sku['shop_id'] ?? 0,
                    'goods_id'              => $sku['goods_id'] ?? 0,
                    'goods_sku_id'          => $sku['goods_sku_id'] ?? 0,
                    'category_id'           => $sku['category_id'] ?? 0,
                    'goods_name'            => $sku['goods_name'] ?? 0,
                    'goods_price'           => $goods_price,
                    'goods_line_price'      => $line_price,
                    'goods_num'             => $quantity,
                    'goods_amount'          => $quantity == 0 ? 0 : $goods_price * $quantity,
                    'goods_discount_amount' => $quantity == 0 ? 0 : ($line_price - $goods_price) * $quantity,
                    'interviewer_staff_id'  => $this->interviewerStaffId,
                    'addon'                 => serialize($sku),
                    'service_day'           => 1,
                    'start_service_time'    => NULL,
                    'end_service_time'      => NULL,
                    'payment_type'          => $this->cart['payment_type'] ?? 1,
                    'final_payment_type'    => $this->cart['cart']['sum']['final_payment_type'] ?? 0,
                    'final_payment_days'    => $this->cart['cart']['sum']['final_payment_days'] ?? 0,
                    'first_dealer' => $sku['first_dealer'] ?? 0,
                    'second_dealer' => $sku['second_dealer'] ?? 0,
                    'clerk_money' => $sku['clerk_money'] ?? 0,
                    'partner_money' => $sku['partner_money'] ?? 0,
                    'shop_money' => $sku['shop_money'] ?? 0,
                    'platform_money' => $sku['platform_money'] ?? 0,
                ];
                array_push($rows, $row);
            }
        }
        if (count($rows) > 1) {
            // 修改orders.item_num
            Db::name('orders')->where('order_id', $this->modelOrderAttrs['order_id'])->update([
                'item_num' => count($rows)
            ]);
        }
        Db::name('order_items')->insertAll($rows);

        return true;
    }

    public function generateSaveTableOrderStaffRel(): bool
    {
        // 如果没有关联服务人员，不插入数据
        if (empty($this->interviewerStaffId)) {
            return true;
        }

        // 增加服务人员关系表(多个服务人员，填多条数据)
        Db::name('order_staff_rel')->insert([
            'order_id' => $this->modelOrderAttrs['order_id'],
            'staff_id' => $this->interviewerStaffId,
        ]);

        return true;
    }



    /**
     * 创建订单支付计划
     *
     * 只处理两种基本支付情况：全额支付或定金+尾款支付
     * 增值服务支付计划由后台管理系统添加
     *
     * @return bool
     */
    public function generateSaveTableOrderPayments(): bool
    {
        $sum = $this->cart['cart']['sum'];
        $paymentType = $this->cart['payment_type'] ?? 1;

        // 获取积分抵扣金额
        $scoreUAmount = isset($sum['score_u_amount']) ? floatval($sum['score_u_amount']) : 0;

        // 判断支付方式
        if ($paymentType == 2 && isset($sum['deposit_amount']) && $sum['deposit_amount'] > 0) {
            // 定金+尾款支付

            // 计算定金金额（减去积分抵扣）
            $depositAmount = floatval($sum['deposit_amount']);

            // 如果有积分抵扣，优先从定金中扣除
            if ($scoreUAmount > 0) {
                $depositAmount = max(0, $depositAmount - $scoreUAmount);
            }

            // 1. 创建定金支付计划
            $paymentId = Db::name('order_payments')->insertGetId([
                'order_id' => $this->modelOrderAttrs['order_id'],
                'payment_phase' => 'deposit',
                'amount' => $depositAmount,
                'status' => 'pending',
                'payment_method' => $this->param['payment'] ?? 'wechat',
                'due_type' => 'fixed_date',
                'due_date' => date('Y-m-d'),
                'distribution_status' => 'pending',
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s'),
            ]);

            // 如果定金金额为0，自动完成支付
            if ($depositAmount == 0 && $paymentId > 0) {
                // 更新支付计划状态为已支付
                Db::name('order_payments')->where('id', $paymentId)->update([
                    'status' => 'paid',
                    'paid_amount' => 0,
                    'payment_time' => date('Y-m-d H:i:s')
                ]);

                // 更新订单的定金支付状态
                Db::name('orders')->where('order_id', $this->modelOrderAttrs['order_id'])->update([
                    'deposit_pay_status' => 1,
                    'pay_status' => 'partial_paid'
                ]);

                // 创建支付交易记录
                Db::name('payment_transactions')->insert([
                    'payment_id' => $paymentId,
                    'order_id' => $this->modelOrderAttrs['order_id'],
                    'amount' => 0,
                    'payment_method' => $this->param['payment'] ?? 'wechat',
                    'transaction_id' => 'auto_' . $this->modelOrderAttrs['order_id'] . '_' . time(),
                    'status' => 'completed',
                    'payment_time' => date('Y-m-d H:i:s'),
                    'note' => '0元支付自动完成',
                    'create_time' => date('Y-m-d H:i:s')
                ]);

                // 处理库存释放
                foreach ($this->cart['cart']['goodsItems'] as $item) {
                    if ($item['selected'] == 1) {
                        OrderSer::stockUnfreeze(
                            (int)$item['goods_id'],
                            (int)$item['goods_sku_id'],
                            (string)$this->modelOrderAttrs['order_id'],
                            (int)$item['quantity'],
                            1
                        );
                    }
                }
            }

            // 2. 创建尾款支付计划
            $finalAmount = $sum['final_amount'] - $sum['deposit_amount'];

            // 如果积分抵扣超过定金金额，剩余的积分抵扣从尾款中扣除
            $remainingScoreUAmount = max(0, $scoreUAmount - floatval($sum['deposit_amount']));
            if ($remainingScoreUAmount > 0) {
                $finalAmount = max(0, $finalAmount - $remainingScoreUAmount);
            }

            if ($finalAmount > 0) {
                // 计算尾款支付时间
                $dueDate = NULL;
                $finalPaymentType = $sum['final_payment_type'] ?? 1;
                $finalPaymentDays = $sum['final_payment_days'] ?? 0;

                if ($finalPaymentType == 1) {
                    // 服务开始前支付
                    if (!empty($this->param['service_date'])) {
                        $serviceTime = strtotime($this->param['service_date']);
                        $dueDate = date('Y-m-d H:i:s', $serviceTime - ($finalPaymentDays * 86400));
                    }
                } else if ($finalPaymentType == 2) {
                    // 服务完成前支付
                    if (!empty($this->param['service_date'])) {
                        $serviceTime = strtotime($this->param['service_date']);
                        $dueDate = date('Y-m-d H:i:s', $serviceTime + ($finalPaymentDays * 86400));
                    }
                }

                $finalPaymentId = Db::name('order_payments')->insertGetId([
                    'order_id' => $this->modelOrderAttrs['order_id'],
                    'payment_phase' => 'final',
                    'amount' => $finalAmount,
                    'status' => 'pending',
                    'payment_method' => $this->param['payment'] ?? 'wechat',
                    'due_type' => 'fixed_date',
                    'due_date' => $dueDate ? date('Y-m-d', strtotime($dueDate)) : NULL,
                    'distribution_status' => 'pending',
                    'create_time' => date('Y-m-d H:i:s'),
                    'update_time' => date('Y-m-d H:i:s'),
                ]);

                // 如果尾款金额为0，自动完成支付
                if ($finalAmount == 0 && $finalPaymentId > 0) {
                    // 更新支付计划状态为已支付
                    Db::name('order_payments')->where('id', $finalPaymentId)->update([
                        'status' => 'paid',
                        'paid_amount' => 0,
                        'payment_time' => date('Y-m-d H:i:s')
                    ]);

                    // 更新订单支付状态为已支付
                    Db::name('orders')->where('order_id', $this->modelOrderAttrs['order_id'])->update([
                        'pay_status' => 'paid'
                    ]);

                    // 创建支付交易记录
                    Db::name('payment_transactions')->insert([
                        'payment_id' => $finalPaymentId,
                        'order_id' => $this->modelOrderAttrs['order_id'],
                        'amount' => 0,
                        'payment_method' => $this->param['payment'] ?? 'wechat',
                        'transaction_id' => 'auto_' . $this->modelOrderAttrs['order_id'] . '_' . time(),
                        'status' => 'completed',
                        'payment_time' => date('Y-m-d H:i:s'),
                        'note' => '0元支付自动完成',
                        'create_time' => date('Y-m-d H:i:s')
                    ]);

                    // 处理库存释放
                    foreach ($this->cart['cart']['goodsItems'] as $item) {
                        if ($item['selected'] == 1) {
                            OrderSer::stockUnfreeze(
                                (int)$item['goods_id'],
                                (int)$item['goods_sku_id'],
                                (string)$this->modelOrderAttrs['order_id'],
                                (int)$item['quantity'],
                                1
                            );
                        }
                    }
                }
            }

            // 3. 更新订单表的定金相关字段
            Db::name('orders')->where('order_id', $this->modelOrderAttrs['order_id'])
                ->update([
                    'has_deposit' => 1,
                    'deposit_amount' => $sum['deposit_amount'],
                    'deposit_pay_status' => $depositAmount == 0 ? 1 : 0, // 如果定金为0，标记为已支付
                    'payment_type' => $paymentType,
                    'final_payment_type' => $sum['final_payment_type'] ?? 0,
                    'final_payment_days' => $sum['final_payment_days'] ?? 0,
                ]);
        } else {
            // 全额支付
            // 计算实际支付金额（减去积分抵扣）
            $payAmount = floatval($sum['final_amount']) - $scoreUAmount;
            $payAmount = max(0, $payAmount); // 确保金额不小于0

            $paymentId = Db::name('order_payments')->insertGetId([
                'order_id' => $this->modelOrderAttrs['order_id'],
                'payment_phase' => 'full',
                'amount' => $payAmount,
                'status' => 'pending',
                'payment_method' => $this->param['payment'] ?? 'wechat',
                'due_type' => 'fixed_date',
                'due_date' => date('Y-m-d'),
                'distribution_status' => 'pending',
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s'),
            ]);

            // 如果支付金额为0，自动完成支付
            if ($payAmount == 0 && $paymentId > 0) {
                // 更新支付计划状态为已支付
                Db::name('order_payments')->where('id', $paymentId)->update([
                    'status' => 'paid',
                    'paid_amount' => 0,
                    'payment_time' => date('Y-m-d H:i:s')
                ]);

                // 更新订单支付状态为已支付
                Db::name('orders')->where('order_id', $this->modelOrderAttrs['order_id'])->update([
                    'pay_status' => 'paid'
                ]);

                // 创建支付交易记录
                Db::name('payment_transactions')->insert([
                    'payment_id' => $paymentId,
                    'order_id' => $this->modelOrderAttrs['order_id'],
                    'amount' => 0,
                    'payment_method' => $this->param['payment'] ?? 'wechat',
                    'transaction_id' => 'auto_' . $this->modelOrderAttrs['order_id'] . '_' . time(),
                    'status' => 'completed',
                    'payment_time' => date('Y-m-d H:i:s'),
                    'note' => '0元支付自动完成',
                    'create_time' => date('Y-m-d H:i:s')
                ]);

                // 处理库存释放
                foreach ($this->cart['cart']['goodsItems'] as $item) {
                    if ($item['selected'] == 1) {
                        OrderSer::stockUnfreeze(
                            (int)$item['goods_id'],
                            (int)$item['goods_sku_id'],
                            (string)$this->modelOrderAttrs['order_id'],
                            (int)$item['quantity'],
                            1
                        );
                    }
                }
            }

            // 更新订单表的支付类型
            Db::name('orders')->where('order_id', $this->modelOrderAttrs['order_id'])
                ->update([
                    'payment_type' => $paymentType,
                ]);
        }

        return true;
    }

    public function generateSaveTableOrderRewards(): bool
    {
        $sum = $this->cart['cart']['sum'];

        // 如果有积分奖励，创建积分奖励记录
        if (isset($sum['score_g']) && $sum['score_g'] > 0) {
            Db::name('order_rewards')->insert([
                'order_id' => $this->modelOrderAttrs['order_id'],
                'member_id' => $this->cart['member_id'],
                'type' => 1, // 1=积分，2=优惠券
                'reward_id' => 0,
                'amount' => $sum['score_g'],
                'status' => 0, // 0=未发放，1=已发放
                'create_time' => date('Y-m-d H:i:s'),
                'update_time' => date('Y-m-d H:i:s'),
            ]);

            // 更新订单表的积分字段
            Db::name('orders')->where('order_id', $this->modelOrderAttrs['order_id'])
                ->update([
                    'score_g' => $sum['score_g'],
                ]);
        }

        // 如果使用了积分抵扣，更新订单表的积分抵扣字段
        if (isset($sum['score_u']) && $sum['score_u'] > 0) {
            Db::name('orders')->where('order_id', $this->modelOrderAttrs['order_id'])
                ->update([
                    'score_u' => $sum['score_u'],
                ]);
        }

        return true;
    }

    public function generateSaveTableInterviewSchedules(): bool
    {
        // 如果没有面试时间或者不需要面试，不创建面试安排
        if (empty($this->param['interview'] ?? '') || empty($this->modelOrderAttrs['requires_interview']) || empty($this->interviewerStaffId)) {
            return true;
        }

        // 创建面试安排
        $interviewId = Db::name('interview_schedules')->insertGetId([
            'order_id'              => $this->modelOrderAttrs['order_id'],
            'shop_id'               => $this->cart['shop_snapshot']['id'] ?? 0,
            'interviewer_id'        => $this->interviewerStaffId,
            'interviewer_member_id' => $this->cart['member_id'],
            'interview_time'        => date('Y-m-d H:i:s', strtotime($this->param['interview'])),
            'room_id'               => '',
            'room_name'             => '',
            'status'                => 'scheduled',
            'create_time'           => date('Y-m-d H:i:s'),
            'update_time'           => date('Y-m-d H:i:s'),
        ]);

        // 如果创建失败，返回失败
        if (!$interviewId) {
            return false;
        }

        // 异步调用，定时创建面试房间
        $action    = [
            'method' => 'app\api\service\InterviewService::createRoom',
            'param'  => [$this->modelOrderAttrs['order_id']],
        ];
        // 延迟秒数（时间戳）= 要执行时间 - 当前时间
        $delayTime = strtotime($this->param['interview']) - time() - 15 * 60;
        $tag       = 'InterviewService_createRoom_' . $this->modelOrderAttrs['order_id'];
        if (!TransactionModel::addOneData($action, $tag, "创建面试房间", $delayTime)) {
            return false;
        }

        return true;
    }

    // 生成订单成功后调用事件
    public function generateSuccess()
    {
        if ($this->modelOrderAttrs['score_u'] > 0) {
            // 扣除积分 
            $config = config('api.services.Member');
            $url = $config['BaseURL'] . $config['Api']['OrderDeductIntegral'];
            $data = [
                'member_id' => $this->cart['member_id'],
                'order_id' => $this->modelOrderAttrs['order_id'],
                'points' => $this->modelOrderAttrs['score_u']
            ];
            $res = curlPostApiContentByUrlAndParams($url, $data, ['Authorization: Bearer ' . $this->param['token']], false);
            if (empty($res) || $res['code'] != 1) {
                return false;
            }
        }

        if ($this->modelOrderAttrs['used_coupon_id']  > 0) {
             $data   = [
                'id'  => $this->modelOrderAttrs['used_coupon_id'],
                'member_id' => $this->cart['member_id'],
                'order_id' => $this->modelOrderAttrs['order_id'],
                'status'    => 'USED',
            ];
            $result = CouponService::updateCoupon($data,  $this->param['token'], "api");
            if (empty($result) || $result['code'] != 1) {
                return false;
            }
        }

        return true;
    }
}
