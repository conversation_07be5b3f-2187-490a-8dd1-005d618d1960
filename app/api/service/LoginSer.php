<?php

namespace app\api\service;

use app\common\enum\Response;
use app\common\model\ActivityShareRecordModel;
use app\common\model\MemberDeviceModel;
use app\common\model\MemberModel;
use app\common\model\SurveyMemberModel;
use app\common\service\LoginService;
use app\common\service\WechatService;
use think\facade\Request;

class LoginSer extends BaseSer
{
    /**
     * 小程序登录
     * @param array $param(loginCode, phoneCode, pid)
     * loginCode 小程序code
     * phoneCode 手机号code
     * pid 推荐人id
     */
    public function miniappLogin(array $param): array
    {
        try {
            if (isset($param['loginCode']) && !empty($param['loginCode'])) {
                //获取手机号
                $phone = '';
                $wechat = new WechatService();
                if (isset($param['phoneCode']) && !empty($param['phoneCode'])) {
                    $accessToken = $wechat->getAccessToken('miniapp', 'login');
                    list($isOk, $res) = $wechat->getMiniAppPhone($param['phoneCode'], $accessToken); //获取手机号
                    if (!$isOk) {
                        return fail(Response::WX_LOGIN_ERROR, $res);
                    }
                    $phone = $res;
                }

                //添加会员
                $pid = $param['pid'] ?? 0;
                if ($pid > 0) {
                    $parentMember = MemberModel::where('id', $pid)->field('id,is_fx')->find();
                    if (empty($parentMember)) {
                        $pid = 0;
                    }
                }

                $bdid = $param['bdid'] ?? '';
                //获取微信信息
                $data = $wechat->getMiniAppInfo($param['loginCode']);
                if (isset($data['openid'])) {
                    $unionid = $data['unionid'] ?? '';
                    $miniappMember = MemberModel::where('miniapp_openid', $data['openid'])->field('id')->find();
                    if (empty($miniappMember)) {
                        $miniappMember = MemberModel::where('mobile', $phone)->where('miniapp_openid', '')->field('id')->find();
                        if (empty($miniappMember)) {
                            //新会员
                            try {
                                $member = empty($unionid) ? [] : MemberModel::where('uuid', $unionid)->field('id')->find();
                                if (!empty($member)) {
                                    //其他端登录过
                                    MemberModel::where('uuid', $unionid)
                                        ->update(['miniapp_openid' => $data['openid'], 'mobile' => $phone]);
                                    $memberId = $member['id'];
                                } else {
                                    $memberId = MemberModel::insertGetId([
                                        'uuid' => $unionid,
                                        'mobile' => $phone,
                                        'pid' => $pid,
                                        'miniapp_openid' => $data['openid'],
                                        'is_work' => 1,
                                        'bdid' =>  $bdid,
                                        'register_time' => time()
                                    ]);
                                }
                                //TODO 注册成功后事件
                                return $this->setLogin($memberId, true, $param);
                            } catch (\Exception $e) {
                                return fail(Response::WX_LOGIN_ERROR, $e->getMessage());
                            }
                        } else {
                            // 店铺导入的客户, 绑定openid
                            MemberModel::where('id', $miniappMember['id'])
                                ->update([
                                    'uuid' => $unionid,
                                    'miniapp_openid' => $data['openid'],
                                    'is_work' => 1,
                                    'register_time' => time()
                                ]);
                            //TODO 注册成功后事件
                            return $this->setLogin($miniappMember['id'], false, $param);
                        }
                    } else {
                        //老会员
                        return $this->setLogin($miniappMember['id'], false, $param);
                    }
                } else {
                    return fail(Response::WX_LOGIN_ERROR, $data['errmsg']);
                }
            } else {
                return fail(Response::WX_LOGIN_ERROR, 'loginCode不能为空');
            }
        } catch (\Exception $e) {
            return fail(Response::WX_LOGIN_ERROR, $e->getMessage());
        }
    }

    public function setLogin(int $memberId = 0, bool $isNew = false, array $param = []): array
    {
        $member = MemberModel::where('id', $memberId)
            ->field('id,nickname,mobile,avatar,level_id,is_vip,vip_level,vip_effective_date,is_work,login_times,pid')
            ->find();
        if (empty($member)) {
            return fail(Response::MEMBER_NOT_EXIST);
        }
        if ($member['is_work'] == 0) {
            return fail(Response::MEMBER_NOT_WORK);
        }
        $role = 'member';
        $service = new LoginService();
        $token = $service->createToken($role, $member['id']);
        if (empty($token)) {
            return fail(Response::MEMBER_LOGIN_FAIL);
        }
        //更新登录信息
        $ip = Request::ip();
        $editMember = [
            'last_login_time' => time(),
            'last_login_ip' => $ip,
            'login_times' => $member['login_times'] + 1,
        ];
        $survey = SurveyMemberModel::where('deviceid', BaseSer::$deviceId)->where('member_id', 0)->field('id,service_object')->findOrEmpty();
        if (!$survey->isEmpty()) {
            SurveyMemberModel::where('id', $survey['id'])->update(['member_id' => $member['id']]);
            $editMember['service_object'] = $survey->service_object;
        }
        MemberModel::where('id', $member['id'])->update($editMember);
        //保存登录信息
        $service->setLogin($role, $token['token'],  $member->toArray());
        //记录设备信息
        MemberDeviceModel::insert([
            'member_id' => $member['id'],
            'platform' => in_array(BaseSer::$platformStr, MemberDeviceModel::TYPE_PLATFORM) ? BaseSer::$platformStr : 'OTHER',
            'device_id' => BaseSer::$deviceId,
            'mobile' => $member['mobile'],
            'login_ip' => $ip,
        ]);
        if ($isNew) {
            $this->registerSuccess($token['token'], $member['id'], $member['pid']);
        }
        ActivityShareRecordModel::addOneData($param['activity_id'], $param['pid'], $member['id'], $ip, BaseSer::$deviceId, $param['token']);
        return success($token);
    }

    // 新用户注册成功后
    public function registerSuccess(string $token, int $memberId = 0, int $invitedMemberId = 0): bool
    {
        try {
            $config = config('api.services.Member');
            $url = $config['BaseURL'] . $config['Api']['NewMember'];
            $data = [
                'member_id' => $memberId,
                'invited_member_id' => $invitedMemberId,
            ];
            $res = curlPostApiContentByUrlAndParams($url, $data, ['Authorization: Bearer ' . $token], false);
            if (empty($res)) {
                return false;
            }
            if ($res['code'] != 1) {
                return false;
            }
            return true;
        } catch (\Exception $e) {
            echo $e->getMessage();
            exit;
            return false;
        }
    }
}
