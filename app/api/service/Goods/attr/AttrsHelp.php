<?php

namespace app\api\service\Goods\attr;

use app\api\service\Goods\scenes\ScenesSimilarList;
use app\common\service\UploadService;

trait AttrsHelp
{
    protected function sku($simpleData = [])
    {
        $goodsId = $simpleData['goods_id'] ?? 0;
        $retData = $this->baseData->getData('simpleSku', $goodsId);
        // $retData      = array_slice($retData, 0, 3);
        return $retData ?? [];
    }


    // 服务人员
    protected function service_person($simpleData = [])
    {
        $data = [];
        if (isset($simpleData['shop_id'])) {
            $staffList = $this->baseData->getData('staffList', $simpleData['shop_id']);
            $rows = $staffList[$simpleData['basic_category_id']] ?? [];
            $exist = [];
            foreach ($rows as $key => $row) {
                if (!in_array($row['id'], $exist)) {
                    $exist[] = $row['id'];
                    $data[] = $row;
                }
            }
        }
        return $data;
    }

    // 服务年限
    protected function service_year($simpleData = [])
    {
        if (isset($simpleData['shop_id'])) {
            $shop            = $this->baseData->getData('shop', $simpleData['shop_id']);
            $start_work_year = intval($shop['start_work_year'] ?? 0);
            if ($start_work_year > 0) {
                return (date('Y') - $start_work_year) . "年";
            }
        }
        return '暂无';
    }

    /**
     * @param array $simpleData
     * @return array
     * 相似商品
     */
    protected function similar_goods($simpleData = [])
    {
        $goodsId        = $simpleData['goods_id'] ?? '';
        $similarGoodsId = $this->attrs['similarGoods'] ?? $this->baseData->getData('similarGoods', $goodsId);
        if (empty($similarGoodsId)) {
            return [];
        }

        $saveScenes   = clone $this->scenes; //保存场景
        $this->scenes = new ScenesSimilarList(); //重新设置场景
        $retData      = [];
        foreach ($similarGoodsId as $tmpPid) {
            $this->getAttrs($retData, $tmpPid, $this->attrs);
        }
        $this->scenes = $saveScenes; //还原场景
        // $retData      = array_filter($retData, function ($goods) {});
        $retData = array_slice($retData, 0, 3);
        return $retData;
    }

    protected function shop($simpleData = [])
    {
        $returnData['contact_phone'] = ''; //店铺联系方式
        if (isset($simpleData['shop_id'])) {
            $shop          = $this->baseData->getData('shop', $simpleData['shop_id']);
            $contact_phone = intval($shop['contact_phone'] ?? 0);
            if ($contact_phone > 0) {
                $returnData['contact_phone'] = $contact_phone;
            }
        }
        return $returnData;
    }

    /**
     * 商品收藏
     * @param $simpleData
     * @return mixed
     */
    protected function goods_collect($simpleData = [])
    {
        return $this->baseData->getData('memberCollect', null);
    }

    protected function goods_tags($simpleData = [])
    {
        $tagList = [];
        if (isset($simpleData['goods_tags']) && !empty($simpleData['goods_tags'])) {
            $tags = explode(',', $simpleData['goods_tags']);
            foreach ($tags as $tagId) {
                $tag = $this->baseData->getData('tags', $tagId);
                if (!empty($tag)) {
                    array_push($tagList, $tag['name']);
                }
            }
        }
        return implode(',', $tagList);
    }

    /**
     * 处理goods_auth_tags
     * @param $simpleData
     * @return array
     */
    protected function goods_auth_tags($simpleData = [])
    {
        $tagList = [];
        if (isset($simpleData['goods_auth_tags']) && !empty($simpleData['goods_auth_tags'])) {
            $tags = explode(',', $simpleData['goods_auth_tags']);
            foreach ($tags as $tagId) {
                $tag = $this->baseData->getData('tags', $tagId);
                if (!empty($tag)) {
                    array_push($tagList, UploadService::get($tag['image_id']));
                }
            }
        }
        return $tagList;
    }

    protected function goods_image($simpleData = [])
    {
        $goodsId = $simpleData['goods_id'] ?? 0;
        $retData = $this->baseData->getData('goodsImage', $goodsId);
        return $retData;
    }
}
