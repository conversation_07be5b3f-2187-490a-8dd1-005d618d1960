<?php

namespace app\api\service;

use app\api\controller\Tag;
use app\api\service\Goods\GoodsSer;
use app\common\model\ServiceZoneModel;
use app\common\model\ShopBannerModel;
use app\common\model\ShopModel;
use app\common\model\StaffModel;
use app\common\service\GoodsCommentService;
use app\common\service\RegionService;
use app\common\service\TagService;
use app\common\service\UploadService;
use think\facade\Db;

class ShopSer extends BaseSer
{
    public static $basicColumns = [
        'id',
        'shop_name',
        'logo_id',
        'image_id',
        'contact_name',
        'contact_phone',
        'opening_hours',
        'province_id',
        'city_id',
        'area_id',
        'address',
        'longitude',
        'latitude',
        'introduce',
        'service_times',
        'start_work_year',
        'business_license_image', // 营业执照
        'auth_tag', // 店铺认证标记id
        'intro_images', //门店介绍图片
        'intro_video', //门店介绍视频
    ];

    public static function getBasicWhere()
    {
        return [
            ['audit_status', '=', ShopModel::STATUS_PASS], // 审核通过
            ['expire_time', '>', time()], // 过期时间
            ['status', '=', ShopModel::STATUS_ENABLE], // 开启
        ];
    }

    /**
     * 获取门店列表
     * 按距离排序
     * 参考文档：https://www.cnblogs.com/jiafuwei/p/5699091.html
     */
    public static function getShopList(array $ids = [], int $limit = 100)
    {
        $where = self::getBasicWhere();
        if (!empty($ids)) {
            array_unshift($where, ['id', 'in', $ids]);
        }
        $basicColumns = implode(',', self::$basicColumns);
        $shopList     = ShopModel::field($basicColumns . ',ROUND(6378.138 * 2 * ASIN(SQRT(POW(SIN((' . self::$position['latitude'] . ' * PI() / 180 - latitude * PI() / 180) / 2),2) + COS(' . self::$position['latitude'] . ' * PI() / 180) * COS(latitude * PI() / 180) * POW(SIN((' . self::$position['longitude'] . ' * PI() / 180 - longitude * PI() / 180) / 2),2))) * 1000) AS distance')
            // ->with(['staff'])
            // ->where(self::getBasicWhere())
            ->where($where)
            ->order('distance ASC') //distance 单位 m
            ->limit($limit)
            ->select()
            ->toArray();

        // 处理所有图片
        $imageIds = [];
        $imageIds = array_merge($imageIds, array_column($shopList, 'logo_id'));
        $imageIds = array_merge($imageIds, array_column($shopList, 'image_id'));
        $imageIds = array_merge($imageIds, array_column($shopList, 'business_license_image'));
        $file     = UploadService::getByIds($imageIds, "200x200");

        // 处理店铺基础数据
        foreach ($shopList as $key => &$row) {
            $row['format_logo']                   = formatLogo($file[$row['logo_id']] ?? '');
            $row['format_image']                  = formatLogo($file[$row['image_id']] ?? '');
            $row['format_business_license_image'] = formatLogo($file[$row['business_license_image']] ?? '');
            // 店铺认证标签
            $row['auth_tag']                       = $row['auth_tag'] ? self::handleAuthTags($row['auth_tag']) : [];
        }
        unset($row);
        return $shopList;
    }

    public static function getShopDetailByShopId(int $shopId = 0)
    {
        if (empty($shopId)) {
            return [];
        }
        $where        = [
            ['id', '=', $shopId]
        ];
        $where        = array_merge($where, self::getBasicWhere());
        $basicColumns = implode(',', self::$basicColumns);
        $info         = ShopModel::field($basicColumns . ',ROUND(6378.138 * 2 * ASIN(SQRT(POW(SIN((' . self::$position['latitude'] . ' * PI() / 180 - latitude * PI() / 180) / 2),2) + COS(' . self::$position['latitude'] . ' * PI() / 180) * COS(latitude * PI() / 180) * POW(SIN((' . self::$position['longitude'] . ' * PI() / 180 - longitude * PI() / 180) / 2),2))) * 1000) AS distance')
            ->where($where)
            ->findOrEmpty();
        if ($info->isEmpty()) {
            return [];
        }
        //添加service_zone_name（店铺服务区域）返回字段
        if (!empty($info->zone)) {
            $regionId                = $info->zone[0]->region_id ?? 0;
            $info->service_zone_name = $regionId != 0 ? ((new RegionService())->getByRegionId($regionId))['merger_name'] : '';
        } else {
            $info->service_zone_name = '';
        }
        $info = $info->toArray();

        $fids = [
            $info['logo_id'],
            $info['image_id'],
            $info['business_license_image'],
        ];
        // intro_images门店介绍图片ID集合，多个用逗号分隔
        if (!empty($info['intro_images'])) {
            $intro_images =  explode(',', $info['intro_images']);
            $fids = array_merge($fids,  $intro_images);
        }
        // intro_video门店介绍视频ID
        if (!empty($info['intro_video'])) {
            $fids[] = $info['intro_video'];
        }
        // 处理所有图片
        $file                                  = UploadService::getByIds($fids);
        $info['manage_tag']                    = self::getManageTag($shopId); //店铺经营范围标签
        $info['auth_tag']                      = $info['auth_tag'] ? self::handleAuthTags($info['auth_tag']) : []; //店铺认证标签
        $info['format_logo']                   = formatLogo($file[$info['logo_id']] ?? '');
        $info['format_image']                  = formatLogo($file[$info['image_id']] ?? '');
        $info['format_business_license_image'] = formatLogo($file[$info['business_license_image']] ?? '');
        $info['contact_phone']                 = $info['contact_phone'] ?? '';
        //  $intro_images门店介绍图片
        if (!empty($info['intro_images'])) {
            $info['format_intro_images'] = array_map(function ($item) use ($file) {
                return formatLogo($file[$item] ?? '');
            }, $intro_images);
        }
        // intro_video门店介绍视频
        if (!empty($info['intro_video'])) {
            $info['format_intro_video'] = formatLogo($file[$info['intro_video']] ?? '');
        }
        // 店铺服务区域
        return $info;
    }

    /**
     * 店铺经营范围标签
     * 设计的优点，动态获取数据，不需要额外的shop表字段，自动维护；缺点：脱离了tag_type和tag表所有标签统一管理，暂定此方案，后续再优化。
     * @param int $shopId
     * @return array
     */
    public static function getManageTag(int $shopId = 0): array
    {
        if (empty($shopId)) {
            return [];
        }

        return Db::table('service_zone_apply')
            ->join('goods_category', 'service_zone_apply.c_id = goods_category.c_id')
            ->join('goods_category_basic', 'goods_category.first_category_id = goods_category_basic.c_id')
            ->where('service_zone_apply.shop_id', $shopId)
            ->group("goods_category_basic.name")
            ->column('goods_category_basic.name');
    }

    public static function getShopDetailForWebsite(int $shopId = 0): array
    {
        $data      = [
            'banner'    => [], //banner
            'goods'     => [], //服务项目
            'introduce' => '', //介绍
        ];
        $banner    = ShopBannerModel::where('shop_id', $shopId)->field('id, url, image_id')->order('sort', 'desc')->limit(5)->select()->toArray();
        $bannerIds = empty($banner) ? [] : array_column($banner, 'image_id');
        $file      = UploadService::getByIds($bannerIds);
        if ($banner) {
            foreach ($banner as $key => &$row) {
                $row['format_image'] = formatLogo($file[$row['image_id']] ?? '');
            }
            unset($row);
            $data['banner'] = $banner;
        }
        $shop = ShopModel::where('id', $shopId)->field(self::$basicColumns)->findOrEmpty();
        if (!$shop->isEmpty()) {
            $region = new RegionService();
            $area   = $region->getRegionDetail($shop->area_id);
            if ($area) {
                $shop->area_name = $area['province_name'] . $area['city_name'] . $area['district_name'];
            }
            $serviceZoneRes = ServiceZoneModel::where('shop_id', $shopId)->value('district_id');
            $serviceZoneId  = (!empty($serviceZoneRes) && is_array($serviceZoneRes)) ? $serviceZoneRes[0] : $serviceZoneRes;
            $region->getRegionDetail($serviceZoneId);
            $shop->service_zone = $region->getRegionDetail($serviceZoneId);
            $shop               = $shop->toArray();
            $data               = array_merge($data, $shop);
        }
        // 服务项目
        $goods    = (new GoodsSer('Shop', ['shop_id' => $shopId]))->getData(['limit' => 20]);
        $goodsIds = array_column($goods, 'id');
        $data['goods'] = [];
        $data['commonent'] = [];
        if ($goodsIds) {
            $data['goods'] = $goods;
            // 获取评价
            $commonentService  = new GoodsCommentService();
            $data['commonent'] = $commonentService->list(['goods_id' => $goodsIds]);
        }
        return $data;
    }

    /**
     * @param int $shopId
     * @param $field
     * @return ShopModel
     */
    public static function getShopDetail(int $shopId = 0, $field = '*')
    {
        return ShopModel::field($field)->findOrEmpty($shopId);
    }

    /**
     * 处理认证标签图片
     * @param string $authTag
     * @return array
     */
    public static function handleAuthTags(string $authTag = ''): array
    {
        if (empty($authTag)) {
            return [];
        }
        try {
            // 分割标签ID
            $tagIds = array_filter(explode(',', $authTag));
            if (empty($tagIds)) {
                return [];
            }
            // 批量获取图片URL
            return TagService::getByIds($tagIds);
        } catch (\Exception $e) {
            return [];
        }
    }
}
