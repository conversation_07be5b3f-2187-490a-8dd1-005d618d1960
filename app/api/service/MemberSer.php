<?php

namespace app\api\service;

use app\api\controller\Coupon;
use app\common\enum\Response;
use app\common\model\InterviewScheduleModel;
use app\common\model\MemberCollectModel;
use app\common\model\MemberFetalMovementRecordModel;
use app\common\model\MemberModel;
use app\common\model\OrderCommentModel;
use app\common\model\OrdersModel;

class MemberSer extends BaseSer
{
    public function detail(int $memberId = 0, $field = '')
    {
        if ($field == '') {
            $field = 'id,nickname,avatar,mobile,service_object,real_name,id_card_type,id_card_number,auth_status,auth_reject_reason';
        }
        $member = MemberModel::where('id', $memberId)->field($field)->findOrEmpty();
        if ($member->isEmpty()) {
            return fail(Response::MEMBER_NOT_EXIST);
        }
        return $member;
    }

    public function info(int $memberId = 0, string $token = ''): array
    {
        $member = $this->detail($memberId);
        // 我的收藏
        $collect = MemberCollectModel::where('member_id', $memberId)->count();
        // 我的订单
        $orderList = OrdersModel::where('member_id', $memberId)
            ->where('status', OrdersModel::StatusActive)
            ->field('order_id,member_id,pay_status,status,confirm,start_service_time')
            ->select();

        $waitConfirm = 0; //待确认
        $waitPay     = 0; //待付款
        $waitService = 0; //待服务
        $service     = 0; // 服务中
        $waitComment = 0; //待评价
        //app\api\service\Order\OrderSer.php:line:53
        /*if ($selected == 'wait_pay') {
            $where[] = ['pay_status', '=', OrdersModel::PayStatusUnpaid];
            $where[] = ['status', '=', OrdersModel::StatusActive];
        }
        if ($selected == 'wait_confirm') {
            $where[] = ['pay_status', '>', OrdersModel::PayStatusUnpaid];
            $where[] = ['confirm', '=', OrdersModel::ConfirmUnconfirmed];
            $where[] = ['status', '=', OrdersModel::StatusActive];
        }
        if ($selected == 'wait_service') {
            $where[] = ['pay_status', '>', OrdersModel::PayStatusUnpaid];
            $where[] = ['confirm', '>', OrdersModel::ConfirmUnconfirmed];
            $where[] = ['status', '=', OrdersModel::StatusActive];
            $where[] = ['start_service_time', '=', null];
        }
        if ($selected == 'service') {
            $where[] = ['pay_status', '>', OrdersModel::PayStatusUnpaid];
            $where[] = ['confirm', '=', OrdersModel::ConfirmContractConfirmed];
            $where[] = ['status', '=', OrdersModel::StatusActive];
            $where[] = ['start_service_time', '<', date('Y-m-d H:i:s')];
        }
        if ($selected == 'finish') {
            $where[] = ['status', '=', OrdersModel::StatusFinish];
        }*/
        foreach ($orderList as $order) {
            if ($order->pay_status == OrdersModel::PayStatusUnpaid && $order->status == OrdersModel::StatusActive) {
                $waitPay++;
            }
            if (in_array($order->pay_status, [OrdersModel::PayStatusPaid, OrdersModel::PayStatusPaidToGuarantee, OrdersModel::PayStatusPartPaid]) && $order->confirm == OrdersModel::ConfirmUnconfirmed && $order->status == OrdersModel::StatusActive) {
                $waitConfirm++;
            }
            if (in_array($order->pay_status, [OrdersModel::PayStatusPaid, OrdersModel::PayStatusPaidToGuarantee, OrdersModel::PayStatusPartPaid]) && $order->confirm > OrdersModel::ConfirmUnconfirmed && $order->status == OrdersModel::StatusActive && $order->start_service_time == '') {
                $waitService++;
            }
            if (in_array($order->pay_status, [OrdersModel::PayStatusPaid, OrdersModel::PayStatusPaidToGuarantee, OrdersModel::PayStatusPartPaid]) && $order->confirm == OrdersModel::ConfirmContractConfirmed && $order->status == OrdersModel::StatusActive && $order->start_service_time != '' && $order->start_service_time < date('Y-m-d H:i:s')) {
                $service++;
            }
        }
        // 待评价
        $waitComment = OrderCommentModel::where('member_id', $memberId)->where('comment_status', 0)->count();

        // 我的面试
        $interview = InterviewScheduleModel::where('interviewer_member_id', $memberId)
            ->where('interview_time', '>=', date('Y-m-d 00:00:00'))
            ->field('id,interviewer_member_id,interview_time,status,room_id')
            ->select();

        $wait_start   = 0;
        $wait_confirm = 0;
        $wait_service = 0;
        // 即将开始的面试记录
        $start_interview = [
            'room_id'        => 0,
            'interview_time' => '',
        ];


        foreach ($interview as $row) {
            if ($row->status == InterviewScheduleModel::STATUS_SCHEDULED) {
                $wait_start++;
                // 5分钟内的面试
                if ($row->interview_time > date('Y-m-d H:i:s') && $row->interview_time <= date('Y-m-d H:i:s', strtotime('+5 minutes'))) {
                    $start_interview = [
                        'room_id'        => $row->room_id,
                        'interview_time' => $row->interview_time,
                    ];
                }
            }
            if ($row->status == InterviewScheduleModel::STATUS_IN_PROGRESS) {
                $wait_confirm++;
            }
            if ($row->status == InterviewScheduleModel::STATUS_COMPLETED) {
                $wait_service++;
            }
        }

        // 获取会员积分
        $memberIntegral = [];
        $config = config('api.services.Member');
        $url = $config['BaseURL'] . $config['Api']['GetMemberIntegral'];
        $res = curlGetApiContentByUrl($url, [], ['Authorization: Bearer ' . $token], false);
        if (!empty($res) && $res['code'] == 1) {
            $memberIntegral = $res['data'];
        }
        $data = [
            'member_id' => $memberId,
            'member_integral' => $memberIntegral, //会员积分
            'object_name' => $member->service_object ?? '',
            'mobile'    => $member->mobile,
            'nickname'  => $member->nickname,
            'avatar'    => formatAvatar($member->avatar),
            //real_name,id_card_type,id_card_number,auth_status
            'real_name'      => $member->real_name,
            'id_card_type'   => $member->id_card_type,
            'id_card_number' => $member->id_card_number,
            'auth_status'    => $member->auth_status,
            'auth_reject_reason' => $member->auth_reject_reason,
            'service_object' => [
                [
                    'key' => 'adult',
                    'value' => '女性',
                ],
                [
                    'key' => 'baby',
                    'value' => '婴幼儿',
                ],
                // [
                //     'key' => 'old',
                //     'value' => '老人',
                // ],
            ],
            // 优惠券
             'coupon'    => [
                 'count' => 0,
                 'tips'  => '',
             ],
            // 我的收藏
            'collect'   => [
                'count' => $collect,
                'tips'  => '',
            ],
            // // 我的足迹
            // 'footprint' => [
            //     'count' => 0,
            //     'tips'  => '暂无足迹',
            // ],
            // 我的订单
            'order'     => [
                'wait_confirm' => $waitConfirm, //待确认
                'wait_pay'     => $waitPay, //待付款
                'wait_service' => $waitService, //待服务
                'service'      => $service, // 服务中
                'wait_comment' => $waitComment, //待评价
            ],
            // 我的面试
            'interview' => [
                // 已经开始的房间
                'start_interview' => $start_interview,
                'wait_start'      => $wait_start, //待开始
                'wait_confirm'    => $wait_confirm, //待确认
                'wait_service'    => $wait_service, //待服务
            ],
            // 我的工具
            'tool'      => [
                // 我的地址
                [
                    'icon' => 'https://oss.jiangsuyishengai.com/img/util_wddz.png',
                    'name' => '我的地址',
                    'type' => 'page',
                    'url'  => '/service/address/list',
                ],
                // // 我的预约
                // [
                //     'icon' => 'https://oss.jiangsuyishengai.com/img/util_wdyy.png',
                //     'name' => '我的预约',
                //     'type' => 'page',
                //     'url'  => '',
                // ],
                // 我的收藏
                [
                    'icon' => 'https://oss.jiangsuyishengai.com/img/util_wdsc.png',
                    'name' => '我的收藏',
                    'type' => 'page',
                    'url'  => '/service/my/collect',
                ],
                // // 成为达人
                // [
                //     'icon' => 'https://oss.jiangsuyishengai.com/img/util_cwdr.png',
                //     'name' => '成为达人',
                //     'type' => 'page',
                //     'url'  => '',
                // ],
                // // 母婴学社
                // [
                //     'icon' => 'https://oss.jiangsuyishengai.com/img/util_mydx.png',
                //     'name' => '母婴学社',
                //     'type' => 'page',
                //     'url'  => '',
                // ],
                // // 妈妈创业
                // [
                //     'icon' => 'https://oss.jiangsuyishengai.com/img/util_mmcy.png',
                //     'name' => '妈妈创业',
                //     'type' => 'page',
                //     'url'  => '',
                // ],
                // 加入我们
                // [
                //     'icon' => 'https://oss.jiangsuyishengai.com/img/util_jrwm.png',
                //     'name' => '加入我们',
                //     'type' => 'page',
                //     'url'  => '',
                // ],
                // 联系客服
                [
                    'icon' => 'https://oss.jiangsuyishengai.com/img/util_lxkf.png',
                    'name' => '联系客服',
                    'type' => 'button',
                    'url'  => 'contact_customer_service',
                ],
            ],
        ];
        return success($data);
    }

    public function myEdit(array $param = [], string &$msg = ''): bool
    {
        $member = MemberModel::where('id', $param['member_id'])->find();
        if (!$member) {
            $msg = '用户不存在';
            return false;
        }
        $isUpdate = false;
        if (isset($param['nickname']) && $param['nickname'] != '') {
            $member->nickname = trim($param['nickname']);
            $isUpdate         = true;
        }

        if (isset($param['avatar']) && $param['avatar'] != '') {
            $member->avatar = trim($param['avatar']);
            $isUpdate       = true;
        }

        if (isset($param['service_object']) && $param['service_object'] != '') {
            $member->service_object = trim($param['service_object']);
            $isUpdate       = true;
        }
        // 用户身份信息
        $upCard = false;
        if (isset($param['real_name']) && $param['real_name'] != '') {
            $member->real_name = trim($param['real_name']);
            $isUpdate       = true;
            $upCard =   true;
        }
        if (isset($param['id_card_type']) && $param['id_card_type'] != '') {
            $member->id_card_type = trim($param['id_card_type']);
            $isUpdate       = true;
            $upCard =   true;
        }
        if (isset($param['id_card_number']) && $param['id_card_number'] != '') {
            $member->id_card_number = trim($param['id_card_number']);
            $isUpdate       = true;
            $upCard =   true;
        }
        if (isset($param['id_card_front']) && $param['id_card_front'] != '') {
            $member->id_card_front = trim($param['id_card_front']);
            $isUpdate       = true;
            $upCard =   true;
        }
        if (isset($param['id_card_back']) && $param['id_card_back'] != '') {
            $member->id_card_back = trim($param['id_card_back']);
            $isUpdate       = true;
            $upCard =   true;
        }
        // 申请认证
        if ($upCard && $member->real_name && $member->id_card_type && $member->id_card_number && $member->id_card_front && $member->id_card_back) {
            $member->auth_status = 1;
            $isUpdate = true;
        }
        // 用户身份信息End
        if ($isUpdate) {
            $member->save();
        }
        return true;
    }

    public static function SaveRecord(array $param): bool
    {
        $memberId = intval($param['member_id'] ?? 0);
        if ($memberId <= 0) {
            return false;
        }
        $data = [
            'member_id' => $memberId,
            'start_time' => $param['start_time'] ?? '',
            'end_time' => $param['end_time'] ?? '',
            'movement_count' => intval($param['num'] ?? 0),
            'duration_sec' => intval($param['run_time'] ?? 0),
            'created_at' => date('Y-m-d H:i:s'),
        ];
        $insertId = MemberFetalMovementRecordModel::insertGetId($data);
        if ($insertId <= 0) {
            return false;
        }
        return true;
    }

    public static function GetRecordList(int $memberId, $last_id =  0): array
    {
        $query =  MemberFetalMovementRecordModel::where('member_id', $memberId);
        if ($last_id > 0) {
            $query->where('id', '<', $last_id);
        }
        return $query->order('created_at', 'desc')
            ->limit(100)
            ->select()
            ->toArray();
    }
}
