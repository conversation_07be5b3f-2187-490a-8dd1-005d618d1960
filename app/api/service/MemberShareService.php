<?php

namespace app\api\service;

use app\common\enum\Response;
use app\common\model\MemberShareModel;
use app\common\model\MemberShareClickModel;
use app\common\model\MemberModel;
use app\common\service\RegionService;
use think\facade\Request;
use think\facade\Cache;

class MemberShareService
{
    /**
     * 创建分享记录
     */
    public function createShare(array $param): array
    {
        try {
            // 验证必要参数
            if (empty($param['sharer_id']) || empty($param['share_type']) || empty($param['object_id'])) {
                return fail(Response::REQUEST_PARAM_ERROR, '缺少必要参数');
            }

            // 验证分享者是否存在
            $sharer = MemberModel::where('id', $param['sharer_id'])->find();
            if (!$sharer) {
                return fail(Response::MEMBER_NOT_EXIST, '分享者不存在');
            }

            // 生成唯一分享ID
            $shareId = MemberShareModel::generateShareId();

            // 获取地理位置信息
            $address = '';
            if (!empty($param['longitude']) && !empty($param['latitude'])) {
                $regionService = new RegionService();
                $addressInfo = $regionService->getAddressByLngLat($param['longitude'], $param['latitude']);
                $address = $addressInfo['formatted_address'] ?? '';
            }

            // 构建分享数据
            $shareData = [
                'share_id' => $shareId,
                'sharer_id' => $param['sharer_id'],
                'share_type' => intval($param['share_type']),
                'object_id' => intval($param['object_id']),
                'object_name' => $param['object_name'] ?? '',
                'share_channel' => intval($param['share_channel'] ?? MemberShareModel::SHARE_CHANNEL_OTHER),
                'share_time' => date('Y-m-d H:i:s'),
                'share_path' => $param['share_path'] ?? '',
                'share_params' => !empty($param['share_params']) ? json_encode($param['share_params']) : null,
                'expire_time' => !empty($param['expire_time']) ? date('Y-m-d H:i:s', $param['expire_time']) : null,
                'status' => MemberShareModel::STATUS_VALID,
                'longitude' => !empty($param['longitude']) ? floatval($param['longitude']) : null,
                'latitude' => !empty($param['latitude']) ? floatval($param['latitude']) : null,
                'area_id' => $param['area_id'] ?? null,
                'address' => $address,
                'device_id' => $param['deviceid'] ?? null,
                'device_model' => $param['device_model'] ?? null,
                'device_platform' => $this->getDevicePlatform($param['platform'] ?? ''),
                'app_version' => $param['version'] ?? null,
                'ip_address' => Request::ip(),
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s'),
            ];

            $shareModel = new MemberShareModel();
            $result = $shareModel->save($shareData);

            if (!$result) {
                return fail(Response::ERROR, '创建分享记录失败');
            }

            return success([
                'share_id' => $shareId,
                'share_url' => $this->generateShareUrl($shareId, $param),
            ]);

        } catch (\Exception $e) {
            return fail(Response::ERROR, '创建分享记录异常：' . $e->getMessage());
        }
    }

    /**
     * 记录分享点击
     */
    public function recordClick(array $param): array
    {
        try {
            // 验证分享ID
            if (empty($param['share_id'])) {
                return fail(Response::REQUEST_PARAM_ERROR, '分享ID不能为空');
            }

            // 查找分享记录
            $share = MemberShareModel::where('share_id', $param['share_id'])->find();
            if (!$share) {
                return fail(Response::ERROR, '分享记录不存在');
            }

            // 检查分享是否有效
            if (!$share->isValid()) {
                return fail(Response::ERROR, '分享链接已失效');
            }

            // 防重复点击检查（同一设备1分钟内只记录一次）
            $cacheKey = "share_click:{$param['share_id']}:{$param['deviceid']}";
            if (Cache::get($cacheKey)) {
                return success(['message' => '重复点击，已忽略']);
            }

            // 获取该分享的点击次数
            $clickNo = MemberShareClickModel::where('share_id', $param['share_id'])->count() + 1;

            // 检查是否为新用户
            $isNewUser = 0;
            $isFirstClick = 0;
            $memberId = intval($param['member_id'] ?? 0);

            if ($memberId > 0) {
                // 检查用户注册时间（如果是今天注册的，认为是新用户）
                $member = MemberModel::where('id', $memberId)->find();
                if ($member && date('Y-m-d', $member->register_time) == date('Y-m-d')) {
                    $isNewUser = 1;
                }

                // 检查是否为该用户对此分享的首次点击
                $existClick = MemberShareClickModel::where('share_id', $param['share_id'])
                    ->where('member_id', $memberId)
                    ->find();
                if (!$existClick) {
                    $isFirstClick = 1;
                }
            } else {
                // 未登录用户，检查设备是否首次点击
                $existClick = MemberShareClickModel::where('share_id', $param['share_id'])
                    ->where('device_id', $param['deviceid'])
                    ->find();
                if (!$existClick) {
                    $isFirstClick = 1;
                }
            }

            // 获取地理位置信息
            $address = '';
            if (!empty($param['longitude']) && !empty($param['latitude'])) {
                $regionService = new RegionService();
                $addressInfo = $regionService->getAddressByLngLat($param['longitude'], $param['latitude']);
                $address = $addressInfo['formatted_address'] ?? '';
            }

            // 构建点击数据
            $clickData = [
                'share_id' => $param['share_id'],
                'click_no' => $clickNo,
                'member_id' => $memberId > 0 ? $memberId : null,
                'is_new_user' => $isNewUser,
                'click_time' => date('Y-m-d H:i:s'),
                'is_first_click' => $isFirstClick,
                'enter_path' => $param['enter_path'] ?? null,
                'referrer' => $param['referrer'] ?? null,
                'scene' => $param['scene'] ?? null,
                'longitude' => !empty($param['longitude']) ? floatval($param['longitude']) : null,
                'latitude' => !empty($param['latitude']) ? floatval($param['latitude']) : null,
                'area_id' => $param['area_id'] ?? null,
                'address' => $address,
                'device_id' => $param['deviceid'] ?? null,
                'device_model' => $param['device_model'] ?? null,
                'device_platform' => $this->getDevicePlatform($param['platform'] ?? ''),
                'network_type' => $param['network_type'] ?? null,
                'app_version' => $param['version'] ?? null,
                'ip_address' => Request::ip(),
                'user_agent' => Request::header('User-Agent'),
                'conversion_status' => MemberShareClickModel::CONVERSION_STATUS_NONE,
                'created_at' => date('Y-m-d H:i:s'),
            ];

            $clickModel = new MemberShareClickModel();
            $result = $clickModel->save($clickData);

            if (!$result) {
                return fail(Response::ERROR, '记录点击失败');
            }

            // 设置防重复点击缓存（1分钟）
            Cache::set($cacheKey, 1, 60);

            return success([
                'click_id' => $clickModel->id,
                'share_info' => [
                    'share_type' => $share->share_type,
                    'object_id' => $share->object_id,
                    'object_name' => $share->object_name,
                    'share_path' => $share->share_path,
                ]
            ]);

        } catch (\Exception $e) {
            return fail(Response::ERROR, '记录点击异常：' . $e->getMessage());
        }
    }

    /**
     * 获取设备平台
     */
    private function getDevicePlatform($platform): string
    {
        $platform = strtolower($platform);
        switch ($platform) {
            case 'ios':
                return MemberShareModel::PLATFORM_IOS;
            case 'android':
                return MemberShareModel::PLATFORM_ANDROID;
            case 'windows':
                return MemberShareModel::PLATFORM_WINDOWS;
            case 'mac':
                return MemberShareModel::PLATFORM_MAC;
            default:
                return MemberShareModel::PLATFORM_OTHER;
        }
    }

    /**
     * 生成分享链接
     */
    private function generateShareUrl($shareId, $param): string
    {
        $baseUrl = config('app.app_host', '');
        $path = $param['share_path'] ?? '/pages/index/index';
        
        // 添加分享参数
        $query = [
            'share_id' => $shareId,
            'from_share' => 1,
        ];

        // 如果有额外参数，合并进去
        if (!empty($param['share_params']) && is_array($param['share_params'])) {
            $query = array_merge($query, $param['share_params']);
        }

        return $baseUrl . $path . '?' . http_build_query($query);
    }

    /**
     * 获取分享统计信息
     */
    public function getShareStats($shareId): array
    {
        try {
            $share = MemberShareModel::where('share_id', $shareId)->find();
            if (!$share) {
                return fail(Response::ERROR, '分享记录不存在');
            }

            // 总点击数
            $totalClicks = MemberShareClickModel::where('share_id', $shareId)->count();

            // 独立访客数（按设备ID去重）
            $uniqueVisitors = MemberShareClickModel::where('share_id', $shareId)
                ->group('device_id')
                ->count();

            // 新用户数
            $newUsers = MemberShareClickModel::where('share_id', $shareId)
                ->where('is_new_user', 1)
                ->count();

            // 转化数
            $conversions = MemberShareClickModel::where('share_id', $shareId)
                ->where('conversion_status', MemberShareClickModel::CONVERSION_STATUS_CONVERTED)
                ->count();

            return success([
                'share_id' => $shareId,
                'total_clicks' => $totalClicks,
                'unique_visitors' => $uniqueVisitors,
                'new_users' => $newUsers,
                'conversions' => $conversions,
                'conversion_rate' => $totalClicks > 0 ? round($conversions / $totalClicks * 100, 2) : 0,
                'share_info' => [
                    'share_type' => $share->share_type,
                    'share_type_text' => $share->share_type_text,
                    'object_name' => $share->object_name,
                    'share_time' => $share->format_share_time,
                    'status' => $share->status,
                    'status_text' => $share->status_text,
                ]
            ]);

        } catch (\Exception $e) {
            return fail(Response::ERROR, '获取统计信息异常：' . $e->getMessage());
        }
    }

    /**
     * 设置转化
     */
    public function setConversion($shareId, $memberId, $conversionType, $conversionId = null): array
    {
        try {
            // 查找最近的点击记录
            $click = MemberShareClickModel::where('share_id', $shareId)
                ->where('member_id', $memberId)
                ->where('conversion_status', MemberShareClickModel::CONVERSION_STATUS_NONE)
                ->order('click_time', 'desc')
                ->find();

            if (!$click) {
                return fail(Response::ERROR, '未找到可转化的点击记录');
            }

            $result = $click->setConversion($conversionType, $conversionId);

            if (!$result) {
                return fail(Response::ERROR, '设置转化失败');
            }

            return success(['message' => '转化设置成功']);

        } catch (\Exception $e) {
            return fail(Response::ERROR, '设置转化异常：' . $e->getMessage());
        }
    }

    /**
     * 获取用户的分享列表
     */
    public function getUserShares($memberId, $page = 1, $limit = 20): array
    {
        try {
            $offset = ($page - 1) * $limit;

            $shares = MemberShareModel::where('sharer_id', $memberId)
                ->order('share_time', 'desc')
                ->limit($offset, $limit)
                ->select();

            $result = [];
            foreach ($shares as $share) {
                // 获取点击统计
                $clickCount = MemberShareClickModel::where('share_id', $share->share_id)->count();
                $conversionCount = MemberShareClickModel::where('share_id', $share->share_id)
                    ->where('conversion_status', MemberShareClickModel::CONVERSION_STATUS_CONVERTED)
                    ->count();

                $result[] = [
                    'share_id' => $share->share_id,
                    'share_type' => $share->share_type,
                    'share_type_text' => $share->share_type_text,
                    'object_name' => $share->object_name,
                    'share_channel_text' => $share->share_channel_text,
                    'share_time' => $share->format_share_time,
                    'status' => $share->status,
                    'status_text' => $share->status_text,
                    'click_count' => $clickCount,
                    'conversion_count' => $conversionCount,
                    'conversion_rate' => $clickCount > 0 ? round($conversionCount / $clickCount * 100, 2) : 0,
                ];
            }

            return success($result);

        } catch (\Exception $e) {
            return fail(Response::ERROR, '获取分享列表异常：' . $e->getMessage());
        }
    }
}
