<?php

declare(strict_types=1);

namespace app\api\service;

use app\common\enum\Response;
use app\common\service\TencentMeetingRoomService;
use think\facade\Db;

class InterviewService
{
    private TencentMeetingRoomService $tencentMeeting;

    public function __construct()
    {
        $this->tencentMeeting = new TencentMeetingRoomService();
    }

    /**
     * 创建会议房间,此方法给定时任务使用
     * @param string $orderId
     * @return array 给定时任务使用的方法，则返回格式必须使用success()/fail()
     */
    public static function createRoom(string $orderId): array
    {
        //创建房间的开始时间和结束时间，会议提前15分钟开始，会议持续2小时
        $minutes     = 15; //比预约面试开始提前的分钟数
        $meetingTime = 2 * 60; //会议时长，从预约开始时间算起，单位分钟；
        $where       = [
            ['status', '=', 'scheduled'],
            ['order_id', '=', $orderId], //预约开始时间$minutes分钟创建房间
        ];
        $interview   = Db::name("interview_schedules")->field('order_id,interview_time')->where($where)->findOrEmpty();
        if (empty($interview)) {
            return success([], '订单号不需要创建房间，可能原因是订单号错误！');
        }
        $interviewTime = strtotime($interview['interview_time']);
        $result        = (new TencentMeetingRoomService())->createRoom1($interview['order_id'], [
            'interview_time' => $interviewTime,
            'start_time' => $interviewTime - ($minutes * 60), //类型为时间戳
            'end_time'   => $interviewTime + ($meetingTime * 60), //类型为时间戳
        ]);
        if (isset($result['data'])) {
            return success([], '创建房间成功，房间信息：' . arrayToJson($result['data']));
        }
        return fail(Response::REQUEST_ERROR, '创建房间失败，原因：' . arrayToJson($result));
    }

    /**
     * 解散房间
     * @param string $roomId
     * @return array|null
     */
    public function dismissRoom(string $roomId)
    {
        if (empty($roomId)) {
            return null;
        }
        return $this->tencentMeeting->getRoomInfo($roomId);
    }

    /**
     * 更新房间信息
     * @param string $roomId
     * @return array|null
     */
    public function getRoomInfo(string $roomId)
    {
        if (empty($roomId)) {
            return null;
        }
        return $this->tencentMeeting->dismissRoom($roomId);
    }

    /**
     * 更新房间信息
     * @return void
     */
    public function updateRoom()
    {
        //TODO:
    }
}
