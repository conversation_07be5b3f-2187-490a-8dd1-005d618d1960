<?php

declare(strict_types=1);

namespace app\api\middleware;

use app\common\enum\Response;
use think\facade\Cache;

class Auth
{
    //控制器白名单，不用验证登录
    protected array $noAuth = [
        'login',
        'config',
        'wx/login',
        'notify/weChatPay',
        'shop/website',
        'survey/question',
        'activity-gift/signup', //供应商活动送礼品
        'contract/signNotify',
        'contract/detail',
    ];

    // 强制要求有token
    protected array $forceAuth = [
        'cart',
        'order',
        'pay',
        'pay/wechat',
        'babyCount',
        'contract/preview',
        'wishPool',
        'member/integral', //积分
    ];

    /**
     * 处理请求
     *
     * @param \think\Request $request
     * @param \Closure       $next
     * @return Response
     */
    public function handle($request, \Closure $next)
    {
        $request->memberAuth = [];
        $route = $request->pathinfo();
        $routeName = $request->rule()->getName();
        $route =  $routeName === 'Activity/detail' ? 'Activity/detail' : $route;
        if (!in_array($route, $this->noAuth)) {
            $request->memberAuth = [
                'id' => 0,
            ];
            $headerAuth = $request->header('Authorization');
            $token = !empty($headerAuth) ? substr($headerAuth, 7) : '';
            if (empty($token)) {
                if (in_array($route, $this->forceAuth)) {
                    return json(fail(Response::MEMBER_NOT_LOGIN));
                } else {
                    return $next($request);
                }
            }
            $authId = Cache::get('member_token:' . $token);
            $isLogin = intval($authId) ? true : false;
            if (!$isLogin) {
                return json(fail(Response::MEMBER_NOT_LOGIN));
            }

            $auth = Cache::get('member_auth:' . $authId);
            if (empty($auth)) {
                return json(fail(Response::MEMBER_NOT_LOGIN));
            }
            // controller 获取 $this->request->token
            $request->token = $token;
            $request->memberAuth = $auth;
        }
        return $next($request);
    }
}
