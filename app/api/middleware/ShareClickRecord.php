<?php

namespace app\api\middleware;

use app\api\service\MemberShareService;
use think\facade\Request;
use think\facade\Log;

/**
 * 分享点击记录中间件
 * 自动记录通过分享链接访问的用户行为
 */
class ShareClickRecord
{
    /**
     * 处理请求
     *
     * @param \think\Request $request
     * @param \Closure $next
     * @return mixed
     */
    public function handle($request, \Closure $next)
    {
        // 先执行正常的请求处理
        $response = $next($request);

        // 检查是否为分享链接访问
        $shareId = $request->param('share_id', '');
        $fromShare = $request->param('from_share', 0);

        // 如果包含分享参数，则记录点击
        if (!empty($shareId) && intval($fromShare) === 1) {
            $this->recordShareClick($request, $shareId);
        }

        return $response;
    }

    /**
     * 记录分享点击
     */
    private function recordShareClick($request, $shareId)
    {
        try {
            // 获取所有参数
            $params = $request->param();
            
            // 构建点击参数
            $clickParam = [
                'share_id' => $shareId,
                'member_id' => intval($params['member_id'] ?? 0), // 可以为0（未登录用户）
                'enter_path' => $request->pathinfo(),
                'referrer' => $request->header('Referer', ''),
                'scene' => $params['scene'] ?? '',
                'longitude' => $params['longitude'] ?? '',
                'latitude' => $params['latitude'] ?? '',
                'area_id' => $params['area_id'] ?? '',
                'deviceid' => $params['deviceid'] ?? '',
                'device_model' => $params['device_model'] ?? '',
                'platform' => $params['platform'] ?? 'OTHER',
                'network_type' => $this->getNetworkType($request),
                'version' => $params['version'] ?? '',
            ];

            // 调用分享服务记录点击
            $shareService = new MemberShareService();
            $result = $shareService->recordClick($clickParam);

            // 记录日志（可选）
            if ($result['code'] !== 1) {
                Log::warning('分享点击记录失败', [
                    'share_id' => $shareId,
                    'error' => $result['msg'] ?? '未知错误',
                    'params' => $clickParam
                ]);
            }

        } catch (\Exception $e) {
            // 记录异常但不影响正常请求
            Log::error('分享点击记录异常', [
                'share_id' => $shareId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * 获取网络类型（从User-Agent或其他方式推断）
     */
    private function getNetworkType($request): string
    {
        $userAgent = $request->header('User-Agent', '');
        
        // 简单的网络类型推断逻辑
        if (stripos($userAgent, 'wifi') !== false) {
            return 'WiFi';
        } elseif (stripos($userAgent, '5g') !== false) {
            return '5G';
        } elseif (stripos($userAgent, '4g') !== false) {
            return '4G';
        } elseif (stripos($userAgent, '3g') !== false) {
            return '3G';
        } elseif (stripos($userAgent, '2g') !== false) {
            return '2G';
        }
        
        return 'Unknown';
    }
}
