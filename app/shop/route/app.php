<?php
/**
 * #############【从业端】#############
 */
use think\facade\Route;

// auth
Route::middleware(\app\shop\middleware\Auth::class);

// config
Route::get('config', 'Config/index')->name('getConfig');
// data上传
Route::post('upload/data', 'Upload/saveData')->name('uploadSaveData');
// 图片上传
Route::post('upload/image', 'Upload/image')->name('uploadImage');
// pdf上传
Route::post('upload/pdf', 'Upload/pdf')->name('uploadPdf');
// 视频上传
Route::post('upload/video', 'Upload/video')->name('uploadVideo');
// 音频上传
Route::post('upload/audio', 'Upload/audio')->name('uploadAudio');
// 富文本上传
Route::get('upload/ueditor', 'Upload/ueditorConfig')->name('ueditorUpload');
Route::post('upload/ueditor', 'Upload/ueditor')->name('ueditorUpload');

// 获取区域列表
Route::get('area', 'Area/getAreaList')->name('areaList');

// 获取省级列表
Route::get('area/province', 'Area/getProvinceList')->name('provinceList');

// 获取市级列表
Route::get('area/city', 'Area/getCityList')->name('cityList');

// 根据code联动查询
Route::get('area/linkage', 'Area/getAreaLinkage')->name('areaLinkage');

// 根据经纬度获取地址
Route::get('area/address', 'Area/getAddressByLngLat')->name('addressByLngLat');

// 根据地址获取经纬度
Route::get('area/position', 'Area/getLngLatByAddress')->name('lngLatByAddress');

// 发送短信
Route::post('sms/send', 'Login/sendSmsCode')->name('sendSms');

// 根据名字搜索门店id
Route::get('search', 'Login/getShopList')->name('searchShopByName');

// 登录
Route::post('login', 'Login/login')->name('login');
// 登出
Route::post('logout', 'Login/logout')->name('logout');
// 注册
Route::post('register', 'Login/register')->name('register');
// 重置密码
Route::put('reset', 'Login/resetPassword')->name('resetPassword');

// 获取用户信息
Route::get('user', 'User/getInfo')->name('getUserInfo');
// 获取简历
Route::get('user/resume', 'User/getResume')->name('getUserResume');
// 更新用户信息
Route::put('user', 'User/editInfo')->name('editUserInfo');
// // 上传认证证明
// Route::post('user/authn', 'User/addAuthn')->name('addStaffAuthn');
// // 编辑认证证明
// Route::put('user/:id/authn', 'User/editAuthn')->name('editStaffAuthn');
// // 删除认证证明
// Route::delete('user/:id/authn', 'User/deleteAuthn')->name('deleteStaffAuthn');
// 身份证提交
Route::post('user/idcard', 'User/addIdCard')->name('addIdCard');
// 健康证提交
Route::post('user/health', 'User/addHealth')->name('addHealth');
// 技能证提交
Route::post('user/skill', 'User/addSkill')->name('addSkill');
// 技能证编辑
Route::post('user/skill/:id', 'User/editSkill')->name('editSkill');
// 技能证删除
Route::delete('user/skill/:id', 'User/deleteSkill')->name('deleteSkill');
// 疫苗接种单提交
Route::post('user/vaccination', 'User/addVaccination')->name('addVaccination');
// 疫苗接种单编辑
Route::post('user/vaccination/:id', 'User/editVaccination')->name('editVaccination');
// 疫苗接种单删除
Route::delete('user/vaccination/:id', 'User/deleteVaccination')->name('deleteVaccination');
// 无犯罪记录提交
Route::post('user/no_crime', 'User/addNoCrime')->name('addNoCrime');
// 无犯罪记录编辑
Route::post('user/no_crime', 'User/editNoCrime')->name('editNoCrime');
// 无犯罪记录删除
Route::delete('user/no_crime', 'User/deleteNoCrime')->name('deleteNoCrime');
// 体检报告提交
Route::post('user/physical', 'User/addPhysical')->name('addPhysical');
// 体检报告编辑
Route::post('user/physical/:id', 'User/editPhysical')->name('editPhysical');
// 体检报告删除
Route::delete('user/physical/:id', 'User/deletePhysical')->name('deletePhysical');

// 获取休息时间
Route::get('user/rest', 'User/getRestTime')->name('getRestTime');
// 设置休息时间
Route::post('user/rest', 'User/setRestTime')->name('setRestTime');
// 删除休息时间
Route::delete('user/rest/:id', 'User/deleteRestTime')->name('deleteRestTime');

// 获取接单区域
Route::get('user/area', 'User/getArea')->name('getArea');
// 设置接单区域
Route::post('user/area', 'User/setArea')->name('setArea');

// 设置服务项目
// Route::post('user/service', 'user/setService')->name('setService');

// 培训经历
Route::post('user/train', 'User/addTrain')->name('addTrain');
Route::put('user/train/:id', 'User/editTrain')->name('editTrain');
Route::delete('user/train/:id', 'User/deleteTrain')->name('deleteTrain');

// 意见反馈
Route::post('feedback', 'User/addFeedback')->name('addFeedback');

// 申请提现佣金
Route::post('user/cash', 'Account/commission')->name('accountCash');
// 我的金额
Route::get('user/balance', 'Account/getBalance')->name('getBalance');
// 获取归属门店信息
Route::get('user/shop', 'User/getShopInfo')->name('getShopInfo');
// 获取接单日历
Route::get('user/calendar', 'User/getCalendar')->name('getCalendar');

// 开始服务
Route::put('order/start', 'Order/start')->name('startOrder');
// 结束服务
Route::put('order/end', 'Order/end')->name('endOrder');
// 核销按钮
Route::put('order/verify', 'Order/verify')->name('verifyOrder');
// 确认服务
Route::put('order/confirm', 'Order/confirm')->name('confirmOrder');
// 完成服务
Route::put('order/finish', 'Order/finish')->name('finishOrder');
// 拒绝服务
Route::put('order/refuse', 'Order/refuse')->name('refuseOrder');

Route::get('order/detail', 'Order/getOrderDetail')->name('orderDetail');
// 获取订单列表
Route::get('orders', 'Order/getOrderList')->name('getOrderList');
// 获取面试列表
Route::get('interviews', 'Order/getInterviewList')->name('getInterviewList');

// 从业人员修改面试时间
Route::put('order/change_interview', 'Order/changeInterviewTime')->name('changeInterviewTime');

//面试房间踢出成员
Route::get('interview/kickUserOut', 'Interview/kickUserOut');

//从业人员评价列表
Route::get('staffComment', 'StaffComment/index');
