<?php

namespace app\shop\controller;

use app\common\controller\Base;
use think\response\Json;
use app\shop\validate\EditStaffInfo as EditStaffInfoValidate;
use app\shop\validate\EditStaffAuthn as EditStaffAuthnValidate;
use app\shop\validate\EditStaffRest as EditStaffRestValidate;
use app\shop\validate\EditStaffArea as EditStaffAreaValidate;
use app\shop\validate\EditStaffTrain as EditStaffTrainValidate;
use app\shop\validate\StaffCalendar as StaffCalendarValidate;
use app\shop\validate\Feedback as FeedbackValidate;
use app\common\service\StaffService;
use app\common\enum\Response;
use app\common\service\FeedbackService;

class User extends Base
{
    public function getInfo(): Json
    {
        $service = new StaffService();
        return json($service->getStaff(intval($this->param['staff_id'])));
    }

    public function getResume(): Json
    {
        $id = intval($this->param['staff_id']);
        if ($id <= 0) {
            return json(fail(Response::REQUEST_PARAM_ERROR, "服务人员ID错误"));
        }
        $service = new StaffService();
        $info = $service->getStaff(intval($id));
        if ($info['data']['audit_status'] == 0) {
            return json(fail(Response::REQUEST_PARAM_ERROR, "该服务人员正在审核中"));
        }
        return json($info);
    }

    public function editInfo(): Json
    {
        $validate = new EditStaffInfoValidate();
        $result = $validate->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }

        // 修改自身
        $this->param['id'] = $this->param['staff_id'];
        $service = new StaffService();
        return json($service->edit($this->param));
    }

    // 身份证提交
    public function addIdCard():Json{
        $validate = new \app\shop\validate\SubmitIdCard();
        $result = $validate->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }
        $msg = "";
        $service = new StaffService();
        $res = $service->addIdCard($this->param);
        if(!$res) {
            return json(fail(Response::INVALID_STAFF, $msg));
        }
        return json($service->getStaff($this->param['staff_id']));
    }

    // 健康证提交
    public function addHealth():Json{
        $this->param['name'] = "健康证";
        $validate = new \app\shop\validate\SubmitHealthCertificate();
        $result = $validate->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }
        $msg = "";
        $service = new StaffService();
        $res = $service->addHealth($this->param, $msg);
         if(!$res) {
            return json(fail(Response::INVALID_STAFF, $msg));
        }
        return json($service->getStaff($this->param['staff_id']));
    }

    // 技能证新增
    public function addSkill():Json{
        $validate = new \app\shop\validate\SubmitSkillCertificate();
        $result = $validate->scene('add')->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }

        $service = new StaffService();
        $msg = '';
        $res = $service->addSkill($this->param, $msg);
        if(!$res) {
            return json(fail(Response::INVALID_STAFF, $msg));
        }
        return json($service->getStaff($this->param['staff_id']));
    }

    // 技能证编辑
    public function editSkill():Json{
        $validate = new \app\shop\validate\SubmitSkillCertificate();
        $result = $validate->scene('edit')->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }

        $service = new StaffService();
        $msg = '';
        $res = $service->editSkill($this->param, $msg);
        if(!$res) {
            return json(fail(Response::INVALID_STAFF, $msg));
        }
        return json($service->getStaff($this->param['staff_id']));
    }

    // 技能证删除
    public function deleteSkill():Json{
        if (empty($this->param['staff_id']) || empty($this->param['id'])) {
            return json(fail(Response::REQUEST_PARAM_ERROR, "参数错误"));
        }

        $service = new StaffService();
        $msg = '';
        $res = $service->deleteSkill($this->param, $msg);
        if(!$res) {
            return json(fail(Response::INVALID_STAFF, $msg));
        }
        return json($service->getStaff($this->param['staff_id']));
    }

    // 疫苗接种单新增
    public function addVaccination():Json{
        $validate = new \app\shop\validate\SubmitVaccinationCertificate();
        $result = $validate->scene('add')->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }

        $service = new StaffService();
        $msg = '';
        $res = $service->addVaccination($this->param, $msg);
        if(!$res) {
            return json(fail(Response::INVALID_STAFF, $msg));
        }
        return json($service->getStaff($this->param['staff_id']));
    }

    // 疫苗接种单编辑
    public function editVaccination():Json{
        $validate = new \app\shop\validate\SubmitVaccinationCertificate();
        $result = $validate->scene('edit')->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }

        $service = new StaffService();
        $msg = '';
        $res = $service->editVaccination($this->param, $msg);
        if(!$res) {
            return json(fail(Response::INVALID_STAFF, $msg));
        }
        return json($service->getStaff($this->param['staff_id']));
    }

    // 疫苗接种单删除
    public function deleteVaccination():Json{
        if (empty($this->param['staff_id']) || empty($this->param['id'])) {
            return json(fail(Response::REQUEST_PARAM_ERROR, "参数错误"));
        }

        $service = new StaffService();
        $msg = '';
        $res = $service->deleteVaccination($this->param, $msg);
        if(!$res) {
            return json(fail(Response::INVALID_STAFF, $msg));
        }
        return json($service->getStaff($this->param['staff_id']));
    }

    // 无犯罪记录新增
    public function addNoCrime():Json{
        $validate = new \app\shop\validate\SubmitNoCrimeCertificate();
        $result = $validate->scene('add')->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }

        $service = new StaffService();
        $msg = '';
        $res = $service->addNoCrime($this->param, $msg);
        if(!$res) {
            return json(fail(Response::INVALID_STAFF, $msg));
        }
        return json($service->getStaff($this->param['staff_id']));
    }

    // 无犯罪记录编辑
    public function editNoCrime():Json{
        $validate = new \app\shop\validate\SubmitNoCrimeCertificate();
        $result = $validate->scene('edit')->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }

        $service = new StaffService();
        $msg = '';
        $res = $service->editNoCrime($this->param, $msg);
        if(!$res) {
            return json(fail(Response::INVALID_STAFF, $msg));
        }
        return json($service->getStaff($this->param['staff_id']));
    }

    // 无犯罪记录删除
    public function deleteNoCrime():Json{
        if (empty($this->param['staff_id']) || empty($this->param['id'])) {
            return json(fail(Response::REQUEST_PARAM_ERROR, "参数错误"));
        }

        $service = new StaffService();
        $msg = '';
        $res = $service->deleteNoCrime($this->param, $msg);
        if(!$res) {
            return json(fail(Response::INVALID_STAFF, $msg));
        }
        return json($service->getStaff($this->param['staff_id']));
    }

    // 体检报告新增
    public function addPhysical():Json{
        $validate = new \app\shop\validate\SubmitPhysicalReport();
        $result = $validate->scene('add')->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }

        $service = new StaffService();
        $msg = '';
        $res = $service->addPhysical($this->param, $msg);
        if(!$res) {
            return json(fail(Response::INVALID_STAFF, $msg));
        }
        return json($service->getStaff($this->param['staff_id']));
    }

    // 体检报告编辑
    public function editPhysical():Json{
        $validate = new \app\shop\validate\SubmitPhysicalReport();
        $result = $validate->scene('edit')->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }

        $service = new StaffService();
        $msg = '';
        $res = $service->editPhysical($this->param, $msg);
        if(!$res) {
            return json(fail(Response::INVALID_STAFF, $msg));
        }
        return json($service->getStaff($this->param['staff_id']));
    }

    // 体检报告删除
    public function deletePhysical():Json{
        if (empty($this->param['staff_id']) || empty($this->param['id'])) {
            return json(fail(Response::REQUEST_PARAM_ERROR, "参数错误"));
        }

        $service = new StaffService();
        $msg = '';
        $res = $service->deletePhysical($this->param, $msg);
        if(!$res) {
            return json(fail(Response::INVALID_STAFF, $msg));
        }
        return json($service->getStaff($this->param['staff_id']));
    }

    public function getRestTime(): Json
    {
        $service = new StaffService();
        $res = $service->getRestTime(intval($this->param['staff_id']));
        if ($res !== false) {
            return json(success($res));
        }
        return json(fail());
    }

    // 休息时间
    public function setRestTime(): Json
    {
        $validate = new EditStaffRestValidate();
        $result = $validate->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }
        $service = new StaffService();
        return json($service->setRestTime($this->param));
    }

    public function deleteRestTime(): Json
    {
        $service = new StaffService();
        return json($service->delRestTime($this->param));
    }

    public function getArea(): Json
    {
        $service = new StaffService();
        $res = $service->getArea(intval($this->param['staff_id']));
        if ($res !== false) {
            return json(success($res));
        }
        return json(fail());
    }

    // 接单区域
    public function setArea(): Json
    {
        $validate = new EditStaffAreaValidate();
        $result = $validate->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }
        $service = new StaffService();
        return json($service->setArea($this->param));
    }

    public function addTrain(): Json
    {
        $validate = new EditStaffTrainValidate();
        $result = $validate->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }
        $service = new StaffService();
        return json($service->addTrain($this->param));
    }

    public function editTrain(): Json
    {
        $validate = new EditStaffTrainValidate();
        $result = $validate->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }
        $service = new StaffService();
        return json($service->editTrain($this->param));
    }

    public function deleteTrain(): Json
    {
        $service = new StaffService();
        return json($service->delTrain($this->param));
    }

    public function addFeedback(): Json
    {
        $validate = new FeedbackValidate();
        $result = $validate->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }
        $service = new FeedbackService();
        return json($service->add($this->param));
    }

    public function getShopInfo(): Json
    {
        $service = new StaffService();
        $res = $service->getStaffShopById(intval($this->param['staff_id']));
        if ($res !== false) {
            return json(success($res));
        }
        return json(fail(Response::REQUEST_PARAM_ERROR, "找不到对应信息"));
    }

    // public function setService(): Json
    // {
    //     $service = new StaffService();
    //     return json($service->setService($this->param));
    // }

    public function getCalendar(): Json
    {
        $validate = new StaffCalendarValidate();
        $result = $validate->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }

        $service = new StaffService();
        $list = $service->getWorkList($this->param);
        return json(success(array_values($list)));
    }
}
