<?php

declare(strict_types=1);

namespace app\shop\validate;

use app\common\enum\staff\Authn;
use think\Validate;

class SubmitIdCard extends Validate
{
    /**
     * 定义验证规则
     * 格式：'字段名' =>  ['规则1','规则2'...]
     *
     * @var array
     */
    protected $rule = [
        'staff_id' => 'require|number',
        'id_card_front_image' => 'require|checkImageId',
        'id_card_back_image'  => 'require|checkImageId',
        'id_card_number'      => 'require|checkIdCardNumber',
        'real_name'           => 'require|max:50',
    ];

    /**
     * 定义错误信息
     * 格式：'字段名.规则名' =>  '错误信息'
     *
     * @var array
     */
    protected $message = [
        'staff_id.require' => '请选择员工',
        'staff_id.number' => '请选择员工',
        'id_card_front_image.require' => '请上传身份证正面照片',
        'id_card_back_image.require'  => '请上传身份证反面照片',
        'id_card_number.require'      => '请输入身份证号码',
        'real_name.require'           => '请输入真实姓名',
        'real_name.max'               => '真实姓名不能超过50个字符',
    ];

    /**
     * 验证图片ID是否有效
     *
     * @param mixed $value
     * @param string $rule
     * @param array $data
     * @return bool|string
     */
    protected function checkImageId($value, $rule, $data = [])
    {
        // 检查图片ID是否为正整数
        if (!is_numeric($value) || intval($value) <= 0) {
            return '请上传有效的图片';
        }
        
        return true;
    }

    /**
     * 验证身份证号码格式
     *
     * @param mixed $value
     * @param string $rule
     * @param array $data
     * @return bool|string
     */
    protected function checkIdCardNumber($value, $rule, $data = [])
    {
        // 身份证号码长度检查
        if (strlen($value) !== 18) {
            return '身份证号码必须为18位';
        }

        // 身份证号码格式检查（前17位为数字，最后一位为数字或X）
        if (!preg_match('/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/', $value)) {
            return '身份证号码格式不正确';
        }

        // 身份证校验码验证
        if (!$this->validateIdCardChecksum($value)) {
            return '身份证号码校验失败';
        }

        return true;
    }

    /**
     * 验证身份证校验码
     *
     * @param string $idCard
     * @return bool
     */
    private function validateIdCardChecksum($idCard)
    {
        $weights = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
        $checkCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
        
        $sum = 0;
        for ($i = 0; $i < 17; $i++) {
            $sum += intval($idCard[$i]) * $weights[$i];
        }
        
        $checkCode = $checkCodes[$sum % 11];
        $lastChar = strtoupper($idCard[17]);
        
        return $checkCode === $lastChar;
    }

    /**
     * 验证场景定义
     *
     * @var array
     */
    protected $scene = [
        'submit' => ['staff_id', 'id_card_front_image', 'id_card_back_image', 'id_card_number', 'real_name'],
        'update' => ['staff_id', 'id_card_front_image', 'id_card_back_image', 'id_card_number', 'real_name'],
    ];
}
