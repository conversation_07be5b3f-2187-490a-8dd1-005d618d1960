<?php

declare(strict_types=1);

namespace app\shop\validate;

use app\common\enum\staff\Authn;
use think\Validate;

class EditStaffAuthn extends Validate
{
    /**
     * 定义验证规则
     * 格式：'字段名' =>  ['规则1','规则2'...]
     *
     * @var array
     */
    protected $rule = [
        'authn_type' => 'require|checkAuthnType',
        'image_id'  => 'require|checkAuthnType',
        'authn_no' => '',
    ];

    /**
     * 定义错误信息
     * 格式：'字段名.规则名' =>  '错误信息'
     *
     * @var array
     */
    protected $message = [
        'image_id.require' => '请上传认证证明',
        'authn_type.require' => '请选择认证类型',
        // 'authn_no.require' => '请输入认证号',
    ];

    protected function checkAuthnType($value, $rule, $data = [])
    {
        if (!isset($data['id'])) {
            $authnType = $data['authn_type'] ?? 0;
            if (!isset(Authn::AuthnCode[$authnType])) {
                return '请选择认证类型';
            }
        }
        // if ($value != Authn::ID_CARD && isset($data['name'])) {
        //     return '请填写认证名称';
        // }
        return true;
    }
}
