<?php

declare(strict_types=1);

namespace app\shop\validate;

use app\common\enum\staff\Authn;
use think\Validate;

class SubmitPhysicalReport extends Validate
{
    /**
     * 体检报告类型常量
     */
    const REPORT_TYPE_URL = 1;      // 链接
    const REPORT_TYPE_PDF = 2;      // PDF文件
    const REPORT_TYPE_IMAGES = 3;   // 多张图片

    /**
     * 定义验证规则
     * 格式：'字段名' =>  ['规则1','规则2'...]
     *
     * @var array
     */
    protected $rule = [
        'staff_id'           => 'require|number',
        'report_no'          => 'require|max:100',
        'physical_date'      => 'require|checkDate',
        'report_type'        => 'require|in:1,2,3',
        'report_url'         => 'requireIf:report_type,1|url',
        'pdf_file_id'        => 'requireIf:report_type,2|checkFileId',
        'report_images'      => 'requireIf:report_type,3|checkImageIds',
        'name'               => 'max:100',
    ];

    /**
     * 定义错误信息
     * 格式：'字段名.规则名' =>  '错误信息'
     *
     * @var array
     */
    protected $message = [
        'staff_id.require'           => '请选择员工',
        'staff_id.number'            => '请选择员工',
        'report_no.require'          => '请输入体检报告编号',
        'report_no.max'              => '体检报告编号不能超过100个字符',
        'physical_date.require'      => '请选择体检时间',
        'report_type.require'        => '请选择体检报告上传方式',
        'report_type.in'             => '体检报告上传方式无效',
        'report_url.requireIf'       => '请输入体检报告链接地址',
        'report_url.url'             => '请输入有效的链接地址',
        'pdf_file_id.requireIf'      => '请上传PDF文件',
        'report_images.requireIf'    => '请上传体检报告图片',
        'name.max'                   => '体检报告名称不能超过100个字符',
    ];

    /**
     * 验证文件ID是否有效
     *
     * @param mixed $value
     * @param string $rule
     * @param array $data
     * @return bool|string
     */
    protected function checkFileId($value, $rule, $data = [])
    {
        // 检查文件ID是否为正整数
        if (!is_numeric($value) || intval($value) <= 0) {
            return '请上传有效的PDF文件';
        }
        
        return true;
    }

    /**
     * 验证图片ID数组是否有效
     *
     * @param mixed $value
     * @param string $rule
     * @param array $data
     * @return bool|string
     */
    protected function checkImageIds($value, $rule, $data = [])
    {
        // 如果是字符串，转换为数组
        if (is_string($value)) {
            $imageIds = array_filter(explode(',', $value));
        } elseif (is_array($value)) {
            $imageIds = array_filter($value);
        } else {
            return '请上传有效的图片';
        }

        // 检查是否至少有一张图片
        if (empty($imageIds)) {
            return '请至少上传一张体检报告图片';
        }

        // 检查每个图片ID是否为正整数
        foreach ($imageIds as $imageId) {
            if (!is_numeric($imageId) || intval($imageId) <= 0) {
                return '请上传有效的图片';
            }
        }
        
        return true;
    }

    /**
     * 验证日期格式
     *
     * @param mixed $value
     * @param string $rule
     * @param array $data
     * @return bool|string
     */
    protected function checkDate($value, $rule, $data = [])
    {
        // 检查日期格式 YYYY-MM-DD
        if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $value)) {
            return '日期格式不正确，请使用 YYYY-MM-DD 格式';
        }

        // 验证日期是否有效
        $dateArray = explode('-', $value);
        if (!checkdate(intval($dateArray[1]), intval($dateArray[2]), intval($dateArray[0]))) {
            return '请输入有效的日期';
        }

        // 检查日期不能是未来日期
        if (strtotime($value) > time()) {
            return '体检时间不能是未来日期';
        }

        return true;
    }

    /**
     * 验证场景定义
     *
     * @var array
     */
    protected $scene = [
        'add' => ['staff_id', 'report_no', 'physical_date', 'report_type', 'report_url', 'pdf_file_id', 'report_images', 'name'],
        'edit' => ['staff_id', 'report_no', 'physical_date', 'report_type', 'report_url', 'pdf_file_id', 'report_images', 'name'],
        'delete' => ['staff_id', 'id'],
    ];
}
