<?php

declare(strict_types=1);

namespace app\shop\validate;

use app\common\enum\staff\Authn;
use think\Validate;

class SubmitNoCrimeCertificate extends Validate
{
    /**
     * 定义验证规则
     * 格式：'字段名' =>  ['规则1','规则2'...]
     *
     * @var array
     */
    protected $rule = [
        'staff_id'                => 'require|number',
        'no_crime_certificate_image' => 'require|checkImageId',
        'no_crime_certificate_no' => 'require|max:100',
        'issue_date'              => 'require|checkDate',
        'expiry_date'             => 'checkDate',
        'name'                    => 'max:100',
    ];

    /**
     * 定义错误信息
     * 格式：'字段名.规则名' =>  '错误信息'
     *
     * @var array
     */
    protected $message = [
        'staff_id.require'                => '请选择员工',
        'staff_id.number'                 => '请选择员工',
        'no_crime_certificate_image.require' => '请上传无犯罪记录证明图片',
        'no_crime_certificate_no.require' => '请输入无犯罪记录编号',
        'no_crime_certificate_no.max'     => '无犯罪记录编号不能超过100个字符',
        'issue_date.require'              => '请选择开具日期',
        'name.max'                        => '无犯罪记录名称不能超过100个字符',
    ];

    /**
     * 验证图片ID是否有效
     *
     * @param mixed $value
     * @param string $rule
     * @param array $data
     * @return bool|string
     */
    protected function checkImageId($value, $rule, $data = [])
    {
        // 检查图片ID是否为正整数
        if (!is_numeric($value) || intval($value) <= 0) {
            return '请上传有效的图片';
        }
        
        return true;
    }

    /**
     * 验证日期格式
     *
     * @param mixed $value
     * @param string $rule
     * @param array $data
     * @return bool|string
     */
    protected function checkDate($value, $rule, $data = [])
    {
        // 如果为空且不是必填字段，则跳过验证
        if (empty($value) && $rule !== 'require|checkDate') {
            return true;
        }

        // 检查日期格式 YYYY-MM-DD
        if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $value)) {
            return '日期格式不正确，请使用 YYYY-MM-DD 格式';
        }

        // 验证日期是否有效
        $dateArray = explode('-', $value);
        if (!checkdate(intval($dateArray[1]), intval($dateArray[2]), intval($dateArray[0]))) {
            return '请输入有效的日期';
        }

        // 检查开具日期不能是未来日期
        if (strtotime($value) > time()) {
            return '开具日期不能是未来日期';
        }

        return true;
    }

    /**
     * 验证场景定义
     *
     * @var array
     */
    protected $scene = [
        'add' => ['staff_id', 'no_crime_certificate_image', 'no_crime_certificate_no', 'issue_date', 'expiry_date', 'name'],
        'edit' => ['staff_id', 'no_crime_certificate_image', 'no_crime_certificate_no', 'issue_date', 'expiry_date', 'name'],
        'delete' => ['staff_id', 'id'],
    ];
}
