<?php

declare(strict_types=1);

namespace app\shop\validate;

use app\common\enum\staff\Authn;
use think\Validate;

class SubmitHealthCertificate extends Validate
{
    /**
     * 定义验证规则
     * 格式：'字段名' =>  ['规则1','规则2'...]
     *
     * @var array
     */
    protected $rule = [
        'staff_id'                => 'require|number',
        'health_certificate_image' => 'require|checkImageId',
        'health_certificate_no'   => 'require|max:100',
        'issue_date'              => 'require|checkDate',
        'name'                    => 'max:100',
    ];

    /**
     * 定义错误信息
     * 格式：'字段名.规则名' =>  '错误信息'
     *
     * @var array
     */
    protected $message = [
        'staff_id.require'                => '请选择员工',
        'staff_id.number'                 => '请选择员工',
        'health_certificate_image.require' => '请上传健康证图片',
        'health_certificate_no.require'   => '请输入健康证号码',
        'health_certificate_no.max'       => '健康证号码不能超过100个字符',
        'issue_date.require'              => '请选择发证日期',
        'name.max'                        => '健康证名称不能超过100个字符',
    ];

    /**
     * 验证图片ID是否有效
     *
     * @param mixed $value
     * @param string $rule
     * @param array $data
     * @return bool|string
     */
    protected function checkImageId($value, $rule, $data = [])
    {
        // 检查图片ID是否为正整数
        if (!is_numeric($value) || intval($value) <= 0) {
            return '请上传有效的图片';
        }
        
        return true;
    }

    /**
     * 验证日期格式
     *
     * @param mixed $value
     * @param string $rule
     * @param array $data
     * @return bool|string
     */
    protected function checkDate($value, $rule, $data = [])
    {
        // 检查日期格式 YYYY-MM-DD
        if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $value)) {
            return '日期格式不正确，请使用 YYYY-MM-DD 格式';
        }

        // 验证日期是否有效
        $dateArray = explode('-', $value);
        if (!checkdate(intval($dateArray[1]), intval($dateArray[2]), intval($dateArray[0]))) {
            return '请输入有效的日期';
        }

        // 检查日期不能是未来日期
        if (strtotime($value) > time()) {
            return '发证日期不能是未来日期';
        }

        return true;
    }

    /**
     * 验证场景定义
     *
     * @var array
     */
    protected $scene = [
        'submit' => ['staff_id', 'health_certificate_image', 'health_certificate_no', 'issue_date', 'name'],
        'update' => ['staff_id', 'health_certificate_image', 'health_certificate_no', 'issue_date', 'name'],
    ];
}
