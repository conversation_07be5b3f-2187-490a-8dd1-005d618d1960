<?php

declare(strict_types=1);

namespace app\shop\validate;

use app\common\enum\staff\Authn;
use think\Validate;

class SubmitSkillCertificate extends Validate
{
    /**
     * 定义验证规则
     * 格式：'字段名' =>  ['规则1','规则2'...]
     *
     * @var array
     */
    protected $rule = [
        'staff_id'                => 'require|number',
        'skill_certificate_images' => 'require|checkImageIds',
        'skill_certificate_name'  => 'require|max:100',
        'skill_certificate_no'    => 'max:100',
        'issue_date'              => 'checkDate',
        'expiry_date'             => 'checkDate',
    ];

    /**
     * 定义错误信息
     * 格式：'字段名.规则名' =>  '错误信息'
     *
     * @var array
     */
    protected $message = [
        'staff_id.require'                => '请选择员工',
        'staff_id.number'                 => '请选择员工',
        'skill_certificate_images.require' => '请上传技能证图片',
        'skill_certificate_name.require'  => '请输入技能证名称',
        'skill_certificate_name.max'      => '技能证名称不能超过100个字符',
        'skill_certificate_no.max'        => '技能证号码不能超过100个字符',
    ];

    /**
     * 验证图片ID数组是否有效
     *
     * @param mixed $value
     * @param string $rule
     * @param array $data
     * @return bool|string
     */
    protected function checkImageIds($value, $rule, $data = [])
    {
        // 如果是字符串，转换为数组
        if (is_string($value)) {
            $imageIds = array_filter(explode(',', $value));
        } elseif (is_array($value)) {
            $imageIds = array_filter($value);
        } else {
            return '请上传有效的图片';
        }

        // 检查是否至少有一张图片
        if (empty($imageIds)) {
            return '请至少上传一张技能证图片';
        }

        // 检查每个图片ID是否为正整数
        foreach ($imageIds as $imageId) {
            if (!is_numeric($imageId) || intval($imageId) <= 0) {
                return '请上传有效的图片';
            }
        }
        
        return true;
    }

    /**
     * 验证日期格式
     *
     * @param mixed $value
     * @param string $rule
     * @param array $data
     * @return bool|string
     */
    protected function checkDate($value, $rule, $data = [])
    {
        // 如果为空，则跳过验证（因为日期不是必填的）
        if (empty($value)) {
            return true;
        }

        // 检查日期格式 YYYY-MM-DD
        if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $value)) {
            return '日期格式不正确，请使用 YYYY-MM-DD 格式';
        }

        // 验证日期是否有效
        $dateArray = explode('-', $value);
        if (!checkdate(intval($dateArray[1]), intval($dateArray[2]), intval($dateArray[0]))) {
            return '请输入有效的日期';
        }

        return true;
    }

    /**
     * 验证场景定义
     *
     * @var array
     */
    protected $scene = [
        'add' => ['staff_id', 'skill_certificate_images', 'skill_certificate_name', 'skill_certificate_no', 'issue_date', 'expiry_date'],
        'edit' => ['staff_id', 'skill_certificate_images', 'skill_certificate_name', 'skill_certificate_no', 'issue_date', 'expiry_date'],
        'delete' => ['staff_id', 'id'],
    ];
}
