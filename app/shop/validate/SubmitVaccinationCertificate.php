<?php

declare(strict_types=1);

namespace app\shop\validate;

use app\common\enum\staff\Authn;
use think\Validate;

class SubmitVaccinationCertificate extends Validate
{
    /**
     * 定义验证规则
     * 格式：'字段名' =>  ['规则1','规则2'...]
     *
     * @var array
     */
    protected $rule = [
        'staff_id'                    => 'require|number',
        'vaccination_certificate_image' => 'require|checkImageId',
        'vaccination_certificate_name' => 'require|max:100',
        'vaccination_certificate_no'   => 'require|max:100',
        'vaccination_date'            => 'checkDate',
        'expiry_date'                 => 'checkDate',
    ];

    /**
     * 定义错误信息
     * 格式：'字段名.规则名' =>  '错误信息'
     *
     * @var array
     */
    protected $message = [
        'staff_id.require'                    => '请选择员工',
        'staff_id.number'                     => '请选择员工',
        'vaccination_certificate_image.require' => '请上传疫苗接种单图片',
        'vaccination_certificate_name.require'  => '请输入疫苗接种单名称',
        'vaccination_certificate_name.max'      => '疫苗接种单名称不能超过100个字符',
        'vaccination_certificate_no.require'    => '请输入疫苗接种单编号',
        'vaccination_certificate_no.max'        => '疫苗接种单编号不能超过100个字符',
    ];

    /**
     * 验证图片ID是否有效
     *
     * @param mixed $value
     * @param string $rule
     * @param array $data
     * @return bool|string
     */
    protected function checkImageId($value, $rule, $data = [])
    {
        // 检查图片ID是否为正整数
        if (!is_numeric($value) || intval($value) <= 0) {
            return '请上传有效的图片';
        }
        
        return true;
    }

    /**
     * 验证日期格式
     *
     * @param mixed $value
     * @param string $rule
     * @param array $data
     * @return bool|string
     */
    protected function checkDate($value, $rule, $data = [])
    {
        // 如果为空，则跳过验证（因为日期不是必填的）
        if (empty($value)) {
            return true;
        }

        // 检查日期格式 YYYY-MM-DD
        if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $value)) {
            return '日期格式不正确，请使用 YYYY-MM-DD 格式';
        }

        // 验证日期是否有效
        $dateArray = explode('-', $value);
        if (!checkdate(intval($dateArray[1]), intval($dateArray[2]), intval($dateArray[0]))) {
            return '请输入有效的日期';
        }

        return true;
    }

    /**
     * 验证场景定义
     *
     * @var array
     */
    protected $scene = [
        'add' => ['staff_id', 'vaccination_certificate_image', 'vaccination_certificate_name', 'vaccination_certificate_no', 'vaccination_date', 'expiry_date'],
        'edit' => ['staff_id', 'vaccination_certificate_image', 'vaccination_certificate_name', 'vaccination_certificate_no', 'vaccination_date', 'expiry_date'],
        'delete' => ['staff_id', 'id'],
    ];
}
