<?php

declare(strict_types=1);

namespace app\shop\validate;

use app\common\model\StaffModel;
use think\facade\Cache;
use think\facade\Db;
use think\Validate;

class ResetPassword extends Validate
{
    /**
     * 定义验证规则
     * 格式：'字段名' =>  ['规则1','规则2'...]
     *
     * @var array
     */
    protected $rule = [
        'mobile'    => 'require|length:11',
        'code'      => 'require|length:4|checkCode',
        'password'    => 'require|min:6',
        're_password' => 'require|confirm:password'
    ];

    /**
     * 定义错误信息
     * 格式：'字段名.规则名' =>  '错误信息'
     *
     * @var array
     */
    protected $message = [
        'mobile.require'    => '请输入手机号',
        'mobile.length'     => '手机号长度为11位',
        'code.require'      => '请输入验证码',
        'code.length'       => '验证码长度为4位',
        'password.require'    => '请输入密码',
        'password.min'        => '密码长度不能小于6位',
        're_password.require' => '请输入确认密码',
        're_password.confirm' => '两次密码输入不一致'
    ];
    private $mobile;

    protected function checkCode($value, $rule, $data = [])
    {
        $key    = 'sms_captcha_' . $data['mobile'] . '_forgetpassword';
        $code = Cache::store('redis')->handler()->get($key);
        if ((string)$code != (string)$value) {
            return '验证码不正确';
        }
        Cache::delete($key);
        return true;
    }
}
