<?php
declare(strict_types=1);

namespace app\common\service;

use app\common\enum\Response;

/**
 * 优惠券
 * <AUTHOR>
 * @description CouponService
 * @date 2025/4/14
 */
class CouponService extends BaseService
{
    const BEARER                  = 'Authorization: Bearer ';
    const SERVICE_NAME_YSA_MEMBER = 'ysa-member';


    public static function createCouponTemplate(array $params, string $token, string $flag = self::FLAG_ADMIN)
    {
        $interfaceKey  = 'postCouponTemplate';
        $interfaceName = self::getInterface($interfaceKey, self::SERVICE_NAME_YSA_MEMBER, $flag);
        //请求接口的url组装（domain+interfaceName）
        $url = self::getDomain(self::SERVICE_NAME_YSA_MEMBER) . $interfaceName;
        return curlPostApiContentByUrlAndParams($url, $params, [self::BEARER . $token], false);
    }

    //优惠券模板列表
    public static function couponTemplateList(array $params, string $token, string $flag = self::FLAG_ADMIN)
    {
        $interfaceKey  = 'getCouponTemplate';
        $interfaceName = self::getInterface($interfaceKey, self::SERVICE_NAME_YSA_MEMBER, $flag);
        //请求接口的url组装（domain+interfaceName）
        $url = self::getDomain(self::SERVICE_NAME_YSA_MEMBER) . $interfaceName;
        return curlGetApiContentByUrl($url, $params, [self::BEARER . $token], false);
    }

    //优惠券模板更新
    public static function updateCouponTemplate(array $params, string $token, string $flag = self::FLAG_ADMIN)
    {
        $interfaceKey  = 'putCouponTemplate';
        $interfaceName = self::getInterface($interfaceKey, self::SERVICE_NAME_YSA_MEMBER, $flag);
        //请求接口的url组装（domain+interfaceName）
        $url = self::getDomain(self::SERVICE_NAME_YSA_MEMBER) . $interfaceName . '/' . $params['id'];
        return curlPostApiContentByUrlAndParams($url, $params, [self::BEARER . $token], true);
    }

    //优惠券模板状态更新
    public static function updateCouponTemplateStatus(array $params, string $token, string $flag = self::FLAG_ADMIN)
    {
        $interfaceKey  = 'putCouponTemplateStatus';
        $interfaceName = self::getInterface($interfaceKey, self::SERVICE_NAME_YSA_MEMBER, $flag);
        //请求接口的url组装（domain+interfaceName）
        $url = self::getDomain(self::SERVICE_NAME_YSA_MEMBER) . $interfaceName . '/' . $params['id'];
        return curlPostApiContentByUrlAndParams($url, $params, [self::BEARER . $token], true);
    }

    //优惠券模板详情
    public static function getCouponTemplateById(array $params, string $token, string $flag = self::FLAG_ADMIN)
    {
        $interfaceKey  = 'getCouponTemplateById';
        $interfaceName = self::getInterface($interfaceKey, self::SERVICE_NAME_YSA_MEMBER, $flag);
        //请求接口的url组装（domain+interfaceName）
        $url = self::getDomain(self::SERVICE_NAME_YSA_MEMBER) . $interfaceName . '/' . $params['id'];
        return curlGetApiContentByUrl($url, $params, [self::BEARER . $token]);
    }

    //优惠券模板删除
    public static function deleteCouponTemplate(array $params, string $token, string $flag = self::FLAG_ADMIN)
    {
        $interfaceKey  = 'deleteCouponTemplate';
        $interfaceName = self::getInterface($interfaceKey, self::SERVICE_NAME_YSA_MEMBER, $flag);
        //请求接口的url组装（domain+interfaceName）
        $url = self::getDomain(self::SERVICE_NAME_YSA_MEMBER) . $interfaceName . '/' . $params['id'];
        return curlGetApiContentByUrl($url, $params, [self::BEARER . $token], true);
    }

    //优惠券池添加
    public static function createCouponPool(array $params, string $token, string $flag = self::FLAG_ADMIN)
    {
        $interfaceKey  = 'postCouponPool';
        $interfaceName = self::getInterface($interfaceKey, self::SERVICE_NAME_YSA_MEMBER, $flag);
        //请求接口的url组装（domain+interfaceName）
        $url = self::getDomain(self::SERVICE_NAME_YSA_MEMBER) . $interfaceName;
        return curlPostApiContentByUrlAndParams($url, $params, [self::BEARER . $token], false);
    }


    //优惠券添加 （手动发放优惠券）
    public static function createCoupon(array $params, string $token, string $flag = self::FLAG_ADMIN)
    {
        $interfaceKey  = 'postCoupon';
        $interfaceName = self::getInterface($interfaceKey, self::SERVICE_NAME_YSA_MEMBER, $flag);
        //请求接口的url组装（domain+interfaceName）
        $url = self::getDomain(self::SERVICE_NAME_YSA_MEMBER) . $interfaceName;
        return curlPostApiContentByUrlAndParams($url, $params, [self::BEARER . $token], false);
    }

    //优惠券列表
    public static function couponList(array $params, string $token, string $flag = self::FLAG_ADMIN)
    {
        $interfaceKey  = 'getCouponList';
        $interfaceName = self::getInterface($interfaceKey, self::SERVICE_NAME_YSA_MEMBER, $flag);
        //请求接口的url组装（domain+interfaceName）
        $url = self::getDomain(self::SERVICE_NAME_YSA_MEMBER) . $interfaceName;
        return curlGetApiContentByUrl($url, $params, [self::BEARER . $token], false);
    }

    //优惠券列表
    public static function couponOrderList(array $params, string $token, string $flag = self::FLAG_API)
    {
        $interfaceKey  = 'getCouponOrderList';
        $interfaceName = self::getInterface($interfaceKey, self::SERVICE_NAME_YSA_MEMBER, $flag);
        //请求接口的url组装（domain+interfaceName）
        $url = self::getDomain(self::SERVICE_NAME_YSA_MEMBER) . $interfaceName;
        return curlGetApiContentByUrl($url, $params, [self::BEARER . $token], false);
    }

    // 优惠券详情
    public static function getCouponById(array $params, string $token, string $flag = self::FLAG_ADMIN)
    {
        $interfaceKey  = 'getCouponById';
        $interfaceName = self::getInterface($interfaceKey, self::SERVICE_NAME_YSA_MEMBER, $flag);
        //请求接口的url组装（domain+interfaceName）
        $url = self::getDomain(self::SERVICE_NAME_YSA_MEMBER) . $interfaceName . '/' . $params['id'];
        return curlGetApiContentByUrl($url, $params, [self::BEARER . $token]);
    }

    //优惠券更新
    public static function updateCoupon(array $params, string $token, string $flag = self::FLAG_ADMIN)
    {
        $interfaceKey  = 'putCoupon';
        $interfaceName = self::getInterface($interfaceKey, self::SERVICE_NAME_YSA_MEMBER, $flag);
        //请求接口的url组装（domain+interfaceName）
        $url = self::getDomain(self::SERVICE_NAME_YSA_MEMBER) . $interfaceName . '/' . $params['id'];
        return curlPostApiContentByUrlAndParams($url, $params, [self::BEARER . $token], true);
    }

    // 获取指定用户可用优惠券的数量
    public static function getCouponCountByMemberId(array $params, string $token, string $flag = self::FLAG_ADMIN)
    {
        $interfaceKey  = 'getCouponCount';
        $interfaceName = self::getInterface($interfaceKey, self::SERVICE_NAME_YSA_MEMBER, $flag);
        //请求接口的url组装（domain+interfaceName）
        $url = self::getDomain(self::SERVICE_NAME_YSA_MEMBER) . $interfaceName . '/' . $params['id'];
        return curlGetApiContentByUrl($url, $params, [self::BEARER . $token]);
    }

}