<?php

declare(strict_types=1);

namespace app\common\service;

use app\common\model\GoodsCategoryBasicModel;
use app\common\model\GoodsCategoryModel;
use app\common\model\ServiceZoneApplyModel;
use app\common\model\ServiceZoneModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class GoodsCategoryService
{

    private $shopId;
    private GoodsCategoryBasicModel $goodsCategoryBasicModel;
    private GoodsCategoryModel $goodsCategoryModel;
    private ServiceZoneModel $serviceZoneModel;
    private ServiceZoneApplyModel $serviceZoneApplyModel;


    /**
     * GoodsCategory constructor.
     */
    public function __construct()
    {
        $this->goodsCategoryBasicModel = new  GoodsCategoryBasicModel();
        $this->goodsCategoryModel      = new  GoodsCategoryModel();
        $this->goodsCategoryBasicModel = new  GoodsCategoryBasicModel();
        $this->serviceZoneModel        = new  ServiceZoneModel();
        $this->serviceZoneApplyModel   = new  ServiceZoneApplyModel();
    }

    /**
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getList(): array
    {
        //判断此项目在店铺所在所服务地区是否被申请过，如被申请过则不允许再进行申请
        return $this->goodsCategoryBasicModel->getAll();
    }

    /**
     * @param $request
     * @return array|string
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getServiceZoneById($request): array
    {
        $shopId            = $request['shop_id']; //店铺ID
        $third_category_id = $request['third_category_id']; //三级分类ID
        $zoneList          = $this->serviceZoneModel->where(['shop_id' => $shopId])->select()->toArray();
        if (!empty($zoneList)) {
            $result = [];
            foreach ($zoneList as $key => $value) {
                $data['name'] = $this->getByRegionId($value['region_id']);
                $data['code'] = $value['province_id'] . "," . $value['city_id'] . "," . $value['district_id'] . "," . $value['region_id'];
                $data['flag'] = $this->handleApplyUnique($shopId, $value['region_id'], $third_category_id); //唯一性
                $result[]     = $data;
            }
            return $result;
        }
        return [];
    }

    //判断同一个店铺&&同一个region_id&&同一个项目名称（third_category_id）是否已经申请（未审核视为已申请；审核不通过，允许再申请）
    private function handleApplyUnique($shopId, $regionId, $thirdCategoryId): bool
    {
        $zoneApply = $this->serviceZoneApplyModel
            ->where(['shop_id' => $shopId, 'region_id' => $regionId, 'third_category_id' => $thirdCategoryId])
            ->whereIn('status', '0,1', 'and')
            ->select();
        if (!$zoneApply->isEmpty()) {
            return true;
        }
        return false;
    }

    /**
     * @param $data
     * @return bool
     * @throws \think\exception\DbException
     */
    public function addApply($data): bool
    {
        //goods_category:service_zone_apply=>1:N
        //goods_category:goods_category_review=>1:1
        //service_zone_apply:service_zone_apply_review=>1:1

        $thirdCategoryId                 = $data['c_id'];
        $this->shopId                    = $data['shop_id'];
        $thirdCategory                   = $this->goodsCategoryBasicModel->find($thirdCategoryId);
        $secondCategoryId                = $thirdCategory->getData('parent_id');
        $secondCategory                  = $this->goodsCategoryBasicModel->find($secondCategoryId);
        $firstCategoryId                 = $secondCategory->getData('parent_id');
        $applyData['shop_id']            = $this->shopId;
        $applyData['third_category_id']  = $thirdCategoryId;
        $applyData['second_category_id'] = $secondCategoryId;
        $applyData['first_category_id']  = $firstCategoryId;
        $serviceZone                     = $data['serviceZone'];
        $model                           = $this->goodsCategoryModel;
        $category                        = $model->where($applyData)->find();
        if (!empty($category)) {
            return $this->handleApplyZoneList($serviceZone, $category->c_id, $thirdCategoryId);
        } else {
            $applyData['third_category_unique_name'] = $data['name']; //三级分类名称个性化命名，初始化与基础表同字段一致
            $applyData['unique_name_status']         = "1"; //审核状态，初始化为审核通过
            $applyData['remark']                     = $data['remark'] ?? '';

            //添加事务
            try {

                $model->startTrans();
                $model->save($applyData);
                $applyId = $model->c_id;
                if ($this->handleApplyZoneList($serviceZone, $applyId, $thirdCategoryId)) {
                    $model->commit();
                    return true;
                }
            } catch (\Exception $e) {
                $model->rollback();
                //  $e->getMessage();
            }
        }
        return false;
    }


    //记录针对服务区地区提交的申请，支持一次申请提交多个服务区域
    private function handleApplyZoneList($serviceZone, $applyId, $thirdCategoryId): bool
    {
        if (is_array($serviceZone)) {
            for ($i = 0; $i < count($serviceZone); $i++) {
                $zoneCode                       = explode(",", $serviceZone[$i]);
                $applyZone['province_id']       = $zoneCode[0];
                $applyZone['city_id']           = $zoneCode[1];
                $applyZone['district_id']       = $zoneCode[2];
                $applyZone['region_id']         = $zoneCode[3];
                $applyZone['shop_id']           = $this->shopId;
                $applyZone['c_id']              = $applyId;
                $applyZone['third_category_id'] = $thirdCategoryId; //商品三级分类ID
                $applyZoneData[]                = $applyZone;
            }
            $this->serviceZoneApplyModel->saveAll($applyZoneData);
            return true;
        }
        return false;
    }

    /**
     * @param $requestData
     * @return array
     * @throws \think\exception\DbException
     */
    public function getApplyList($requestData): array
    {
        $shopId       = $requestData['shop_id'];
        $categoryList = $this->goodsCategoryModel->where(['shop_id' => $shopId, 'is_delete' => 0])->order('c_id desc')->paginate(config('admin.limit'));
        $pageItems    = $categoryList->items();
        $applyList    = [];
        if (!empty($pageItems)) {
            foreach ($pageItems as $key => $value) {
                $firstCategoryId                        = $value->first_category_id;
                $secondCategoryId                       = $value->second_category_id;
                $thirdCategoryId                        = $value->third_category_id;
                $categoryArr                            = $this->getCategory($firstCategoryId, $secondCategoryId, $thirdCategoryId);
                $itemData                               = $categoryArr;
                $itemData['c_id']                       = $value->c_id;
                $itemData['first_category_id']          = $value->first_category_id;
                $itemData['second_category_id']         = $value->second_category_id;
                $itemData['third_category_id']          = $value->third_category_id;
                $itemData['third_category_unique_name'] = $value->third_category_unique_name;
                $itemData['remark']                     = $value->remark;
                $itemData['create_time']                = $value->create_time;
                $itemData['update_time']                = $value->update_time;
                $serviceZoneApply                       = $this->serviceZoneApplyModel->where(['c_id' => $value->c_id, 'shop_id' => $value['shop_id'], 'third_category_id' => $thirdCategoryId])->select();
                if (!$serviceZoneApply->isEmpty()) {
                    foreach ($serviceZoneApply->toArray() as $k => $v) {
                        $data['name']        = $this->getByRegionId(($v['region_id']));
                        $data['code']        = $v['province_id'] . "," . $v['city_id'] . "," . $v['district_id'] . "," . $v['region_id'];
                        $data['status']      = $v['status']; //审核状态
                        $data['update_time'] = $v['update_time'];
                        $result[]            = $data;
                        unset($data);
                    }
                    $itemData['serviceZoneList'] = $result;
                    unset($result);
                    $applyList[$key] = $itemData;
                }
            }
        }
        $responseData['data']         = $applyList;
        $responseData['current_page'] = $categoryList->currentPage();
        $responseData['last_page']    = $categoryList->lastPage();
        $responseData['per_page']     = $categoryList->listRows();
        $responseData['total']        = $categoryList->total();
        return $responseData;
    }

    /**
     * @param $firstCategoryId 商品一级分类ID
     * @param $secondCategoryId 商品二级分类ID
     * @param $thirdCategoryId 商品三级分类ID
     * @return string[]
     */
    public function getCategory($firstCategoryId, $secondCategoryId, $thirdCategoryId): array
    {
        //需要从缓存中获取数据
        $categoryList = $this->getList();
        $data         = ['first_category_name' => '', 'second_category_name' => '', 'third_category_name' => '']; //初始化
        foreach ($categoryList as $value) {
            if ($value['c_id'] == $firstCategoryId && $value['level'] == 1) {
                $data['first_category_name'] = $value['name'];
                foreach ($value['children'] as $vv) {
                    if ($vv['c_id'] == $secondCategoryId && $vv['level'] == 2) {
                        $data['second_category_name'] = $vv['name'];
                        foreach ($vv['children'] as $vvv) {
                            if ($vvv['c_id'] == $thirdCategoryId && $vvv['level'] == 3) {
                                $data['third_category_name'] = $vvv['name'];
                                break;
                            }
                        }
                    }
                }
            }
        }
        return $data;
    }

    /**
     * @param $requestData
     * @return array
     * @throws \think\exception\DbException
     */
    public function getProjectList($requestData): array
    {
        $shopId = $requestData['shop_id'];
        $field  = 'c_id,first_category_id,second_category_id,third_category_id,third_category_unique_name,unique_name_status,sort,category_status,create_time';
        return $this->goodsCategoryModel->field($field)->where(['shop_id' => $shopId, 'status' => 1])->order('c_id desc')->paginate(config('admin.limit'))->each(function ($item, $key) {
            $firstCategoryId            = $item->first_category_id;
            $secondCategoryId           = $item->second_category_id;
            $thirdCategoryId            = $item->third_category_id;
            $categoryArr                = $this->getCategory($firstCategoryId, $secondCategoryId, $thirdCategoryId);
            $item->first_category_name  = $categoryArr['first_category_name'];
            $item->second_category_name = $categoryArr['second_category_name'];
            $item->third_category_name  = $categoryArr['third_category_name'];
            unset($item->first_category_id, $item->second_category_id, $item->third_category_id);
        })->toArray();
    }

    /**
     * 编辑三级分类个性化名称
     * @param $requestData
     * @return bool
     */
    public function updateCategoryName($requestData): bool
    {
        $updateData['third_category_unique_name'] = $requestData['third_category_unique_name'];
        $updateData['unique_name_status']         = '0'; //审核状态修改为待审核
        $updateData['update_time']                = time();
        return $this->goodsCategoryModel->where(['c_id' => $requestData['c_id']])->update($updateData) > 0;
    }

    /**
     * 编辑三级分类状态（上架或下架）
     * @param $requestData
     * @return bool
     */
    public function updateCategoryStatus($requestData): bool
    {
        $category                  = $this->goodsCategoryModel->where(['c_id' => $requestData['c_id']])->findOrEmpty();
        $category->category_status = $category->category_status == 10 ? 20 : 10; //分类状态(10上架 20下架)
        $category->update_time     = time();
        return $category->save();
    }

    /**
     * 通过店铺ID获取商品三级分类列表
     * @param $shopId 店铺id
     * @return array|bool|\PDOStatement|string|\think\Collection|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getThirdCategoryListById($shopId): array
    {
        if (!empty($shopId)) {
            $list = $this->goodsCategoryModel->field('c_id,shop_id,first_category_id,second_category_id,third_category_id,third_category_unique_name,status,unique_name_status')->where(['shop_id' => $shopId, 'status' => 1])->order("sort asc")->select();
            if (!$list->isEmpty()) {
                foreach ($list as $value) {
                    $categoryArr                 = $this->getCategory($value->first_category_id, $value->second_category_id, $value->third_category_id);
                    $value->first_category_name  = $categoryArr['first_category_name']; //一级分类名称
                    $value->second_category_name = $categoryArr['second_category_name']; //二级分类名称
                    $value->third_category_name  = $categoryArr['third_category_name']; //三级分类名称
                    if ($value->unique_name_status !== 1) {
                        //如果店铺申请三级分类名称个性化名称，平台未审核通过，则显示三级分类名称
                        $value->third_category_unique_name = $value->third_category_name; //三级分类名称个性化名称
                    }
                }
                return $list->toArray();
            }
        }
        return [];
    }

    /**
     * @param $id
     * @return array|string|string[]|null
     */
    public function getByRegionId($id)
    {
        $region = (new RegionService())->getByRegionId($id);
        return preg_replace('/,/', '-', $region['merger_name']);
    }

    /**
     * 通过店铺ID获取三级分类列表
     * $operationType  板块类型:0-ALL(非营养膳+营养膳食) ; 1-非营养膳食；2-营养膳食
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     * @throws DbException
     */
    public static function getThirdCategoryList($shopId): array
    {
        if (empty($shopId)) {
            return [];
        }
        //商品分类列表（三级分类列表）
        $goodsCategoryModel          = new GoodsCategoryModel();
        $goodsCategoryBasicModel     = new GoodsCategoryBasicModel();
        $field                       = 'c_id,shop_id,first_category_id,second_category_id,third_category_id,third_category_unique_name,unique_name_status';
        $where                       = [
            ['shop_id', '=', $shopId],
            ['status', '=', '1'],
            ['category_status', '=', '10'], //只显示上架的三级分类
            ['is_delete', '=', '0']
        ];
        $firstCategoryIds            = []; //存储firstCategoryIds
        $firstCategoryList           = []; //存储firstCategory
        $goods_category_list_grouped = [];
        $goods_category_list         = $goodsCategoryModel->field($field)->where($where)->order('sort asc,c_id desc')->select();
        if (!$goods_category_list->isEmpty()) {
            foreach ($goods_category_list as $goods_category) {
                if ($goods_category->unique_name_status != 1) {
                    $goods_category->third_category_unique_name = $goodsCategoryBasicModel->where(['c_id' => $goods_category->third_category_id])->value('name');
                }
                //处理first_category
                //firstCategoryIds去重
                if (!in_array($goods_category->first_category_id, $firstCategoryIds)) {
                    //firstCategoryId没有重复的
                    $firstCategoryIds[]  = $goods_category->first_category_id;
                    $firstCategoryList[] = [
                        'id'   => $goods_category->first_category_id,
                        'name' => $goodsCategoryBasicModel->where(['c_id' => $goods_category->first_category_id])->value('name'),
                    ];
                }
            }
            //对$goods_category_list数据进行分组，key值为first_category_id值
            $goods_category_list_grouped = array_reduce($goods_category_list->toArray(), function ($result, $item) {
                $map['id']                            = $item['c_id'];
                $map['name']                          = $item['third_category_unique_name'];
                $result[$item['first_category_id']][] = $map;
                return $result;
            }, []);
        }
        return [
            'goods_category_list' => $goods_category_list_grouped,
            'first_category_list' => $firstCategoryList ?? [],
        ];
    }

    /**
     * 通过店铺ID获取服务地区信息
     * @param int $shopId
     * @return array
     */
    public static function getServiceZone(int $shopId): array
    {
        $model = new  ServiceZoneModel();
        $zone  = $model->where(['shop_id' => $shopId])->findOrEmpty();
        if (!empty($zone->toArray())) {
            $regionService = new RegionService();
            return $regionService->getRegionDetail($zone->district_id);
        }
        return [];
    }
}
