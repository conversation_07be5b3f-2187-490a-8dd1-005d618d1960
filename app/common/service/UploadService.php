<?php

namespace app\common\service;

use app\common\enum\Response;
use app\common\model\UploadFileModel;
use think\facade\Db;
use think\facade\Filesystem;

class UploadService
{
    public static function get(int $id, $thumbs = ""): false|string
    {
        $file = UploadFileModel::where('id', $id)->where('is_delete', '=', 0)->field('storage, file_url')->find();
        if (!$file) {
            return false;
        }
        // storage == 远程地址 
        if ($file->storage == 'remote') {
            $file->format_url = $file->file_url;
        } else {
            $th = $thumbs ? "thumbs/" . $thumbs . "/" : "";
            $file->format_url = config('upload.' . $file->storage . '.domain') . $th . $file->file_url;
        }
        return $file->format_url;
    }

    public static function getByIds(array $ids = [], $thumbs = ""): array
    {
        $files = UploadFileModel::where('id', 'in', $ids)->where('is_delete', '=', 0)->field('id, storage, file_url')->select();
        $data  = [];
        foreach ($files as &$file) {
            // storage == 远程地址 
            if ($file->storage == 'remote') {
                $file->format_url = $file->file_url;
            } else {
                $th = $thumbs ? "thumbs/" . $thumbs . "/" : "";
                $file->format_url = config('upload.' . $file->storage . '.domain') . $th . $file->file_url;
            }
            $data[$file->id] = $file->format_url;
        }
        return $data;
    }

    public static function getList(array $ids = []): array
    {
        $files = UploadFileModel::where('id', 'in', $ids)->where('is_delete', '=', 0)->field('id, storage, file_name, file_url')->select();
        if (!$files) {
            return [];
        }

        $data = [];
        foreach ($files as $file) {
            // storage == 远程地址 
            if ($file->storage == 'remote') {
                $file->format_url = $file->file_url;
            } else {
                $file->format_url = config('upload.' . $file->storage . '.domain') . $file->file_url;
            }
            $data[] = [
                'image_id'   => $file->id,
                'format_url' => $file->format_url,
                'file_name'  => $file->file_name,
            ];
        }
        return $data;
    }

    /**
     * 上传文件
     * @param $file
     * @param $path
     * @param $uplocation 上传本地文件的filename
     * @return array
     */
    public function upload($file, $type = 'image', $uplocation = ""): array
    {
        $key = config('upload.uplocation');
        if (empty($uplocation)) {
            if ($key == 'local') {
                //上传本地
                $path = $this->uploadLocal($type, $file);
            }

            if ($key == 'ysa') {
                //上传自己搭的
                $path = $this->uploadYsa($type, $file);
            }
        } else {
            $path = $this->uploadTmpYsa($type, $file);
            if ($path) {
                unlink($file); //删除临时文件
            }
        }
        if ($path == false) {
            return fail(Response::UPLOAD_IMAGE_FAIL);
        }
        $data = [
            'storage'   => config('upload.uplocation'), //存储位置
            'group_id'  => 0, //分组id
            'file_url'  => $path,
            'file_type' => $type,
            'file_name' => $uplocation,
        ];
        if (empty($uplocation)) {
            $data['file_name'] = $file->getOriginalName(); //文件名
            $data['file_size'] = $file->getSize(); //文件大小
            $data['extension'] = $file->getOriginalExtension();
        }
        $insertId = UploadFileModel::insertGetId($data);
        if ($insertId <= 0) {
            return fail(Response::UPLOAD_IMAGE_FAIL);
        }
        $data[$type . '_id'] = $insertId;
        $data['format_url']  = config('upload.' . $key . '.domain') . $path;
        return $data;
    }

    // 本地上传
    private function uploadLocal($type, $file)
    {
        $key      = config('upload.uplocation');
        $saveName = Filesystem::disk(config('upload.' . $key . '.disk'))->putFile($type, $file, 'uniqid');
        if ($saveName) {
            return $saveName;
        }
        return false;
    }

    // 自己搭的
    private function uploadYsa($type, $file)
    {

        $typeList = ['image' => 1, 'file' => 2, 'video' => 3, 'audio' => 4];
        $typeId   = $typeList[$type] ?? 1;
        $pathName = $file->getPathName();
        $mimeType = mime_content_type($pathName);
        $fileName = $file->getOriginalName();
        $data     = [
            // 'file' => new \CURLFile(realpath($file->getPathName())),
            'file' => curl_file_create($pathName, $mimeType, $fileName),
            'type' => $typeId,
        ];
        $key      = config('upload.uplocation');
        $url      = config('upload.' . $key . '.url');
        $result   = curlPost($url, $data); //上传文件
        if ($result) {
            $result = json_decode($result, true);
            if (isset($result['file_name']) && $result['file_name'] != '') {
                return $result['file_name'];
            }
        }
        return false;
    }

    private function uploadTmpYsa($type, $filepath)
    {
        $typeList = ['image' => 1, 'file' => 2, 'video' => 3, 'audio' => 4];
        $typeId   = $typeList[$type] ?? 1;
        $key      = config('upload.uplocation');
        $url      = config('upload.' . $key . '.url');
        $file     = new \CURLFile($filepath);
        $result   = curlPost($url, [
            'file' => $file,
            'type' => $typeId,
        ]); //上传文件
        if ($result) {
            $result = json_decode($result, true);
            if (isset($result['file_name']) && $result['file_name'] != '') {
                return $result['file_name'];
            }
        }
        return false;
    }

    /**
     * @param int $type
     * @param array $fileData
     * @return int
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function saveFileData(int $type, array $fileData): int
    {
        if (empty($fileData['file_name'])) {
            return 0;
        }
        //相同的文件（上传前文件名相同）已经上传，则不需要再次上传，直接返回文件ID
        $exist = self::getByFileUrl($fileData['file_name']);
        if (!empty($exist)) {
            return $exist['id'];
        }
        //1: 图片;2: 文件;3: 视频;4: 音频
        $typeList          = [1 => 'image', 2 => 'file', 3 => 'video', 4 => 'audio'];
        $fileType          = $typeList[$type] ?? $typeList[1];
        $extensionArray    = explode('.', $fileData['file_name']);
        $file['storage']   = 'ysa';
        $file['file_url']  = $fileData['file_name'] ?? ''; //上传前文件名相同，则上传后file_url值也相同
        $file['file_name'] = $fileData['original_name'] ?? ''; //上传前文件名
        $file['file_size'] = $fileData['file_size'] ?? '';
        $file['file_type'] = $fileType;
        $file['extension'] = $extensionArray[1];
        $insertId          = UploadFileModel::insertGetId($file);
        if ($insertId <= 0) {
            return 0;
        }
        return $insertId;
    }

    /**
     * 通过fileUrl获取指定字段数据
     * @param $fileUrl
     * @return string
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getByFileUrl($fileUrl)
    {
        if (empty($fileUrl)) {
            return '';
        }
        $files = UploadFileModel::where('file_url', '=', $fileUrl)->where('is_delete', '=', 0)->field('id, storage, file_url')->find();
        if (empty($files)) {
            return '';
        }
        return $files->toArray();
    }

    /**
     * 通过ID获取指定字段数据
     * @param int $id
     * @return string
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getById(int $id)
    {
        if (empty($id)) {
            return '';
        }
        $files = UploadFileModel::where('id', '=', $id)->where('is_delete', '=', 0)->field('id, storage, file_url,expand_field')->find();
        if (empty($files)) {
            return '';
        }
        return $files->toArray();
    }

    /**
     * @param $id
     * @param $updateData
     * @return int
     * @throws \think\db\exception\DbException
     */
    public static function updateFileData($id, $updateData)
    {
        return Db::name("upload_file")->where(['id' => $id])->update($updateData);
    }
}
