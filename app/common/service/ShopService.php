<?php

namespace app\common\service;

use app\common\enum\Response;
use app\common\enum\shop\Rule;
use app\common\model\AdminModel;
use app\common\model\GoodsCategoryBasicModel;
use app\common\model\RegionModel;
use app\common\model\ShopBannerModel;
use app\common\model\ShopModel;
use app\common\model\ShopSharePayModel;
use app\common\model\ShopShareRuleModel;
use app\common\model\ShopShareSettlementModel;
use app\common\model\StaffModel;
use app\common\model\UploadFileModel;
use think\facade\Db;
use think\facade\Event;

class ShopService
{
    public static $basicColumns = [
        'id',
        'shop_name',
        'logo_id', //门店logo
        'image_id', //门店图片
        'contact_name',
        'contact_phone',
        'opening_hours',
        'province_id',
        'city_id',
        'area_id',
        'address',
        'longitude',
        'latitude',
        'introduce',
        'service_times', //服务次数
        'start_work_year', // 开始营业时间

        'business_license_image',
        'sign_img_id',
        'id_number',
        'merchant_name',
        'company_name',
        'merchant_id',
        'merchant_status',
        'merchant_reason',
        'legal_name', //法人姓名
        'sort',
        'status',
        'jzq_email',
        'jzq_sign_id',
        'jzq_status',
        'jzq_reason', //拒绝原因
        'audit_status',
        'intro_images', //门店介绍图片
        'intro_video', //门店介绍视频
    ];

    public static $shareRuleColumns = [
        'id',
        'first_category',
        'first_category_name',
        'group_id',
        'payment_type',
        'fixed_amount',
        'percentage',
        'validity_period',
        'is_active',
    ];

    public function getShopNameList($name = ''): array
    {
        if (empty($name)) {
            return [];
        }
        //未过期，审核通过的门店
        $list = ShopModel::where('shop_name', 'like', "%{$name}%")
            ->where('expire_time', '>', time())
            ->where('audit_status', ShopModel::STATUS_PASS)
            ->order('id', 'asc')
            ->field('id, shop_name')
            ->select();

        return $list->toArray();
    }

    public function getShopList(array $param = []): array
    {
        $where = [
            ['id', '>', 0],
        ];
        if (isset($param['shop_id']) && $param['shop_id'] != '') {
            $where[] = ['id', '=', $param['shop_id']];
        }
        if (isset($param['shop_name']) && $param['shop_name'] != '') {
            $where[] = ['shop_name', 'like', "%{$param['shop_name']}%"];
        }
        if (isset($param['phone']) && $param['phone'] != '') {
            $where[] = ['contact_phone', '=', "{$param['phone']}"];
        }
        if (isset($param['status']) && $param['status'] != '') {
            $where[] = ['status', '=', "{$param['status']}"];
        }
        if (isset($param['audit_status']) && $param['audit_status'] != '') {
            $where[] = ['audit_status', '=', "{$param['audit_status']}"];
        }

        $pageSize = config('admin.limit');
        if (isset($param['limit']) && $param['limit'] > 0) {
            $pageSize = $param['limit'];
        }

        $list          = ShopModel::order('id desc')->where($where)->paginate($pageSize)->toArray();
        $shopIds       = array_column($list['data'], 'id');
        $admin         = AdminModel::where('shop_id', 'in', $shopIds)->where('is_super', 1)->field('shop_id, username')->select()->toArray();
        $adminUsername = array_column($admin, 'username', 'shop_id');
        foreach ($list['data'] as &$value) {
            $value['username'] = $adminUsername[$value['id']] ?? '';
        }
        unset($value);
        return $list;
    }

    public function getShopData(int $id, $isAll = 0): ShopModel|array
    {
        if ($isAll == 0) {
            $shop = ShopModel::where('id', $id)->field(self::$basicColumns)->find();
        } else {
            $shop = ShopModel::where('id', $id)->find();
        }
        if (!$shop) {
            return fail(Response::ACCOUNT_FORBIDDEN, '门店不存在');
        }
        $fids = [$shop->logo_id, $shop->image_id, $shop->business_license_image, $shop->sign_img_id];
        // intro_images门店介绍图片ID集合，多个用逗号分隔
        if (!empty($shop->intro_images)) {
            $intro_images =  explode(',', $shop->intro_images);
            $fids = array_merge($fids,  $intro_images);
        }
        // intro_video门店介绍视频ID
        if (!empty($shop->intro_video)) {
            $fids[] = $shop->intro_video;
        }
        $files                               = UploadService::getByIds($fids);
        $shop->format_logo                   = formatLogo($files[$shop->logo_id] ?? '');
        $shop->format_image                  = formatLogo($files[$shop->image_id] ?? '');
        $shop->format_business_license_image = formatLogo($files[$shop->business_license_image] ?? '');
        $shop->format_sign_img = formatLogo($files[$shop->sign_img_id] ?? '');
        //  $intro_images门店介绍图片
        if (!empty($shop->intro_images)) {
            $intro_images =  explode(',', $shop->intro_images);
            $shop->format_intro_images = array_map(function ($v) use ($files) {
                return [
                    'id'   => $v,
                    'path' => formatLogo($files[$v] ?? ''),
                ];
            }, $intro_images);
        }
        // intro_video门店介绍视频
        if (!empty($shop->intro_video)) {
            $shop->format_intro_video = formatLogo($files[$shop->intro_video] ?? '');
        }
        //处理店铺认证标签
        if (!empty($shop->auth_tag)) {
            $shop->auth_tag_label = TagService::getByIds(explode(',', $shop->auth_tag), 2);
        } else {
            $shop->auth_tag_label = '';
        }
        $regionService = new RegionService();
        $shop->area    = $regionService->getRegionDetail($shop->area_id);
        $serviceZone   = GoodsCategoryService::getServiceZone($id);
        if (!empty($serviceZone)) {
            $zoneData[0]['value'] = $serviceZone['district_code'];
            $zoneData[0]['label'] = $serviceZone['district_name'];
        } else {
            $zoneData[0]['value'] = '';
            $zoneData[0]['label'] = '';
        }
        $shop->service_zone = $zoneData;
        //获取服务地区信息
        $shop->service_zone_admin = $serviceZone;
        $shop->opening_hours = empty($shop->opening_hours) ? "9:00-18:00" : $shop->opening_hours; //默认营业时间
        return $shop;
    }

    // 获取门店详情
    public function getShop(int $id, $isAll = 0): array
    {
        $shop = $this->getShopData($id, $isAll);
        return success($shop->toArray());
    }

    // 添加门店
    public function addShop(array $param = [], &$msg = ""): bool
    {
        $exist = AdminModel::where('phone', $param['phone'])->whereOr('username', $param['user_name'])->findOrEmpty();
        if (!$exist->isEmpty()) {
            $msg = '账号已存在';
            return false;
        }

        // 启动事务
        Db::startTrans();
        try {
            $data      = [
                'shop_name'     => trim($param['shop_name'] ?? ''),
                'contact_name'  => trim($param['linkman'] ?? ''),
                'contact_phone' => trim($param['phone'] ?? ''),
                'create_time'   => time(),
                'expire_time'   => time() + (365 * 24 * 60 * 60), //1年后
            ];
            $shopId    = ShopModel::insertGetId($data);
            $adminData = [
                'shop_id'       => intval($shopId),
                'username'      => $param['user_name'],
                'password'      => makePassword($param['password']),
                'phone'         => $param['phone'] ?? '',
                'status'        => intval($param['status'] ?? 1),
                'register_time' => time(),
                'is_super'      => 1, // 超级管理员
            ];
            AdminModel::insertGetId($adminData);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            $msg = $e->getMessage();
            return false;
        }
        // 请求auth服务注册账号
        $config = config('admin.services');
        $data = [
            'user_name' => $param['user_name'],
            'password' => $param['password'],
            'nick_name' => trim($param['shop_name'] ?? ''),
            'linkman' => trim($param['linkman'] ?? ''),
            'phone' => trim($param['phone'] ?? ''),
            'type' => 'store',
            'type_id' => $shopId,
        ];
        $res =  curlPostApiContentByUrlAndParams($config['Auth']['BaseURL'] . $config['Auth']['Api']['Register'], $data, ['Authorization: Bearer ' . $param['token'] ?? '']);
        if (empty($res) || $res['code'] != 1) {
            $msg = $res['msg'];
            AdminModel::where('shop_id', $shopId)->delete();
            ShopModel::where('id', $shopId)->delete();
            return false;
        }
        return true;
    }

    // 编辑门店
    public function editShop(array $param = []): array
    {
        $id = intval($param['id']);
        if ($id <= 0) {
            return fail(Response::ACCOUNT_EDIT_FORBIDDEN);
        }
        $shop = ShopModel::where('id', $id)->find();
        if (!$shop) {
            return fail(Response::ACCOUNT_EDIT_FORBIDDEN);
        }

        $data = $this->filterParam($param);
        if (empty($data)) {
            return fail(Response::ACCOUNT_EDIT_FORBIDDEN);
        }

        // 审核初始化
        $data['audit_status']  = ShopModel::STATUS_WAIT;
        $data['audit_reason']  = '';
        $data['audit_time']    = 0;
        $data['audit_user_id'] = 0;
        $data['update_time']   = time();

        $res = ShopModel::where('id', $id)->update($data);
        if (!$res) {
            return fail(Response::ACCOUNT_EDIT_FORBIDDEN, '编辑失败');
        }
        Event::trigger('AdminLog', ['admin_id' => $param['admin_id'], 'shop_id' => $param['shop_id'], 'remark' => '编辑门店']);
        return $this->getShop($id);
    }

    // 编辑门店
    public function editShopForSys(array $param = []): bool
    {
        $id = intval($param['id']);
        if ($id <= 0) {
            return false;
        }
        $shop = ShopModel::where('id', $id)->find();
        if (!$shop) {
            return false;
        }

        $data = $this->filterParam($param);
        if (empty($data)) {
            return false;
        }

        if (isset($param['audit_status'])) {
            $data['audit_status']  = intval($param['audit_status']);
            $data['audit_reason']  = $param['audit_reason'] ?? '';
            $data['audit_time']    = time();
            $data['audit_user_id'] = 0;
        }
        if (isset($param['expire_time'])) {
            $data['expire_time'] = strtotime($param['expire_time']);
        }
        $data['update_time'] = time();
        $res                 = ShopModel::where('id', $id)->update($data);
        if (!$res) {
            return false;
        }
        return true;
    }

    /**
     * 添加分帐
     *
     * @param int $shopId 门店ID
     * @param string $merchant_id 商户ID，优先使用传参
     * @param string $merchant_name 商户名称，优先使用传参
     * @param string &$msg 错误消息
     * @return bool
     */
    public function addReceiverByShopId(int $shopId = 0, string $merchant_id = "", string $merchant_name = "", string &$msg = ''): bool
    {
        // 验证门店是否存在
        $merchant = ShopModel::where('id', $shopId)->field('merchant_id,merchant_name')->findOrEmpty();
        if ($merchant->isEmpty()) {
            $msg = '门店不存在';
            return false;
        }

        // 优先使用传参，如果为空则使用数据库中的值
        $merchantId = !empty($merchant_id) ? $merchant_id : $merchant['merchant_id'];
        $merchantName = !empty($merchant_name) ? $merchant_name : $merchant['merchant_name'];

        // 验证分账信息是否完整
        if (empty($merchantId) || empty($merchantName)) {
            $msg = '分帐信息不完整';
            return false;
        }

        // 调用微信支付服务添加分账
        $wxPayService = new WxPayService();
        $res = $wxPayService->addreceiver($merchantId, $merchantName, $msg);
        if (!$res) {
            ShopModel::where('id', $shopId)->update(['merchant_status' => 2, 'merchant_reason' => $msg]);
            return false;
        }

        // 更新门店分账信息
        return ShopModel::where('id', $shopId)->update([
            'merchant_status' => 1,
            'merchant_reason' => '',
            'merchant_id' => $merchantId,
            'merchant_name' => $merchantName
        ]);
    }

    // 过滤参数 : 保持旧接口传参-> 返回新的表结构
    private function filterParam($param = []): array
    {
        $res = [];
        if (isset($param['shop_name']) && $param['shop_name']) {
            $res['shop_name'] = trim($param['shop_name']);
        }
        if (isset($param['logo_id']) && $param['logo_id']) {
            $res['logo_id'] = intval($param['logo_id']);
        }
        if (isset($param['image_id']) && $param['image_id']) {
            $res['image_id'] = intval($param['image_id']);
        }
        if (isset($param['contact_name']) && $param['contact_name']) {
            $res['contact_name'] = trim($param['contact_name']);
        }
        if (isset($param['contact_phone']) && $param['contact_phone']) {
            $res['contact_phone'] = trim($param['contact_phone']);
        }
        if (isset($param['opening_hours']) && $param['opening_hours']) {
            $res['opening_hours'] = trim($param['opening_hours']);
        }
        if (isset($param['address'])) {
            $res['province_id'] = intval($param['province_id']);
            $res['city_id']     = intval($param['city_id']);
            $res['area_id']     = intval($param['area_id']);
            $res['address']     = trim($param['address']);
        }
        if (isset($param['introduce']) && $param['introduce']) {
            $res['introduce'] = trim($param['introduce']);
        }
        if (isset($param['longitude']) && $param['longitude'] && isset($param['latitude']) && $param['latitude']) {
            $res['longitude'] = $param['longitude'];
            $res['latitude']  = $param['latitude'];
        }
        if (isset($param['service_times']) && $param['service_times']) {
            $res['service_times'] = intval($param['service_times']);
        }
        if (isset($param['start_work_year']) && $param['start_work_year']) {
            $res['start_work_year'] = intval($param['start_work_year']);
        }
        if (isset($param['business_license_image']) && $param['business_license_image']) {
            $res['business_license_image'] = intval($param['business_license_image']);
        }
        if (isset($param['sign_img_id']) && $param['sign_img_id']) {
            $res['sign_img_id'] = intval($param['sign_img_id']);
        }
        if (isset($param['id_number']) && $param['id_number']) {
            $res['id_number'] = trim($param['id_number']);
        }
        if (isset($param['merchant_name']) && $param['merchant_name']) {
            $res['merchant_name'] = trim($param['merchant_name']);
        }
        if (isset($param['legal_name']) && $param['legal_name']) {
            $res['legal_name'] = trim($param['legal_name']);
        }
        if (isset($param['company_name']) && $param['company_name']) {
            $res['company_name'] = trim($param['company_name']);
        }
        if (isset($param['merchant_id']) && $param['merchant_id']) {
            $res['merchant_id'] = trim($param['merchant_id']);
        }
        if (isset($param['sort']) && $param['sort'] && $param['sort'] > 0) {
            $res['sort'] = intval($param['sort']);
        }
        if (isset($param['status'])) {
            $res['status'] = intval($param['status']);
        }
        if (isset($param['intro_images']) && $param['intro_images'] != "") {
            // 图片处理 数字+逗号
            $res['intro_images'] = trim($param['intro_images']); // 去空格
            $res['intro_images'] = str_replace('，', ',', $res['intro_images']); // 替换中文逗号
            $res['intro_images'] = str_replace(' ', '', $res['intro_images']); // 替换空格
            $res['intro_images'] = implode(',', array_unique(explode(',', $res['intro_images']))); // 去重
        }
        if (isset($param['intro_video_json']) && $param['intro_video_json'] != "") {
            $json = json_decode($param['intro_video_json'], true);
            // 根据$json['original_name'] 获取后缀，截取"."后面的字符串
            $extension = pathinfo($json['original_name'], PATHINFO_EXTENSION); // 获取后缀
            $data = [
                'storage'   => config('upload.uplocation'), //存储位置
                'group_id'  => 0, //分组id
                'file_url'  =>  $json['file_name'] ?? '', //文件url
                'file_name' =>  $json['original_name'] ?? '',
                'file_size' =>  $json['file_size'] ?? '',
                'file_type' =>  'video',
                'extension' =>  $extension,
            ];
            if (!empty($json['file_name'])) {
                $res['intro_video'] = UploadFileModel::insertGetId($data);
            }
        }
        return $res;
    }

    public function getShopBannerData(int $id): false|ShopBannerModel
    {
        $banner = ShopBannerModel::where('id', $id)->find();
        if (!$banner) {
            return false;
        }
        $banner->format_image = formatLogo(UploadService::get(intval($banner->image_id)));
        return $banner;
    }

    public function getShopBanner(int $id): array
    {
        $banner = $this->getShopBannerData($id);
        if ($banner === false) {
            return fail(Response::BANNER_NOT_EXIST);
        }
        return success($banner->toArray());
    }

    // 获取门店banner列表
    public function getShopBannerByShopId(int $shopId = 0): array
    {
        $list = ShopBannerModel::where('shop_id', $shopId)
            ->field('id, url, image_id, sort')
            ->order('sort', 'desc')
            ->limit(config('admin.shop.banner_limit'))
            ->select()
            ->toArray();

        foreach ($list as &$item) {
            $item['format_image'] = formatLogo(UploadService::get(intval($item['image_id'])));
        }
        unset($item);
        return $list;
    }

    public function editShopBanner(array $param = []): array
    {
        $id = intval($param['id']);
        if ($id <= 0) {
            return fail(Response::BANNER_NOT_EXIST);
        }
        $banner = ShopBannerModel::where('id', $id)->find();
        if (!$banner) {
            return fail(Response::BANNER_NOT_EXIST);
        }
        $data = [
            'update_time' => time()
        ];
        if (isset($param['url']) && $param['url']) {
            $data['url'] = $param['url'];
        }
        if (isset($param['image_id']) && $param['image_id']) {
            $data['image_id'] = $param['image_id'];
        }
        if (isset($param['sort']) && $param['sort']) {
            $data['sort'] = $param['sort'];
        }
        $res = ShopBannerModel::where('id', $id)->update($data);
        if (!$res) {
            return fail(Response::BANNER_SAVE_FAIL, '编辑失败');
        }
        Event::trigger('AdminLog', ['admin_id' => $param['admin_id'], 'shop_id' => $param['shop_id'], 'remark' => '编辑门店banner']);
        return $this->getShopBanner($id);
    }

    public function deleteShopBanner(array $param = []): array
    {
        $id = intval($param['id']);
        if ($id <= 0) {
            return fail(Response::BANNER_NOT_EXIST);
        }
        $banner = ShopBannerModel::where('id', $id)->find();
        if (!$banner) {
            return fail(Response::BANNER_NOT_EXIST);
        }
        $res = ShopBannerModel::where('id', $id)->delete();
        if (!$res) {
            return fail(Response::BANNER_SAVE_FAIL, '删除失败');
        }

        Event::trigger('AdminLog', ['admin_id' => $param['admin_id'], 'shop_id' => $param['shop_id'], 'remark' => '删除门店banner']);
        return success();
    }

    public function addShopBanner(array $param = []): array
    {
        $total = ShopBannerModel::where('shop_id', $param['shop_id'])->count();
        if ($total >= config('admin.shop.banner_limit')) {
            return fail(Response::BANNER_LIMIT);
        }

        $data = [
            'shop_id'     => $param['shop_id'],
            'url'         => $param['url'] ?? '',
            'image_id'    => $param['image_id'] ?? 0,
            'sort'        => $param['sort'] ?? 0,
            'create_time' => time()
        ];

        $insertId = ShopBannerModel::insertGetId($data);
        if ($insertId <= 0) {
            return fail(Response::BANNER_SAVE_FAIL, '新增失败');
        }
        Event::trigger('AdminLog', ['admin_id' => $param['admin_id'], 'shop_id' => $param['shop_id'], 'remark' => '添加门店banner']);
        return $this->getShopBanner($insertId);
    }

    // 处理单例
    public static function formatRuleData(&$row)
    {
        // $row->format_group = Rule::GROUP_ID_TEXT[$row->group_id] . "服务人员";
        $row->format_group = $row->first_category_name  . ":";
        if ($row->payment_type == Rule::PAYMENT_TYPE_FIXED_AMOUNT) {
            $row->format_text = "每单抽佣固定金额" . $row->fixed_amount . "元";
        } else {
            $row->format_text = "每单抽结算金额的" . $row->percentage . "%";
        }
        $row->format_validity_period = Rule::VALIDITY_PERIOD_TEXT[$row->validity_period ?? 0];
        $row->is_active_bool         = $row->is_active == 1 ? true : false;
    }

    // 处理单例
    public static function formatRuleDataForArray($row)
    {
        // $group_text = Rule::GROUP_ID_TEXT[$row['group_id']] . "服务人员";
        $group_text = $row['first_category_name'] . ":";
        if ($row['payment_type'] == Rule::PAYMENT_TYPE_FIXED_AMOUNT) {
            $payment_text = "每单抽佣固定金额" . $row['fixed_amount'] . "元";
        } else {
            $payment_text = "每单抽结算金额的" . $row['percentage'] . "%";
        }
        return $group_text . " " . $payment_text;
    }

    // 获取门店规则列表
    public function getShareRuleByShopId(int $shopId = 0): array
    {
        $field = [];
        foreach (self::$shareRuleColumns as $column) {
            array_push($field, "A." . $column);
        }
        $query = ShopShareRuleModel::alias('A');
        $query = $query->leftJoin('goods_category_basic B', 'A.first_category = B.c_id');
        $query = $query->where('A.shop_id', $shopId);
        $query = $query->where('B.is_delete', 0);
        $query = $query->field($field);
        $list = $query->order('group_id', 'desc')->select();
        foreach ($list as &$row) {
            $this->formatRuleData($row);
        }
        unset($row);
        return $list->toArray();
    }

    public function getShareRule(int $id): array
    {
        $share = ShopShareRuleModel::where('id', $id)->field(self::$shareRuleColumns)->find();
        if (!$share) {
            return fail(Response::RULE_NOT_EXIST);
        }
        $this->formatRuleData($share);
        return success($share->toArray());
    }

    public function addShare(array $param = []): array
    {
        $fixed_amount = $param['fixed_amount'] ?? 0;
        $percentage   = $param['percentage'] ?? 0;
        $hash = md5($param['shop_id'] . $param['first_category'] . $param['group_id']);
        $exist = ShopShareRuleModel::where('hash', $hash)->findOrEmpty();
        if (!$exist->isEmpty()) {
            return fail(Response::RULE_EXISTED);
        }
        $first_category_name = GoodsCategoryBasicModel::where('c_id', $param['first_category'])->value('name');
        $data         = [
            'hash' => $hash,
            'first_category'        => $param['first_category'],
            'first_category_name'   => $first_category_name,
            'shop_id'         => $param['shop_id'],
            'group_id'        => intval($param['group_id']),
            'validity_period' => intval($param['validity_period']),
            'payment_type'    => $param['payment_type'],
            'fixed_amount'    => $param['payment_type'] == Rule::PAYMENT_TYPE_FIXED_AMOUNT ? $fixed_amount : 0,
            'percentage'      => $param['payment_type'] == Rule::PAYMENT_TYPE_PERCENTAGE_PER_ORDER ? $percentage : 0,
            'is_active'       => intval($param['is_active'] ?? 0),
            'create_time'     => date('Y-m-d H:i:s'),
        ];
        $insertId = ShopShareRuleModel::insertGetId($data);
        if ($insertId <= 0) {
            return fail(Response::RULE_SAVE_FAIL, '新增失败');
        }
        Event::trigger('AdminLog', ['admin_id' => $param['admin_id'], 'shop_id' => $param['shop_id'], 'remark' => '添加门店规则']);
        return $this->getShareRule($insertId);
    }

    public function addShareAll(array $param = []): bool
    {
        $shopId = intval($param['shop_id'] ?? 0);
        if ($shopId <= 0) {
            return false;
        }

        $category = GoodsCategoryBasicModel::where('level', 1)->where('is_delete', 0)->where('name', '!=', '商城')->field('c_id,name')->select()->toArray();
        $category = array_column($category, 'name', 'c_id');
        $data = [];
        $hashList = [];
        foreach ($param as $key => $value) {
            if (is_array($value)) {
                $hash = md5($shopId . $value['first_category']);
                if (in_array($hash, $hashList) === true) {
                    continue;
                }
                $data[] = [
                    'hash' =>  $hash,
                    'first_category'        => $value['first_category'],
                    'first_category_name'   => $category[$value['first_category']],
                    'shop_id'         => $shopId,
                    'group_id'        => 0,
                    'validity_period' => intval($value['validity_period']),
                    'payment_type'    => $value['payment_type'],
                    'fixed_amount'    => $value['payment_type'] == Rule::PAYMENT_TYPE_FIXED_AMOUNT ? $value['fixed_amount']  : 0,
                    'percentage'      => $value['payment_type'] == Rule::PAYMENT_TYPE_PERCENTAGE_PER_ORDER ? $value['percentage'] : 0,
                    'is_active'       => intval($value['is_active'] ?? 0),
                    'create_time'     => date('Y-m-d H:i:s'),
                ];
                array_push($hashList, $hash);
            }
        }
        Db::startTrans();
        try {
            Db::table('shop_share_rule')->where('shop_id', $shopId)->delete();
            Db::table('shop_share_rule')->insertAll($data);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            return false;
        }
        return true;
    }

    public function editShare(array $param = []): array
    {
        $id = intval($param['id']);
        if ($id <= 0) {
            return fail(Response::RULE_NOT_EXIST);
        }
        $share = ShopShareRuleModel::where('id', $id)->find();
        if (!$share) {
            return fail(Response::RULE_NOT_EXIST);
        }
        $data = [
            'payment_type' => $param['payment_type'],
            'update_time'  => date('Y-m-d H:i:s'),
        ];
        if (isset($param['validity_period']) && $param['validity_period']) {
            $data['validity_period'] = $param['validity_period'];
        }
        if (isset($param['fixed_amount']) && $param['fixed_amount']) {
            $data['fixed_amount'] = $param['payment_type'] == Rule::PAYMENT_TYPE_FIXED_AMOUNT ? $param['fixed_amount'] : 0;
        }
        if (isset($param['percentage']) && $param['percentage']) {
            $data['percentage'] = $param['payment_type'] == Rule::PAYMENT_TYPE_PERCENTAGE_PER_ORDER ? $param['percentage'] : 0;
        }
        if (isset($param['is_active'])) {
            $data['is_active'] = intval($param['is_active']);
        }
        $res = ShopShareRuleModel::where('id', $id)->update($data);
        if (!$res) {
            return fail(Response::RULE_SAVE_FAIL, '编辑失败');
        }
        Event::trigger('AdminLog', ['admin_id' => $param['admin_id'], 'shop_id' => $param['shop_id'], 'remark' => '编辑门店规则']);
        return $this->getShareRule($id);
    }

    public function deleteShare(array $param = []): array
    {
        $id = intval($param['id']);
        if ($id <= 0) {
            return fail(Response::RULE_NOT_EXIST);
        }
        $share = ShopShareRuleModel::where('id', $id)->find();
        if (!$share) {
            return fail(Response::RULE_NOT_EXIST);
        }
        $res = $share->delete();
        if (!$res) {
            return fail(Response::RULE_SAVE_FAIL, '删除失败');
        }

        Event::trigger('AdminLog', ['admin_id' => $param['admin_id'], 'shop_id' => $param['shop_id'], 'remark' => '删除门店规则']);
        return success();
    }

    /**
     * 根据shopId新增&更新店铺服务区域
     * @param int $shopId
     * @param array $data
     * @return bool
     */
    public function saveServiceZone(int $shopId, array $data): bool
    {
        $model             = new RegionModel();
        $where             = ['province_code' => $data['province_id'], 'city_code' => $data['city_id'], 'district_code' => $data['district_id']];
        $data['region_id'] = $model->where($where)->value('id');
        return (new \app\common\model\ServiceZoneModel)->updateByShopId($shopId, $data);
    }

    /**
     * 更新店铺信息
     * @param array $data
     * @return bool
     */
    public function saveShop(array $data): bool
    {
        return Db::name('shop')->save($data) > 0;
    }

    // 门店结算
    public function getSettlement(array $param = []): array
    {
        $shopId = intval($param['shop_id'] ?? 0);
        if ($shopId <= 0) {
            return [];
        }
        $rows = ShopShareSettlementModel::where('to_shop_id', $shopId)
            ->field('id, order_id,sum(amount) as total_amount, status')
            ->group('order_id')
            ->order('id desc')
            ->paginate(config('admin.limit'))
            ->toArray();

        return $rows;
    }

    /**
     * 门店结算2
     * getSettlement + getSettlementDetail = 门店结算
     * 
     * M店铺向ABC三店铺借人并完成一笔订单，则M店铺向ABC三店铺结算，M店铺为to_shop_id, ABC店铺为from_shop_id
     * 产生三条记录，分别记录M店铺向ABC店铺结算的佣金金额
     * 订单用人N天，会产生N条薪资记录
     * 
     * 门店结算 = 一条订单记录
     * 门店结算2 = 一条订单记录的详情，描述所有流水
     */
    public function getSettlementDetail(array $param = []): array
    {
        $orderId = $param['order_id'] ?? '';
        if (empty($orderId)) {
            return [];
        }
        $shopId = intval($param['shop_id'] ?? 0);
        if (empty($orderId)) {
            return [];
        }

        $list = ShopShareSettlementModel::where('to_shop_id', $shopId)
            ->where('order_id', $orderId)
            ->order('id desc')
            ->paginate(config('admin.limit'))
            ->toArray();

        $fromShopId = array_column($list['data'], 'from_shop_id');
        $toShopId = array_column($list['data'], 'to_shop_id');
        $shopIds = array_merge($fromShopId, $toShopId);
        $shopIds = array_unique($shopIds);
        $shopList = ShopModel::where('id', 'in', $shopIds)->field('id, shop_name')->select()->toArray();
        $shop = array_column($shopList, 'shop_name', 'id');
        $staffIds = array_column($list['data'], 'staff_id');
        $staffList = StaffModel::where('id', 'in', $staffIds)->field('id, real_name')->select()->toArray();
        $staff = array_column($staffList, 'real_name', 'id');
        foreach ($list['data'] as &$row) {
            $row['from_shop_name'] = $shop[$row['from_shop_id']] ?? '';
            $row['to_shop_name'] = $shop[$row['to_shop_id']] ?? '';
            $row['staff_name'] = $staff[$row['staff_id']] ?? '';
            $row['format_settlement_type'] = $row['settlement_type'] == 1 ? '佣金' : '薪水';
            $list[$row['order_id']][] = $row;
        }
        unset($row);
        return $list;
    }

    // 结算支付
    public function paySettlement(array $param): array
    {
        $orderId = $param['order_id'] ?? '';
        if ($orderId == '') {
            return [];
        }
        $pay = ShopSharePayModel::where('order_id', $orderId)->where('to_shop_id', $param['shop_id'])->findOrEmpty();
        if ($pay->isEmpty()) {
            $list = ShopShareSettlementModel::where('order_id', $orderId)
                ->field('order_id,from_shop_id,to_shop_id,amount')
                ->select()
                ->toArray();
            if ($list) {
                Db::startTrans();
                try {
                    $sum = [];
                    foreach ($list as $row) {
                        if (isset($sum[$row['from_shop_id']])) {
                            $sum[$row['from_shop_id']] += $row['amount'];
                        } else {
                            $sum[$row['from_shop_id']] = $row['amount'];
                        }
                    }

                    $data = [];
                    foreach ($sum as $key => $value) {
                        $data[] = [
                            'order_id' => $orderId,
                            'payment' => 1,
                            'from_shop_id' => $key,
                            'to_shop_id' => $param['shop_id'],
                            'pay' => $value,
                            'pay_status' => 0,
                            'pay_time' => date('Y-m-d H:i:s'),
                        ];
                    }
                    Db::table('shop_share_pay')->insertAll($data);
                    Db::table('shop_share_settlement')->where('order_id', $orderId)->update(['status' => 1]);
                    Db::commit();
                } catch (\Exception $e) {
                    Db::rollback();
                    return [];
                }
            }
        }

        $list = ShopSharePayModel::where('order_id', $orderId)
            ->where('to_shop_id', $param['shop_id'])
            ->order('id desc')
            ->paginate(config('admin.limit'))
            ->toArray();

        if (!empty($list['data'])) {
            $fromShopId = array_column($list['data'], 'from_shop_id');
            $shop = ShopModel::where('id', 'in', $fromShopId)->field('id,shop_name,contact_name,contact_phone')->select()->toArray();
            $shop = array_column($shop, null, 'id');
            foreach ($list['data'] as &$row) {
                $row['from_shop'] = $shop[$row['from_shop_id']] ?? [];
            }
        }
        return  $list;
    }

    public function paySettlementConfirm(array $param = [], string &$msg = ''): bool
    {
        $pay = ShopSharePayModel::where('id', $param['pay_id'])->where('to_shop_id', $param['shop_id'])->findOrEmpty();
        if ($pay->isEmpty()) {
            $msg = '支付记录不存在';
            return false;
        }
        $pay->pay_status = ShopSharePayModel::STATUS_PAID;
        $pay->save();

        $exist = ShopSharePayModel::where('order_id', $pay->order_id)
            ->where('pay_status', ShopSharePayModel::STATUS_UNPAID)
            ->findOrEmpty();
        if ($exist->isEmpty()) {
            ShopShareSettlementModel::where('order_id', $pay->order_id)->update(['status' => ShopShareSettlementModel::STATUS_FINISH]);
        }
        return true;
    }
}
