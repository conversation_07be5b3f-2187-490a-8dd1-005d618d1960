<?php

declare(strict_types=1);

namespace app\common\service;

use app\common\model\BannerModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class BannerService
{
    /**
     * 列表
     * @param $type
     * @param $number
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getList($type, $number): array
    {
        $banners = BannerModel::field('id,image_id,link_url,type,is_external,remark')->where(['type' => $type, 'status' => 1])->order('sort asc,id desc')->limit($number)->select();
        // 检查获取的集合是否为空
        if ($banners->isEmpty()) {
            return [];
        }

        // 使用 map 方法处理每个轮播图，添加图片URL并移除 image_id
        $banners = $banners->map(function ($banner) {
            $banner['image_url'] = UploadService::get($banner['image_id'], "600x600"); // 获取图片的URL
            unset($banner['image_id']); // 从结果中移除 image_id
            return $banner; // 返回处理后的轮播图
        });
        return $banners->toArray();
    }
}
