<?php

namespace app\common\service;

use app\common\model\TongueCheckRecordModel;
use app\common\model\HealthCheckReportModel;
use app\common\model\MemberModel;
use think\facade\Config;
use think\facade\Log;
use think\facade\Cache;
use think\facade\Db;

/**
 * 舌诊服务类
 */
class TongueService
{
    /**
     * API 基础URL
     * @var string
     */
    protected $baseUrl = 'https://www.ai-tongue.com';

    /**
     * API 应用ID
     * @var string
     */
    protected $loginName;

    /**
     * API 应用密钥
     * @var string
     */
    protected $loginPassword;

    /**
     * 构造函数
     */
    public function __construct()
    {
        // 从配置文件中获取 API 凭证
        $this->loginName = Config::get('api.tongue.loginName', '18951556401');
        $this->loginPassword = Config::get('api.tongue.loginPassword', '123456');
    }

    /**
     * 获取访问令牌
     *
     * @param bool $forceRefresh 是否强制刷新令牌，默认为 false
     * @return array|bool 成功返回包含 access_token 的数组，失败返回 false
     */
    public function getAccessToken($forceRefresh = false)
    {
        // 缓存键名
        $cacheKey = 'tongue_access_token';

        // 如果不强制刷新，尝试从缓存获取
        if (!$forceRefresh) {
            $cachedToken = Cache::get($cacheKey);
            if ($cachedToken) {
                // 检查令牌是否即将过期（提前5分钟刷新）
                if ($this->isTokenExpiringSoon($cachedToken)) {
                    // 令牌即将过期，强制刷新
                    return $this->getAccessToken(true);
                }
                return $cachedToken;
            }
        }

        try {
            $url = $this->baseUrl . '/backend/auth/user/pwd/signin';

            $params = [
                'loginName' => $this->loginName,
                'loginPassword' => md5($this->loginPassword),
            ];
            $response = curlPostApiContentByUrlAndParams($url . '?' . http_build_query($params), [], ['Authorization: Basic Y2xvdWRhcHA6MTIzNDU2']);
            if (isset($response['code']) && $response['code'] === 0 && isset($response['data']['access_token'])) {
                // 获取令牌成功，缓存
                $tokenData = $response['data'];

                // 记录获取时间
                $tokenData['obtain_time'] = time();

                // 缓存令牌数据
                Cache::set($cacheKey, $tokenData, $tokenData['expires_in'] - 60);
                return $tokenData;
            } else {
                Log::error('获取舌诊访问令牌失败: ' . json_encode($response, JSON_UNESCAPED_UNICODE));
                return false;
            }
        } catch (\Exception $e) {
            Log::error('获取舌诊访问令牌异常: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 检查令牌是否即将过期
     *
     * @param array $tokenData 令牌数据
     * @param int $bufferSeconds 提前多少秒认为即将过期，默认300秒（5分钟）
     * @return bool 如果令牌即将过期返回 true，否则返回 false
     */
    protected function isTokenExpiringSoon($tokenData, $bufferSeconds = 300)
    {
        // 如果没有获取时间，认为即将过期
        if (!isset($tokenData['obtain_time'])) {
            return true;
        }

        // 计算令牌已经存在的时间（秒）
        $tokenAge = time() - $tokenData['obtain_time'];

        // 令牌有效期为7200秒，如果已经存在的时间超过(7200-buffer)秒，则认为即将过期
        return $tokenAge > (7200 - $bufferSeconds);
    }

    /**
     * 同步健康检测报告数据
     *
     * @param string|null $phone 检测人手机号，可选
     * @param int $size 查询结果数量，默认20
     * @return array 同步结果
     */
    public function syncHealthCheckReports($phone = null, $size = 20)
    {
        try {
            // 获取访问令牌
            $tokenData = $this->getAccessToken();
            if (!$tokenData) {
                return [
                    'success' => false,
                    'message' => '获取访问令牌失败',
                    'total' => 0,
                    'inserted' => 0,
                    'updated' => 0,
                    'failed' => 0
                ];
            }

            // 获取最新的检测时间作为开始时间
            $latestCheckTime = HealthCheckReportModel::getLatestCheckTime();
            $startTime = $latestCheckTime ? date('Y-m-d H:i:s', strtotime($latestCheckTime) - 300) : null;
            $endTime = date('Y-m-d H:i:s'); // 当前时间作为结束时间

            // 构建请求参数
            $params = [
                'size' => $size
            ];

            // 添加可选参数
            if ($phone) {
                $params['phone'] = $phone;
            }

            if ($startTime) {
                $params['start'] = $startTime;
            }

            if ($startTime && $endTime) {
                $params['end'] = $endTime;
            }
            // 发送请求获取健康检测报告数据
            $url = $this->baseUrl . '/backend/devicecheck/m/report/query';
            $response = curlGetApiContentByUrl($url . '?' . http_build_query($params), [], ['Authorization: bearer ' . $tokenData['access_token']]);
            // 检查响应是否成功
            if (!isset($response['code']) || $response['code'] !== 0 || !isset($response['data']) || !is_array($response['data'])) {
                return [
                    'success' => false,
                    'message' => '获取健康检测报告失败',
                    'total' => 0,
                    'inserted' => 0,
                    'updated' => 0,
                    'failed' => 0
                ];
            }

            // 处理响应数据
            $reports = $response['data'];
            $total = count($reports);
            $inserted = 0;
            $updated = 0;
            $failed = 0;

            // 开始事务
            Db::startTrans();

            try {
                foreach ($reports as $report) {
                    // 查找对应的会员
                    $memberId = 0;
                    if (!empty($report['phone'])) {
                        $member = MemberModel::where('mobile', $report['phone'])->findOrEmpty();
                        if (!$member->isEmpty()) {
                            $memberId = $member->id;
                        }
                    }

                    // 检查是否已存在相同的报告记录(设备编码+检测时间)，不加用户id是有可能用户是0
                    $checkTime = isset($report['time']) ? date('Y-m-d H:i:s', strtotime($report['time'])) : date('Y-m-d H:i:s');
                    $existingReport = HealthCheckReportModel::where('device_code', $report['deviceCode'])
                        ->where('check_time', $checkTime)
                        ->findOrEmpty();

                    // 准备保存的数据
                    $reportData = [
                        'member_id' => $memberId,
                        'phone' => $report['phone'] ?? null,
                        'device_code' => $report['deviceCode'] ?? '',
                        'device_name' => $report['deviceName'] ?? null,
                        'login_user_code' => $report['loginUserCode'] ?? null,
                        'login_user_name' => $report['loginUserName'] ?? null,
                        'team_code' => $report['teamCode'] ?? null,
                        'team_name' => $report['teamName'] ?? null,
                        'name' => $report['name'] ?? null,
                        'sex' => $report['sex'] ?? null,
                        'age' => $report['age'] ?? null,
                        'height' => $report['height'] ?? null,
                        'weight' => $report['weight'] ?? null,
                        'check_time' => $checkTime,
                        'pdf_url' => $report['pdf'] ?? null,
                        'health_index' => $report['healthIndex'] ?? null,
                        'constitution_names' => $report['constitutionNames'] ?? null,
                        'symptom_name' => $report['symptomName'] ?? null,
                        'color_of_tongue_names' => $report['colorOfTongueNames'] ?? null,
                        'color_of_moss_names' => $report['colorOfMossNames'] ?? null,
                        'moss_names' => $report['mossNames'] ?? null,
                        'bodyfluid_names' => $report['bodyfluidNames'] ?? null,
                        'shape_of_tongue_names' => $report['shapeOfTongueNames'] ?? null,
                        'vein_names' => $report['veinNames'] ?? null,
                        'disease_risks' => $report['diseaseRisks'] ?? null
                    ];

                    if ($existingReport->isEmpty()) {
                        // 创建新记录
                        $result = HealthCheckReportModel::create($reportData);
                        if ($result->id > 0) {
                            $inserted++;
                        } else {
                            $failed++;
                        }
                    } else {
                        // 更新已存在的记录
                        $result = $existingReport->save($reportData);
                        if ($result) {
                            $updated++;
                        } else {
                            $failed++;
                        }
                    }
                }

                // 提交事务
                Db::commit();

                return [
                    'success' => true,
                    'message' => '同步健康检测报告成功',
                    'total' => $total,
                    'inserted' => $inserted,
                    'updated' => $updated,
                    'failed' => $failed
                ];
            } catch (\Exception $e) {
                // 回滚事务
                Db::rollback();
                Log::error('同步健康检测报告异常: ' . $e->getMessage());

                return [
                    'success' => false,
                    'message' => '同步健康检测报告异常: ' . $e->getMessage(),
                    'total' => $total,
                    'inserted' => 0,
                    'updated' => 0,
                    'failed' => $total
                ];
            }
        } catch (\Exception $e) {
            Log::error('同步健康检测报告异常: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '同步健康检测报告异常: ' . $e->getMessage(),
                'total' => 0,
                'inserted' => 0,
                'updated' => 0,
                'failed' => 0
            ];
        }
    }

    /**
     * 获取会员的健康检测报告列表（游标分页）
     *
     * @param int $memberId 会员ID
     * @param int $limit 每页记录数
     * @param array|null $cursor 游标信息，包含 last_id 和 last_check_time
     * @return array 健康检测报告列表和下一页游标
     */
    public function getMemberHealthReports($memberId, $limit = 10, $cursor = null)
    {
        try {
            $query = HealthCheckReportModel::where('member_id', $memberId)
                ->order('check_time', 'desc')
                ->order('id', 'desc');

            // 添加游标条件
            if ($cursor && isset($cursor['last_check_time']) && isset($cursor['last_id'])) {
                $lastCheckTime = $cursor['last_check_time'];
                $lastId = $cursor['last_id'];

                $query->where(function ($q) use ($lastCheckTime, $lastId) {
                    $q->where('check_time', '<', $lastCheckTime)
                        ->whereOr(function ($q2) use ($lastCheckTime, $lastId) {
                            $q2->where('check_time', '=', $lastCheckTime)
                                ->where('id', '<', $lastId);
                        });
                });
            }

            // 获取数据
            $records = $query->limit($limit)
                ->select()
                ->toArray();

            // 构建下一页游标
            $nextCursor = null;
            if (!empty($records)) {
                $lastItem = end($records);
                $nextCursor = [
                    'last_check_time' => $lastItem['check_time'],
                    'last_id' => $lastItem['id']
                ];
            }

            // 获取总数（可选，如果前端需要显示总数）
            $total = HealthCheckReportModel::where('member_id', $memberId)->count();

            return [
                'list' => $records,
                'total' => $total,
                'next_cursor' => $nextCursor,
                'limit' => $limit,
            ];
        } catch (\Exception $e) {
            // Log the exception
            Log::error('获取会员健康检测报告失败: ' . $e->getMessage());
            return [
                'list' => [],
                'total' => 0,
                'next_cursor' => null,
                'limit' => $limit,
            ];
        }
    }
    /**
     * 下载健康检测报告PDF并保存到系统
     *
     * @param int $reportId 健康检测报告ID，如果为null则处理所有需要下载的报告
     * @return array 下载结果
     */
    public function downloadHealthReportPdf($reportId = null)
    {
        try {
            // 构建查询条件
            $where = [
                ['pdf_url', '<>', ''],
                ['pdf_id', '=', 0]
            ];

            // 如果指定了报告ID，则只处理该报告
            if ($reportId !== null) {
                $where[] = ['id', '=', $reportId];
            }
            // 查询需要下载PDF的报告
            $reports = HealthCheckReportModel::where($where)->select();

            if ($reports->isEmpty()) {
                return [
                    'success' => true,
                    'message' => '没有需要下载的PDF报告',
                    'total' => 0,
                    'success_count' => 0,
                    'failed_count' => 0
                ];
            }

            $total = count($reports);
            $successCount = 0;
            $failedCount = 0;
            $uploadService = new UploadService();

            foreach ($reports as $report) {
                try {
                    // 下载PDF文件
                    $tempFile = $this->downloadPdfFile($report->pdf_url);
                    if ($tempFile) {
                        // 获取文件名
                        $fileName = basename($report->pdf_url);
                        if (empty($fileName)) {
                            $fileName = 'health_report_' . $report->id . '.pdf';
                        } else {
                            // 确保文件名有.pdf后缀
                            $fileExt = pathinfo($fileName, PATHINFO_EXTENSION);
                            if (empty($fileExt)) {
                                $fileName .= '.pdf';
                            } elseif (strtolower($fileExt) !== 'pdf') {
                                $fileName = pathinfo($fileName, PATHINFO_FILENAME) . '.pdf';
                            }
                        }

                        // 上传文件
                        $uploadResult = $uploadService->upload($tempFile, 'file', $fileName);
                        $fileId = isset($uploadResult['file_id']) ? $uploadResult['file_id'] : false;
                        if ($fileId > 0) {
                            // 更新健康检测报告记录
                            $report->pdf_id = $fileId;
                            $report->save();
                            $successCount++;
                        } else {
                            $failedCount++;
                            Log::error('保存健康检测报告PDF文件数据失败: reportId=' . $report->id);
                        }

                        // 删除临时文件
                        if (file_exists($tempFile)) {
                            unlink($tempFile);
                        }
                    } else {
                        $failedCount++;
                        Log::error('下载健康检测报告PDF失败: reportId=' . $report->id . ', url=' . $report->pdf_url);
                    }
                } catch (\Exception $e) {
                    $failedCount++;
                    Log::error('处理健康检测报告PDF异常: ' . $e->getMessage() . ', reportId=' . $report->id);
                }
            }

            return [
                'success' => true,
                'message' => '处理健康检测报告PDF完成',
                'total' => $total,
                'success_count' => $successCount,
                'failed_count' => $failedCount
            ];
        } catch (\Exception $e) {
            Log::error('下载健康检测报告PDF异常: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => '下载健康检测报告PDF异常: ' . $e->getMessage(),
                'total' => 0,
                'success_count' => 0,
                'failed_count' => 0
            ];
        }
    }

    /**
     * 下载PDF文件到临时目录
     *
     * @param string $url PDF文件URL
     * @return string|false 临时文件路径或失败时返回false
     */
    private function downloadPdfFile($url)
    {
        try {
            // 创建临时文件（不带扩展名）
            $tempFile = tempnam(runtime_path(), 'health_report_');

            // 添加.pdf扩展名
            $tempFileWithExt = $tempFile . '.pdf';

            // 如果文件已存在，先删除
            if (file_exists($tempFileWithExt)) {
                unlink($tempFileWithExt);
            }

            // 重命名临时文件，添加扩展名
            rename($tempFile, $tempFileWithExt);

            // 下载文件
            $ch = curl_init($url);
            $fp = fopen($tempFileWithExt, 'wb');

            curl_setopt($ch, CURLOPT_FILE, $fp);
            curl_setopt($ch, CURLOPT_HEADER, 0);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 60);

            curl_exec($ch);

            if (curl_errno($ch)) {
                Log::error('下载PDF文件失败: ' . curl_error($ch));
                curl_close($ch);
                fclose($fp);

                // 清理临时文件
                if (file_exists($tempFileWithExt)) {
                    unlink($tempFileWithExt);
                }

                return false;
            }

            $statusCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            fclose($fp);

            if ($statusCode != 200) {
                Log::error('下载PDF文件失败，HTTP状态码: ' . $statusCode);

                // 清理临时文件
                if (file_exists($tempFileWithExt)) {
                    unlink($tempFileWithExt);
                }

                return false;
            }

            return $tempFileWithExt;
        } catch (\Exception $e) {
            Log::error('下载PDF文件异常: ' . $e->getMessage());

            // 清理临时文件
            if (isset($tempFileWithExt) && file_exists($tempFileWithExt)) {
                unlink($tempFileWithExt);
            }

            return false;
        }
    }
}
