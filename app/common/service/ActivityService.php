<?php

declare(strict_types=1);

namespace app\common\service;

use app\common\model\ActivityModel;
use app\common\model\ActivitySignupModel;
use app\common\model\AuditModel;
use app\common\model\ShopModel;
use app\common\model\UploadFileModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Exception;
use think\facade\Config;
use think\facade\Db;

class ActivityService
{

    /**
     * 创建活动
     * @param $params
     * @return bool
     */
    public function create(array $params): bool
    {
        //获取经纬度
        $baiduApi = (new RegionService())->getLngLatByAddress(($params['province_name'] . $params['city_name'] . $params['address']) ?? '');
        if (isset($baiduApi['location']['lng']) && isset($baiduApi['location']['lat'])) {
            $params['longitude'] = $baiduApi['location']['lng'];
            $params['latitude']  = $baiduApi['location']['lat'];
        }

        //店铺添加，需要审核(audit_status=0);平台添加，不需要审核(audit_status=0)
        $auditStatus = !empty($params['shop_id']) ? 0 : 1; //审核状态
        $status      = !empty($params['shop_id']) ? 10 : 20; //显示状态
        // 关于视频
        $videoId = 0;
        if (isset($params['video_json']) && $params['video_json'] != "") {
            $json = json_decode($params['video_json'], true);
            // 根据$json['original_name'] 获取后缀，截取"."后面的字符串
            $extension = pathinfo($json['original_name'], PATHINFO_EXTENSION); // 获取后缀
            $data = [
                'storage'   => config('upload.uplocation'), //存储位置
                'group_id'  => 0, //分组id
                'file_url'  =>  $json['file_name'] ?? '', //文件url
                'file_name' =>  $json['original_name'] ?? '',
                'file_size' =>  $json['file_size'] ?? '',
                'file_type' =>  'video',
                'extension' =>  $extension,
            ];
            if (!empty($json['file_name'])) {
                $videoId = UploadFileModel::insertGetId($data);
            }
        }
        // 关于视频end

        $addData = [
            'type'           => $params['type'], //活动类型：1=店铺活动，2=平台活动
            'shop_id'        => $params['shop_id'] ?? 0,
            'title'          => $params['title'],
            'cover_image_id' => $params['cover_image_id'],
            'start_date'     => $params['start_date'],
            'start_time'     => $params['start_time'],
            'end_date'       => $params['end_date'],
            'end_time'       => $params['end_time'],
            'province_code'  => $params['province_code'],
            'province_name'  => $params['province_name'],
            'city_code'      => $params['city_code'],
            'city_name'      => $params['city_name'],
            'address'        => $params['address'],
            'longitude'      => $params['longitude'] ?? '', //经度
            'latitude'       => $params['latitude'] ?? '', //纬度
            'content'        => $params['content'],
            'init_join_num'  => $params['init_join_num'] ?? 0,
            'sort'           => $params['sort'] ?? 0,
            'status'         => $status, // 默认10=下架；状态：10=下架，20=上架
            'audit_status'   => $auditStatus,
            'video_id'       => $videoId,
            'lottery_period' => $params['lottery_period'] ?? '', //抽奖活动
        ];

        // 关于门店id(只有平台创建才可以开启多门店报名)
        if ($params['shop_id'] == 0) {
            $addData['signup_shop_mode'] = $params['signup_shop_mode'] ?? 0;
            $addData['signup_shop_ids'] = $params['signup_shop_ids'] ?? '';
        }
        // 关于门店id end

        // 创建活动
        $activity = ActivityModel::create($addData);
        return $activity->id > 0;
    }

    /**
     * 审核活动
     * @throws Exception
     */
    public function audit(int $id, int $status, int $auditUserId, string $remark = ''): bool
    {
        // 开始事务
        Db::startTrans();
        try {
            $activity = ActivityModel::findOrFail($id);
            if ($activity->audit_status != 0) {
                throw new Exception('活动已审核');
            }
            // 更新活动审核状态
            $activity->audit_status  = $status;
            $activity->audit_user_id = $auditUserId;
            $activity->audit_time    = date('Y-m-d H:i:s');
            $activity->audit_remark  = $remark;
            if ($status == 1) {
                // 审核通过自动上架
                $activity->status = 20;
            }
            // 保存活动
            $activity->save();
            // 添加审核流水
            AuditService::addAuditLog($auditUserId, AuditModel::USER_ROLE_TYPE_ADMIN, AuditModel::TYPE_ACTIVITY, $id, $status, $remark);
            // 提交事务
            Db::commit();
            return true;
        } catch (Exception $e) {
            // 回滚事务
            Db::rollback();
            return false;
        }
    }

    /**
     * 获取活动列表 涉及到 业务端、店铺端、平台管理端
     * @param array $param
     * @param string $lastTime
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getList(array $param, string $lastSort = "", string $lastId = ""): array
    {
        //$baseField = "id,type,shop_id,title,cover_image_id,start_date,end_date,start_time,end_time,city_code,city_name,address,content,init_join_num,real_join_num,sort,status";
        $baseField = '*';
        $query     = ActivityModel::field($baseField);
        $query->where('is_delete', 0);
        //业务端
        if (!empty($param['member_id'])) {
            $query->where('status', 20);
            $query->where('audit_status', 1);
            //业务不需要显示的字段
            $query->hidden(['audit_status', 'audit_user_id', 'audit_time', 'audit_remark']);
        }
        //店铺端
        if (!empty($param['shop_id'])) {
            $query->where('shop_id', $param['shop_id']);
        }
        // 按类型筛选
        if (isset($param['type'])) {
            $query->where('type', $param['type']);
        }
        // 按城市筛选
        if (!empty($param['city_code'])) {
            $query->where('city_code', $param['city_code']);
        }
        // 按时间筛选
        if (!empty($param['date'])) {
            $query->where('start_date', '<=', $param['date'])->where('end_date', '>=', $param['date']);
        }
        // 按审核状态筛选
        if (!empty($param['title'])) {
            $query->whereTitle('like', '%' . $param['title'] . '%');
        }
        // 按显示状态筛选
        if (!empty($param['status']) && $param['status'] != '-1') {
            $query->where('status', $param['status']);
        }
        // 按审核状态筛选，只对平台端生效
        if (empty($param['member_id']) && empty($param['shop_id'])) {
            if (isset($param['audit_status']) && $param['audit_status'] != '-1') {
                $query->where('audit_status', $param['audit_status']);
            }
        }
        // 分页
        if (!empty($param['member_id'])) {
            $query->order('sort ASC, id DESC');
            // 用户端滚动分页
            // ((`sort` > {$lastSort}) OR (`sort` = {$lastSort} AND `id` < {$lastId}))
            if ($lastSort !== '' && $lastId !== '') {
                $query->where(function ($q) use ($lastSort, $lastId) {
                    $q->where('sort', '>', $lastSort)
                        ->whereOr(function ($q2) use ($lastSort, $lastId) {
                            $q2->where('sort', '=', $lastSort)
                                ->where('id', '<', $lastId);
                        });
                });
            }
            $list = $query->limit(config('api.limit'))->select();
            //echo $query->getLastSql();
            //exit;
            foreach ($list as $item) {
                $item->shop_name       = ShopModel::where('id', $item->shop_id)->value('shop_name') ?? '';
                $item->cover_image_url = UploadService::get($item->cover_image_id, "600x600");
                $item->join_num        = $item->init_join_num + $item->real_join_num;
                $item->tag_label       = $item->type == 1 ? '店铺活动' : '平台活动';
                $item->is_signup       = ActivitySignupModel::where(['member_id' => $param['member_id'], 'activity_id' => $item->id, 'status' => 1])->count() > 0 ? 1 : 0; //0=未报名；1=已报名
            }
            return $list->toArray();
        } else {
            // 店铺端&平台端分页
            return $query->order('sort ASC, id DESC')->paginate(config('admin.limit'))->each(function ($item, $key) {
                $item->shop_name          = ShopModel::where('id', $item->shop_id)->value('shop_name') ?? '';
                $item->cover_image_url    = UploadService::get($item->cover_image_id, "600x600");
                $item->join_num           = $item->init_join_num + $item->real_join_num;
                $item->tag_label          = $item->type == 1 ? '店铺活动' : '平台活动';
                $item->audit_status_label = self::auditStatusLabel($item->audit_status) ?? '';
            })->toArray();
        }
    }

    /**
     * 活动详情
     * @param array $param
     * @return array
     */
    public function detail(array $param): array
    {
        $baseField = '*';
        $query     = ActivityModel::field($baseField);
        //$query->where('status', 20);
        $query->where('id', $param['id']);
        //业务不需要显示的字段，针对用户端
        //if (!empty($param['member_id'])) {
        //    $query->hidden(['audit_status', 'audit_user_id', 'audit_time', 'audit_remark']);
        //}
        $activity = $query->findOrEmpty();
        if ($activity->isEmpty()) {
            return [];
        }
        $activityStatus = "pending";
        $startTime = strtotime($activity->start_date . " " . $activity->start_time);
        $endTime = strtotime($activity->end_date . " " . $activity->end_time);
        if (time() < $startTime) {
            $activityStatus = "pending";
        } elseif (time() > $endTime) {
            $activityStatus = "finished";
        } else {
            $activityStatus = "running";
        }
        $fids = [$activity->cover_image_id, $activity->video_id];
        $files = UploadService::getByIds($fids, "600x600"); //获取文件信息
        $activity->shop_name          = ShopModel::where('id', $activity->shop_id)->value('shop_name') ?? '';
        $activity->cover_image_url    = $files[$activity->cover_image_id] ?? '';    //封面图
        $activity->video_url          = $files[$activity->video_id] ?? '';           //视频
        $activity->join_num           = $activity->init_join_num + $activity->real_join_num;
        $activity->tag_label          = $activity->type == 1 ? '店铺活动' : '平台活动';
        $activity->audit_status_label = self::auditStatusLabel($activity->audit_status);
        $activity->activity_status = $activityStatus;
        //针对业务端输出字段
        if (!empty($param['member_id'])) {
            // 查询用户是否已报名
            $signup = ActivitySignupModel::where(['member_id' => $param['member_id'], 'activity_id' => $activity->id, 'status' => 1])
                ->with(['shopChoices.shop'])
                ->findOrEmpty();

            $activity->signup = [
                'contact_name' => $signup->contact_name ?? '',
                'contact_mobile' => $signup->contact_mobile ?? '',
                'contact_wechat' => $signup->contact_wechat ?? '',
            ]; //报名信息
            $activity->is_signup = !$signup->isEmpty() ? 1 : 0; //0=未报名；1=已报名

            // 如果已报名，获取用户选择的门店信息
            if ($activity->is_signup) {
                $selectedShops = [];
                if (!empty($signup->shopChoices)) {
                    foreach ($signup->shopChoices as $choice) {
                        $selectedShops[] = [
                            'shop_id' => $choice->shop_id,
                            'shop_name' => $choice->shop->shop_name ?? '',
                        ];
                    }
                }
                $activity->selected_shops = $selectedShops;
            }

            $activity->content = html_entity_decode($activity->content);
        }
        $activity->shops = [];
        if ($activity->signup_shop_mode > 0) {
            //获取门店地址和名称、logo
            $signup_shop_ids = explode(',', $activity->signup_shop_ids);
            $shops = ShopModel::where('id', 'in', $signup_shop_ids)->field('id,shop_name,area_id,address,logo_id')->select()->toArray();
            $logoIds = array_column($shops, 'logo_id');
            $logoFiles = UploadService::getByIds($logoIds); //获取文件信息
            $region = new RegionService();
            foreach ($shops as &$shop) {
                $areas = $region->getRegionDetail($shop['area_id']);
                $address = "";
                if ($areas) {
                    $address = $areas['province_name'] . $areas['city_name'] . $areas['district_name'];
                }
                $shop['address']  = $address . $shop['address']; //门店地址
                $shop['logo_url'] = $logoFiles[$shop['logo_id']] ?? ''; //门店logo
            }


            $activity->shops = $shops; //门店列表
        }
        return $activity->toArray();
    }

    /**
     * 获取活动城市列表
     * @return array
     */
    public function getCityList(): array
    {
        //https://oss.jiangsuyishengai.com/
        $domain   = Config::get('upload.ysa.domain') . 'activity_region/';
        $activity = ActivityModel::with(['activityRegion'])->where(['status' => 20, 'audit_status' => 1])->distinct(true)->field('city_code,city_name')->select();
        if ($activity->isEmpty()) {
            return [];
        }
        foreach ($activity as $item) {
            $item->image_url = $domain . ($item->activityRegion->image ?? '100000.jpg');
            unset($item->activityRegion);
        }
        $result = $activity->toArray();
        //将"全部"新增在数组列表的第一个位置，此"全部"数据不放在activity_region表中维护
        array_unshift($result, ['city_code' => '', 'city_name' => '最新', 'image_url' => $domain . '999999.jpg']);
        return $result;
    }

    /**
     * 添加报名申请
     * @param array $param
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function signup(array $param): bool
    {
        // 检查活动是否存在
        $activity = ActivityModel::where(['status' => 20, 'is_delete' => 0])->find($param['activity_id']);
        if (!$activity) {
            throw new \Exception('活动不存在');
        }
        $startTime = strtotime($activity->start_date . " " . $activity->start_time);
        $endTime = strtotime($activity->end_date . " " . $activity->end_time);
        if (time() < $startTime) {
            throw new \Exception('活动未开始');
        }
        if (time() > $endTime) {
            throw new \Exception('活动已结束');
        }
        $shop_ids = [];
        if ($activity->signup_shop_mode > 0) {
            $signup_shop_ids = explode(',', $activity->signup_shop_ids);
            //门店报名模式, signup_shop_mode=1 单选 signup_shop_mode=2 多选
            if ($activity->signup_shop_mode == 1) {
                if (empty($param['shop_id'])) {
                    throw new \Exception('请选择门店');
                }
                if (!in_array($param['shop_id'], $signup_shop_ids)) {
                    throw new \Exception('该活动只允许在指定门店报名');
                    return false;
                }
                $shop_ids[] = $param['shop_id'];
            }
            if ($activity->signup_shop_mode == 2) {
                if (empty($param['shop_id'])) {
                    throw new \Exception('请选择门店');
                }
                $param['shop_id'] = explode(',', $param['shop_id']);
                foreach ($param['shop_id'] as $shop_id) {
                    if (!in_array($shop_id, $signup_shop_ids)) {
                        throw new \Exception('该活动只允许在指定门店报名');
                        break;
                    }
                    $shop_ids[] = $shop_id;
                }
            }
        }
        // 检查用户是否已报名该活动
        $existingSignup = ActivitySignupModel::where('activity_id', $param['activity_id'])
            ->where('member_id', $param['member_id'])
            ->where('status', 1) // 只检查正常状态的报名
            ->find();

        if ($existingSignup) {
            throw new \Exception('您已报名该活动，不能重复报名');
        }
        try {
            //开启事务
            Db::startTrans();
            // 创建报名记录
            $signup = ActivitySignupModel::create([
                'activity_id'    => $param['activity_id'],
                'member_id'      => $param['member_id'],
                'contact_name'   => $param['contact_name'],
                'contact_mobile' => $param['contact_mobile'],
                'contact_wechat' => $param['contact_wechat'] ?? null,
                'status'         => 1, // 默认状态为正常,状态:1=正常,2=取消
            ]);

            // 保存用户选择的门店信息
            if (!empty($shop_ids)) {
                $shopChoicesData = [];
                foreach ($shop_ids as $shop_id) {
                    $shopChoicesData[] = [
                        'signup_id' => $signup->id,
                        'shop_id' => $shop_id,
                        'create_time' => date('Y-m-d H:i:s')
                    ];
                }
                // 批量插入门店选择记录
                if (!empty($shopChoicesData)) {
                    \app\common\model\ActivitySignupShopChoicesModel::insertAll($shopChoicesData);
                }
            }

            $activity = ActivityModel::find($param['activity_id']);
            $activity->inc('real_join_num')->save();
            Db::commit();
            return $signup->id > 0;
        } catch (\Exception $e) {
            Db::rollBack();
            return false;
        }
    }

    /**
     * 活动报名列表
     * @param array $param
     * @return array
     * @throws \think\db\exception\DbException
     */
    public function signupList(array $param): array
    {
        // 构建查询条件
        $query = ActivitySignupModel::with(['activity', 'member', 'shopChoices.shop']); // 预加载活动、用户和门店选择信息
        // 根据活动ID过滤
        if (!empty($param['activity_id'])) {
            $query->where('activity_id', $param['activity_id']);
        }
        // 根据用户ID过滤
        if (!empty($param['member_id'])) {
            $query->where('member_id', $param['member_id']);
        }
        // 根据状态过滤
        if (isset($param['status'])) {
            $query->where('status', $param['status']);
        }
        // 分页查询
        $result = $query->order('create_time DESC')->paginate(config('admin.limit'))->toArray();
        // 自定义输出字段
        $result['data'] = array_map(function ($signup) {
            // 处理门店选择信息
            $shopChoices = [];
            if (!empty($signup['shop_choices'])) {
                foreach ($signup['shop_choices'] as $choice) {
                    $shopChoices[] = [
                        'shop_id' => $choice['shop_id'],
                        'shop_name' => $choice['shop']['shop_name'] ?? '',
                    ];
                }
            }

            return [
                'id'             => $signup['id'],
                'activity_id'    => $signup['activity_id'],
                'member_id'      => $signup['member_id'],
                'contact_name'   => $signup['contact_name'],
                'contact_mobile' => $signup['contact_mobile'],
                'contact_wechat' => $signup['contact_wechat'],
                'status'         => $signup['status'],
                'create_time'    => $signup['create_time'],
                'update_time'    => $signup['update_time'],
                'activity'       => [
                    'title'      => $signup['activity']['title'],
                    'start_date' => $signup['activity']['start_date'],
                    'start_time' => $signup['activity']['start_time'],
                    'end_date'   => $signup['activity']['end_date'],
                    'end_time'   => $signup['activity']['end_time'],
                ],
                'member'         => [
                    'nickname' => $signup['member']['nickname'] ?? '',
                ],
                'shop_choices'   => $shopChoices,
            ];
        }, $result['data']);
        return $result; // 返回
    }

    /**
     * 活动编辑
     * @param array $param
     * @return bool
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function update(array $param): bool
    {
        // 查找活动
        $activity = ActivityModel::where(['is_delete' => 0])->find($param['id']);
        if (!$activity) {
            throw new \Exception('活动不存在');
        }
        $upLngLat = false;
        if (isset($param['province_code']) && $param['province_code'] != $activity->province_code) {
            $upLngLat = true;
        }
        if (isset($param['city_code']) && $param['city_code'] != $activity->city_code) {
            $upLngLat = true;
        }
        if (isset($param['address']) && $param['address'] != $activity->address) {
            $upLngLat = true;
        }
        if ($upLngLat) {
            //获取经纬度
            $baiduApi = (new RegionService())->getLngLatByAddress(($param['province_name'] . $param['city_name'] . $param['address']) ?? '');
            if (isset($baiduApi['location']['lng']) && isset($baiduApi['location']['lat'])) {
                $param['longitude'] = $baiduApi['location']['lng'];
                $param['latitude']  = $baiduApi['location']['lat'];
            }
        }
        // 更新活动信息
        if (!empty($param['shop_id'])) {
            // 店铺端
            // 更新 status 从上架到下架，则不需要进行审核，从下架到上架，这需要审核
            if (array_key_exists('status', $param) && $activity->status === 20) {
                $param['status'] = 10;
            } else {
                $param['audit_status'] = 0;
            }
        } else {
            //总部端，直接审核通过并上架
            $param['audit_status'] = 1;
            // $param['status']       = 20;
        }
        // 关于视频
        $videoId = 0;
        if (isset($param['video_json']) && $param['video_json'] != "") {
            $json = json_decode($param['video_json'], true);
            // 根据$json['original_name'] 获取后缀，截取"."后面的字符串
            $extension = pathinfo($json['original_name'], PATHINFO_EXTENSION); // 获取后缀
            $data = [
                'storage'   => config('upload.uplocation'), //存储位置
                'group_id'  => 0, //分组id
                'file_url'  =>  $json['file_name'] ?? '', //文件url
                'file_name' =>  $json['original_name'] ?? '',
                'file_size' =>  $json['file_size'] ?? '',
                'file_type' =>  'video',
                'extension' =>  $extension,
            ];
            if (!empty($json['file_name'])) {
                $videoId = UploadFileModel::insertGetId($data);
            }
            $param['video_id'] = $videoId;
        }
        // 关于视频end

        // 关于门店id(只有平台创建才可以开启多门店报名)
        if ($activity->shop_id == 0) {
            if (isset($param['signup_shop_mode'])) {
                $param['signup_shop_mode'] = $param['signup_shop_mode'] ?? 0;
            }
            if (isset($param['signup_shop_ids'])) {
                $param['signup_shop_ids'] = $param['signup_shop_ids'] ?? '';
            }
        }
        // 关于门店id end

        $activity->save($param);
        return $activity->id > 0;
    }

    /**
     * 活动删除
     * @throws ModelNotFoundException
     * @throws DataNotFoundException
     * @throws DbException
     */
    public function softDelete(array $param): bool
    {
        // 查找活动
        $activity = ActivityModel::where(['shop_id' => $param['shop_id'], 'is_delete' => 0])->findOrEmpty($param['id']);
        if ($activity->isEmpty()) {
            throw new \Exception('活动不存在');
        }
        $activity->status = 10; // 下架
        // 设置为软删除状态
        $activity->is_delete = 1;
        return $activity->save() > 0;
    }

    /**
     * 处理数值转名称
     * @param $auditStatus
     * @return string
     */
    public static function auditStatusLabel($auditStatus): string
    {
        switch ($auditStatus) {
            case "0":
                $label = "待审核";
                break;
            case "1":
                $label = "审核通过";
                break;
            case "2":
                $label = "审核不通过";
                break;
            default:
                $label = "";
                break;
        }
        return $label;
    }

    public static function checkStatusById($id = 0, &$msg = ""): bool
    {
        if ($id == 0) {
            $msg = '活动id不能为空';
            return false;
        }
        // 查找活动
        $activity = ActivityModel::field('id,start_date,end_date,start_time,end_time,status,audit_status,is_delete')->find($id);
        if (!$activity) {
            $msg = '活动不存在';
            return false;
        }
        $startTime = strtotime($activity->start_date . " " . $activity->start_time);
        $endTime = strtotime($activity->end_date . " " . $activity->end_time);
        if (time() < $startTime) {
            $msg = '活动未开始';
            return false;
        }
        if (time() > $endTime) {
            $msg = '活动已结束';
            return false;
        }
        if ($activity->audit_status != 1) {
            $msg = '活动未审核通过';
            return false;
        }
        if ($activity->is_delete == 1) {
            $msg = '活动已删除';
            return false;
        }
        if ($activity->status != 20) {
            $msg = '活动已下架';
            return false;
        }
        return true;
    }
}
