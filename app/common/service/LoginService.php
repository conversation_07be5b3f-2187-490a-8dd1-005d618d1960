<?php

declare(strict_types=1);

namespace app\common\service;

use app\common\enum\Response;
use app\common\model\AdminModel;
use app\common\model\AuthAccessTokenModel;
use app\common\model\StaffModel;
use think\facade\Cache;
use think\facade\Request;
use think\facade\Event;

class LoginService
{
    /**
     * 设置登录
     * @param string $role
     * @param string $token
     * @return bool
     */
    public function setLogin(string $role = 'admin', string $token = '', array $userinfo = []): bool
    {
        $result = false;
        if (!empty($token)) {
            $userinfo['token'] = $token;

            // 过期时间
            $expires_in = 3600 * 24 * 30;
            Cache::set($role . '_token:' . $token, $userinfo['id'], $expires_in);
            Cache::set($role . '_auth:' . $userinfo['id'], $userinfo, $expires_in);
            $result = true;
        }
        return $result;
    }

    /**
     * 管理员登录
     * @param array $param
     * @return array
     */
    public function AdminLogin(array $param = []): array
    {

        $adminData = AdminModel::where([
            'username' => $param['username'],
            'password' => makePassword($param['password']),
        ])->field('id,shop_id,username,status,last_login_ip,last_login_time,is_super')->find();

        if (empty($adminData)) {
            return fail(Response::ACCOUNT_PASSWORD_ERROR);
        }
        $shop = $adminData->shop;
        if (empty($shop)) {
            return fail(Response::ACCOUNT_EXPIRE);
        }

        if ($adminData['status'] != 1) {
            return fail(Response::ACCOUNT_FORBIDDEN);
        }
        $adminData->groups = $adminData->group;
        $power             = new AdminPowerService();
        $adminRole         = $power->getAdminRole($adminData->toArray(), true);
        if (empty($adminRole)) {
            return fail(Response::NOT_PERMISSION);
        }

        $role  = 'admin'; //这个是指哪个应用登录, 跟token走
        $token = $this->createToken($role, $adminData['id']);
        if (empty($token)) {
            return fail(Response::TOKEN_CREATE_ERROR);
        }
        //保存登录信息
        $this->setLogin($role, $token['token'], $adminData->toArray());
        //更新登录信息
        AdminModel::where('id', $adminData['id'])->update([
            'last_login_time' => time(),
            'last_login_ip'   => Request::ip(),
        ]);
        //登录日志
        Event::trigger('AdminLog', ['admin_id' => $adminData['id'], 'shop_id' => $adminData['shop_id'], 'remark' => '后台登录']);
        return success($token);
    }

    /**
     * 服务人员登录
     */
    public function staffLogin(array $param = []): array
    {

        $staffData = StaffModel::where([
            'mobile'   => $param['mobile'],
            'password' => makePassword($param['passwd']),
        ])->field('id,shop_id,mobile,real_name,status,last_login_ip,last_login_time')->find();

        if (empty($staffData)) {
            return fail(Response::ACCOUNT_PASSWORD_ERROR);
        }

        $role  = 'staff';
        $token = $this->createToken($role, $staffData['id']);
        if (empty($token)) {
            return fail(Response::TOKEN_CREATE_ERROR);
        }
        //保存登录信息
        $this->setLogin($role, $token['token'], $staffData->toArray());
        //更新登录信息
        StaffModel::where('id', $staffData['id'])->update([
            'last_login_time' => time(),
            'last_login_ip'   => Request::ip(),
        ]);
        return success($token);
    }

    /**
     * 服务人员register
     */
    public function staffRegister(array $param = []): array
    {
        $data = [
            'shop_id'       => $param['shop_id'],
            'mobile'        => $param['mobile'],
            'password'      => makePassword($param['passwd']),
            'register_time' => time(),
        ];

        $insertId = StaffModel::insertGetId($data);
        if ($insertId <= 0) {
            return fail(Response::REGISTER_FAIL);
        }
        // 自动登录
        $staffData = StaffModel::where('id', $insertId)
            ->field('id,shop_id,mobile,real_name,status,last_login_ip,last_login_time')
            ->find();

        $role  = 'staff';
        $token = $this->createToken($role, $staffData['id']);
        if (empty($token)) {
            return fail(Response::TOKEN_CREATE_ERROR);
        }
        //保存登录信息
        $this->setLogin($role, $token['token'], $staffData->toArray());
        //更新登录信息
        StaffModel::where('id', $staffData['id'])->update([
            'last_login_time' => time(),
            'last_login_ip'   => Request::ip(),
        ]);
        return success($token);
    }

    // 修改密码
    public function changePassword(array $param = []): array
    {
        $data = [
            'password'    => makePassword($param['password']),
            'update_time' => time(),
        ];
        $res  = StaffModel::where('mobile', $param['mobile'])->update($data);
        if (!$res) {
            return fail(Response::CHANGE_PASSWORD_FAIL);
        }
        return success();
    }

    // 重置密码
    public function resetPassword(array $param = []): array
    {
        // 验证旧密码
        $adminData = AdminModel::where([
            'id' => $param['admin_id'],
            'password' => makePassword($param['old_passwd']),
        ])->field('id')->find();
        if (empty($adminData)) {
            return fail(Response::ACCOUNT_PASSWORD_ERROR, '密码错误');
        }
        $data = [
            'password'    => makePassword($param['new_passwd']),
        ];
        $res  = AdminModel::where('id', $param['admin_id'])->update($data);
        if (!$res) {
            return fail(Response::CHANGE_PASSWORD_FAIL);
        }
        return success();
    }

    // token
    public function createToken(string $role = 'admin', int $uid = 0): array
    {
        $retData = [
            "uid" => $uid,
            "token"      => generateAccessToken(),
            "expires_in" => 3600 * 24 * 30, //30天
            "token_type" => 'Bearer',
            "scope"      => 'custom default protected',
        ];

        $param    = [
            'token'      => $retData['token'],
            'login_role' => $role,
            'login_id'   => $uid,
            'expires'    => time() + $retData['expires_in'],
            'status'     => 1,
        ];
        $insertId = AuthAccessTokenModel::strict(false)->insertGetId($param);
        if ($insertId) {
            return $retData;
        }
        return [];
    }

    /**
     * 退出
     * @param string $role
     * @param int $id
     * @param string $token
     * @return bool
     */
    public function loginOut(string $role = 'user', $id = 0, $token = ''): bool
    {
        if (empty($id) && $token) {
            // 通过token查id
            $id = Cache::get($role . '_token:' . $token);
        }
        if (empty($token) && $id > 0) {
            // 通过id查token
            $info  = Cache::get($role . '_auth:' . $id);
            $token = $info['token'];
        }
        Cache::delete($role . '_token:' . $token);
        Cache::delete($role . '_auth:' . $id);
        Cache::delete($role . '_role:' . $id);
        return true;
    }
}
