<?php

declare(strict_types=1);

namespace app\common\service;

use app\common\enum\RedisKey;
use app\common\enum\Response;
use app\common\enum\TagType;
use app\common\model\ContractGoodsRelationModel;
use app\common\model\ContractTemplateModel;
use app\common\model\ExtraFeeModel;
use app\common\model\GoodsCategoryBasicModel;
use app\common\model\GoodsModel;
use app\common\model\GoodsCategoryModel;
use app\common\model\GoodsImageModel;
use app\common\model\GoodsSkuModel;
use app\common\model\ServiceZoneModel;
use app\common\model\ShopModel;
use Psr\SimpleCache\InvalidArgumentException;
use think\db\exception\DbException;
use think\Exception;
use think\facade\Cache;
use think\facade\Db;
use think\facade\Event;
use think\facade\Config;
use think\response\Json;

/**
 * 商品服务类
 * Class Goods
 * @package app\store\service
 */
class GoodsService
{

    private GoodsModel $goodsModel;
    private GoodsCategoryModel $goodsCategoryModel;
    private ServiceZoneModel $serviceZoneModel;
    private GoodsImageModel $goodsImageModel;
    private GoodsSkuModel $goodsSkuModel;
    private GoodsCategoryBasicModel $goodsCategoryBasicModel;
    private mixed $cacheSwitch;
    private mixed $timeout;
    /**
     * 商品审核标记-false 不需要审核；true-需要审核
     * @var false
     */
    private bool $auditFlag = false;

    public function __construct()
    {
        $this->redisConfig();
        $this->goodsModel              = new GoodsModel();
        $this->goodsCategoryModel      = new GoodsCategoryModel();
        $this->serviceZoneModel        = new ServiceZoneModel();
        $this->goodsImageModel         = new GoodsImageModel();
        $this->goodsSkuModel           = new GoodsSkuModel();
        $this->goodsCategoryBasicModel = new GoodsCategoryBasicModel();
    }

    /**
     * 商品列表页
     * @param $shopId
     * @return array
     * @throws \think\exception\DbException
     * @throws DbException
     */
    public function goodsIndex($shopId, $searchData): array
    {
        $where[] = ['is_delete', '=', '0'];
        $order   = 'audit_status asc,create_time desc,update_time desc'; //平台管理后台排序规则
        if (!empty($shopId)) {
            $where[] = ['shop_id', '=', $shopId];
            $order   = 'goods_sort asc,goods_id desc'; //店铺端管理后台排序规则
        }
        if (isset($searchData['goods_name']) && $searchData['goods_name'] != '') {
            $where[] = ['goods_name', 'like', '%' . $searchData['goods_name'] . '%'];
        }
        if (isset($searchData['goods_status']) && $searchData['goods_status'] != '') {
            $where[] = ['goods_status', '=', $searchData['goods_status']];
        }
        if (isset($searchData['audit_status']) && $searchData['audit_status'] != '') {
            $where[] = ['audit_status', '=', $searchData['audit_status']];
        }
        $field = 'shop_id,goods_id,goods_name,goods_sort,goods_status,audit_status,category_id,audit_status,sales_actual,create_time,update_time';
        $goodsList = $this->goodsModel->where($where)->field($field)->order($order)->paginate(config('admin.limit'))->each(function ($item) {
            $item->category_name  = $this->goodsCategoryModel->where([['c_id', '=', $item->category_id], ['shop_id', '=', $item->shop_id]])->value('third_category_unique_name') ?? "/";
            $goodsStatus['value'] = $item->goods_status;
            if ($item->goods_status == '10') {
                $goodsStatus['text'] = '上架';
            } else {
                $goodsStatus['text'] = '下架';
            }
            $item->goods_status   = $goodsStatus;
            $auditStatus['value'] = $item->audit_status;
            if ($item->audit_status == '1') {
                $auditStatus['text'] = '审核通过';
            } else if ($item->audit_status == '2') {
                $auditStatus['text'] = '审核不通过';
            } else {
                $auditStatus['text'] = '待审核';
            }
            $item->audit_status = $auditStatus;
            $item->shop_name    = ShopModel::where(['id' => $item->shop_id])->value('shop_name') ?? '/';
            unset($item->shop_id, $item->category_id);
        })->toArray();
        $goodsId = array_column($goodsList['data'], 'goods_id');

        // 查看是否绑定合同
        $rel = ContractGoodsRelationModel::where('goods_id', 'in', $goodsId)->where('is_active', 1)->field('goods_id,template_id')->select()->toArray();
        $rel = array_column($rel, 'template_id', 'goods_id');
        foreach ($goodsList['data'] as $k => $v) {
            $templateId = $rel[$v['goods_id']] ?? 0;
            $goodsList['data'][$k]['need_contract'] =  $templateId > 0 ? 1 : 0;
            $goodsList['data'][$k]['contract_template_id'] = $rel[$v['goods_id']] ?? 0;
        }
        return $goodsList;
    }

    /**
     * 添加商品
     * @param $requestData
     * @return array|string[]
     */
    public function createGoods($requestData): array
    {
        //涉及表：goods、goods_image、goods_sku
        //开启事务
        Db::startTrans();
        try {
            $goodsId = $this->handleInsertGoods($requestData);
            $this->handleInsertGoodsImage($requestData, $goodsId);
            $this->handleInsertGoodsSku($requestData, $goodsId);
            Db::commit();
            return ["商品添加成功"];
        } catch (Exception $e) {
            $error = $e->getMessage();
            Db::rollBack();
            return [$error];
        }
    }

    /**
     * 通过商品ID获取详情
     * @param $goodsId
     * @param $shopId 0-则表示不区分店铺（平台管理后台使用）
     * @return array
     * @throws Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     * @throws InvalidArgumentException
     */
    public function goodsDetails($goodsId, $shopId = 0): array
    {
        $cacheKey    = str_replace(['{shopId}', '{goodsId}'], [$shopId, $goodsId], RedisKey::GOODS_SHOPID_GOODSID);
        $cacheResult = Cache::store('redis')->get($cacheKey);
        if (!empty($cacheResult)) {
            return $cacheResult;
        }
        $where[] = ['is_delete', '=', 0];
        if (!empty($shopId)) {
            $where[] = ['shop_id', '=', $shopId];
        }
        $goods = $this->goodsModel->where($where)->findOrEmpty($goodsId);
        if (!empty($goods)) {
            $goods->category_name     = $this->goodsCategoryModel->where([['c_id', '=', $goods->category_id], ['shop_id', '=', $goods->shop_id]])->value('third_category_unique_name') ?? "/";
            $goods_cover_image['id']  = $goods->cover_image_id;
            $goods_cover_image['url'] = formatGoodsCover(UploadService::get($goods->cover_image_id));
            $goods->goods_cover_image = $goods_cover_image;
            //unset($goods->cover_image_id);
            $goods->goods_sku = $goods->sku()->select();

            //图片ID、图片全路径
            if (!empty($goods->goods_sku->toArray())) {
                foreach ($goods->goods_sku as $key => $value) {
                    $goodsSkuImage['id']                     = $value->sku_image_id;
                    $goodsSkuImage['url']                    = formatGoodsCover(UploadService::get($value->sku_image_id));
                    $goods->goods_sku[$key]->goods_sku_image = $goodsSkuImage;
                    $cacheKeyFreezeStock                     = 'ysa:freeze:' . $goodsId;
                    // Redis中获取冻结库存数量,Hash数据类型
                    $redis                                = Cache::store('redis')->handler();
                    $goods->goods_sku[$key]->freeze_stock = $redis->hGet($cacheKeyFreezeStock, (string)$value['goods_sku_id']) ?? 0;
                }
            }
            //处理商品评论标签、商品认证标签
            if (!empty($goods->goods_comment_tags)) {
                $goods->goods_comment_tags = TagService::getByIds(explode(',', $goods->goods_comment_tags), 2);
            }
            if (!empty($goods->goods_auth_tags)) {
                $goods->goods_auth_tags = TagService::getByIds(explode(',', $goods->goods_auth_tags), 2);
            }

            $goodsImageList = $goods->image()->select();
            //图片ID、图片全路径
            if (!empty($goodsImageList->toArray())) {
                foreach ($goodsImageList as $image) {
                    $goodsImage['id']   = $image->image_id;
                    $goodsImage['url']  = formatGoodsCover(UploadService::get($image->image_id));
                    $returnGoodsImage[] = $goodsImage;
                    $goods->goods_image = $returnGoodsImage;
                }
            } else {
                $goods->goods_image = [];
            }
            $result = $goods->toArray();
            if ($this->cacheSwitch) {
                Cache::store('redis')->set($cacheKey, $result, $this->timeout);
            }
            return $result;
        }
        return $goods;
    }

    /**
     * 通过商品ID更新详情
     * @param $requestData
     * @param $shopId
     * @return bool|int|string
     * @throws PDOException
     */
    public function updateGoods($requestData, $shopId): Json
    {
        $goods = $this->goodsDetails($requestData['goods_id'], $shopId);
        if (empty($goods)) {
            //goodsId不存在
            return json(fail(Response::GOODS_ID_NOT_EXIST));
        }
        $goods['goods_cover_image_id'] = $goods['cover_image_id'];
        //开启事务
        Db::startTrans();
        try {
            $this->handleUpdateGoods($requestData, $goods);
            $this->handleUpdateGoodsImage($requestData, $goods);
            $this->handleUpdateGoodsSku($requestData);
            if ($this->auditFlag) {
                $this->resetAudit((int)$requestData['goods_id']);
            }
            Db::commit();
            $cacheKey = str_replace(['{shopId}', '{goodsId}'], [$shopId, $requestData['goods_id']], RedisKey::GOODS_SHOPID_GOODSID);
            Cache::store('redis')->delete($cacheKey);
            return json(success(["更新成功"]));
        } catch (Exception $e) {
            $this->goodsImageModel->rollback();
            $this->goodsModel->rollback();
            $this->goodsSkuModel->rollback();
            return json(fail(Response::DATA_NOT_FOUND, $e->getMessage()));
        }
    }

    /**
     * 通过商品ID软删除商品，goods_images、goods_sku表不做处理
     * @param $goodsId
     * @param $shopId
     * @return false|int
     * @throws InvalidArgumentException
     */
    public function deleteGoods($goodsId, $shopId): bool
    {
        //软删除商品，需要额外更新goods_status=20(下架)、update_time
        $goods = $this->goodsModel;
        $goods->where(['goods_id' => $goodsId, 'shop_id' => $shopId, 'is_delete' => '0'])->update(['is_delete' => 1, 'goods_status' => '20', 'update_time' => time()]);
        $cacheKey = str_replace(['{shopId}', '{goodsId}'], [$shopId, $goodsId], RedisKey::GOODS_SHOPID_GOODSID);
        Cache::store('redis')->delete($cacheKey);
        return true;
    }

    /**
     * 商品表数据添加
     * @param $requestData
     * @return int|string
     */
    private function handleInsertGoods($requestData): int
    {
        //商品表
        $goodsData['shop_id']        = $requestData['shop_id']; //店铺id
        $goodsData['goods_name']     = $requestData['goods_name']; //商品名称
        $goodsData['cover_image_id'] = $requestData['goods_cover_image_id'] ?? ''; //商品封面图片id
        $goodsData['selling_point']  = $requestData['selling_point'] ?? ''; //商品卖点
        $goodsData['category_id']    = $requestData['category_id'] ?? ''; //商品分类id
        //$goodsData['clark_type_id']  = $requestData['clark_type_id'] ?? ''; //技师类别ID
        $goodsData['content']       = htmlspecialchars_decode($requestData['content']) ?? ''; //商品详情
        $goodsData['sales_initial'] = $requestData['sales_initial'] ?? ''; //初始销量
        //$goodsData['sales_actual'] = $requestData['sales_actual'];//实际销量
        $goodsData['goods_sort']          = $requestData['goods_sort'] ?? ''; //商品排序(数字越小越靠前)
        $goodsData['is_enable_grade']     = $requestData['is_enable_grade'] ?? ''; //是否开启会员折扣(1开启 0关闭)
        $goodsData['is_alone_grade']      = $requestData['is_alone_grade'] ?? ''; //会员折扣设置(0默认等级折扣 1单独设置折扣)
        $goodsData['alone_grade_equity']  = $requestData['alone_grade_equity'] ?? ''; //单独设置折扣的配置
        $goodsData['goods_status']        = '20'; //添加商品时，默认商品为下架状态；商品状态(10上架 20下架)
        $goodsData['goods_tags']          = $requestData['goods_tags'] ?? ''; //商品标签
        $goodsData['is_recommend']        = ($requestData['is_recommend']) ?? ''; //是否推荐到首页10是20否
        $goodsData['max_buy']             = $requestData['max_buy'] ?? ''; //最大购买数量
        $goodsData['min_buy']             = $requestData['min_buy'] ?? ''; //最小购买数量
        if (isset($requestData['festival_pricing_rules']) && $requestData['festival_pricing_rules'] != "") {
            $goodsData['festival_pricing_rules']  = $requestData['festival_pricing_rules']; //特殊节日收费模板id
        }
        if (isset($requestData['extra_data']) && $requestData['extra_data'] != "") {
            $goodsData['extra_data'] = $requestData['extra_data']; //其他收费项目
        }
        $goodsData['unit']                = $requestData['unit'] ?? ''; //服务时长单位
        $goodsData['service_content']     = htmlspecialchars_decode($requestData['service_content'] ?? ''); //服务详情
        $goodsData['service_mode']        = $requestData['service_mode'] ?? ''; //服务方式，1-上门；2-到店
        $goodsData['is_package_service']  = $requestData['is_package_service'] ?? ''; //是否是套餐服务，0-不是；1-是
        $goodsData['package_service_num'] = $requestData['package_service_num'] ?? ''; //套餐服务包含次数  is_package_service=1时有效
        $goodsData['interview_mode']      = $requestData['interview_mode'] ?? ''; //面试设置，1-先面试后付款；2-先付款后面试
        $goodsData['is_deposit']          = $requestData['is_deposit'] ?? ''; //是否有定金；0-无；1-有
        $goodsData['goods_comment_tags']  = htmlspecialchars_decode($requestData['goods_comment_tags'] ?? ''); //商品评论标签
        $goodsData['service_num']         = $requestData['service_num'] ?? ''; //已服务次数
        $goodsData['staff_lock_days']     = $requestData['staff_lock_days'] ?? ''; //预留时间（从业人员锁定天数），单位天，范围0-30
        $goodsData['create_time']         = time();
        $goodsData['update_time']         = time();
        //operation_type = 2 (营养膳食) 时添加以下字段
        //first_category_id =1 为 operation_type = 2 (营养膳食)，否则operation_type = 1 (非营养膳食)
        $goodsData['operation_type']    = $requestData['first_category_id'] == 1 ? 2 : 1; //板块类型 1-非营养膳食；2-营养膳食
        $goodsData['other_fee']         = $requestData['other_fee'] ?? ''; //杂费
        $goodsData['base_service_time'] = $requestData['base_service_time'] ?? ''; //基础服务时长（单位：小时）
        $goodsData['cook_mode']         = $requestData['cook_mode'] ?? ''; //制作方式 ,1-做好送上门；2-上门制作
        $goodsData['refund_rule']       = $requestData['refund_rule'] ?? ''; //退款规则说明
        $goodsData['final_payment_type'] = $requestData['final_payment_type'] ?? 1; //尾款支付类型：1=服务开始前支付；2=服务完成前支付
        $goodsData['final_payment_days']         = $requestData['final_payment_days'] ?? 0; //尾款支付天数(相对于服务开始或结束的天数)
        $goodsData['first_category_id'] = $requestData['first_category_id'] ?? 0; //一级分类ID,goods_category.first_category_id
        return $this->goodsModel->insertGetId($goodsData);
    }

    /**
     * 商品商品图片表数据添加
     * @param $requestData
     * @param $goodsId
     * @return bool
     */
    private function handleInsertGoodsImage($requestData, $goodsId): bool
    {
        //商品图片，批量添加  goods_id,image_id
        if (isset($requestData['goods_image_ids']) & !empty($requestData['goods_image_ids'])) {
            $goodsImageIdsArr = explode(',', $requestData['goods_image_ids']);
            $goodsImageData   = [];
            foreach ($goodsImageIdsArr as $key => $value) {
                $goodsImageData[$key]['goods_id']    = $goodsId;
                $goodsImageData[$key]['image_id']    = $value;
                $goodsImageData[$key]['create_time'] = time();
            }
            return $this->goodsImageModel->insertAll($goodsImageData) > 0;
        }
        return true;
    }

    /**
     * 商品sku表数据添加
     * @param $requestData
     * @param $goodsId
     * @return bool
     */
    private function handleInsertGoodsSku($requestData, $goodsId): bool
    {
        if (isset($requestData['goods_sku']) & !empty($requestData['goods_sku'])) {
            //goods_sku 字段存在，且不为空值，则执行
            //商品sku表,添加
            $goodsSkuData = [];
            $stockData    = []; //存入缓存中 event SaveStock
            $goodsSkuArr  = json_decode(htmlspecialchars_decode($requestData['goods_sku']), true);
            foreach ($goodsSkuArr as $value) {
                $goodsSkuData['goods_sku_id']   = $value['goods_sku_id'] ?? ''; //商品规格id
                $goodsSkuData['goods_id']       = $goodsId; //商品ID
                $goodsSkuData['sku_image_id']   = $value['image_id'] ?? ''; //图片ID
                $goodsSkuData['goods_no']       = $value['goods_no'] ?? ''; //商品编码
                $goodsSkuData['goods_price']    = $value['goods_price'] ?? ''; //商品价格
                $goodsSkuData['line_price']     = $value['line_price'] ?? ''; //商品划线价
                $goodsSkuData['stock_num']      = $value['stock_num'] ?? ''; //当前库存数量
                $goodsSkuData['goods_weight']   = $value['goods_weight'] ?? ''; //服务次数
                $goodsSkuData['deposit']        = $value['deposit'] ?? ''; //定金
                $goodsSkuData['goods_time']     = $value['goods_time'] ?? ''; //服务时长
                $goodsSkuData['unit']           = $requestData['unit'] ?? ''; //服务时长单位
                $goodsSkuData['first_dealer']   = $value['first_dealer'] ?? ''; //一级分销提成
                $goodsSkuData['second_dealer']  = $value['second_dealer'] ?? ''; //二级分销提成
                $goodsSkuData['clerk_money']    = $value['clerk_money'] ?? ''; //技师工资
                $goodsSkuData['partner_money']  = $value['partner_money'] ?? ''; //代理提成
                $goodsSkuData['shop_money']     = $value['shop_money'] ?? ''; //门店提成
                $goodsSkuData['platform_money'] = $value['platform_money'] ?? ''; //平台提成
                $goodsSkuData['zone_id']        = $value['zone_id'] ?? ''; //区域id
                $goodsSkuData['create_time']    = time();
                $goodsSkuData['update_time']    = time();
                //operation_type = 2 (营养膳食) 时添加以下字段
                $goodsSkuData['cook_name']   = $value['cook_name'] ?? ''; //商品名称
                $goodsSkuData['make_time']   = $value['make_time'] ?? ''; //制作时长(分钟)
                $goodsSkuData['food_fee']    = $value['food_fee'] ?? ''; //食材费用
                $goodsSkuData['cook_fee']    = $value['cook_fee'] ?? ''; //加工费用
                $goodsSkuData['cook_status'] = $value['cook_status'] ?? ''; //商品状态(10上架 20下架)
                $goodsSkuData['materials']   = $value['materials'] ?? ''; //食材及配料
                $goodsSkuData['cook_sort']   = $value['cook_sort'] ?? ''; //商品排序
                if (empty($goodsSkuData['goods_sku_id'])) {
                    $goodsSkuId = $this->goodsSkuModel->insertGetId($goodsSkuData);
                    //第一次新增库存时,为页面提交过来的库存数量
                    $changedStocks = $goodsSkuData['stock_num'];
                } else {
                    $dbStocks = $this->goodsSkuModel->where(['goods_sku_id' => $goodsSkuData['goods_sku_id']])->value('stock_num') ?? 0;
                    $this->goodsSkuModel->update($goodsSkuData);
                    //处理重新审核逻辑
                    $this->auditFlag = true;
                    //修改库存时,$changedStocks = DB库存数量-页面提交过来的库存数量
                    $goodsSkuId = $goodsSkuData['goods_sku_id'];
                    //改变库存数量，为正数（例如传值：8），为负数（例如传值：-8）
                    //stock取值规则：A(修改前库存数量)+/-B = C(修改后库存数量[入库库存数量])，即：stock值 =（+/-B）= C-A
                    $changedStocks = $goodsSkuData['stock_num'] - $dbStocks;
                }
                //(double)$changedStocks此次需要类型转换为double，否则报错
                $stockData[] = ['goods_id' => (string)$goodsId, 'goods_sku_id' => (string)$goodsSkuId, 'stock' => (float)$changedStocks];
            }
            //维护库存数量，用于下单业务逻辑，app\common\event\SaveStock.handle()
            Event::trigger('SaveStock', $stockData);
        }
        return true;
    }

    /**
     * 获取商品添加配置列表
     * @param $shopId
     * @return array
     * @throws DbException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function goodsAddConfigItems($shopId): array
    {
        //读取缓存，暂时关闭缓存，待平台管理后台添加缓存后再开启
        /* $cacheKey    = "goodsAddConfigItems:" . $shopId;
        $cacheResult = Cache::store('redis')->get($cacheKey);
        if (!empty($cacheResult)) {
            return $cacheResult;
        }*/
        //商品分类列表 first_category_list-已经审批审核通过的一级分类列表；goods_category_list-已经审批审核通过的三级分类列表
        $goodsCategory       = GoodsCategoryService::getThirdCategoryList($shopId);
        $first_category_list = $goodsCategory['first_category_list'];
        $goods_category_list = $goodsCategory['goods_category_list'];
        //服务区域 serviceZoneList
        $service_zone_list = $this->serviceZoneModel->field('district_id as id')->where(['shop_id' => $shopId])->select();
        if (!$service_zone_list->isEmpty()) {
            $regionService = new RegionService();
            foreach ($service_zone_list as $service_zone) {
                $region             = $regionService->getRegionDetail($service_zone->id);
                $service_zone->name = $region['name'] ?? '';
            }
            $service_zone_list = $service_zone_list->toArray();
        }
        //收费日期 extra_fee
        $extra          = new ExtraFeeModel();
        $extraFeeList   = $extra->getFee();
        $extra_fee_list = [];
        if (!empty($extraFeeList)) {
            foreach ($extraFeeList as $key => $value) {
                $extra_fee_list[$key]['id']   = $value['id'];
                $extra_fee_list[$key]['name'] = $value['describe'] . "（" . $value['service_date'] . "）";
            }
        }
        //商品评价标签 goods_tag
        $goodsTagList   = TagService::getById(TagType::PRODUCT_COMMENT);
        $goods_tag_list = [];
        if (!empty($goodsTagList)) {
            foreach ($goodsTagList as $key => $value) {
                $goods_tag_list[$key]['id']   = $value['id'];
                $goods_tag_list[$key]['name'] = $value['name'];
            }
        }
        $result = compact('first_category_list', 'goods_category_list', 'service_zone_list', 'goods_tag_list', 'extra_fee_list');
        //写入缓存
        //Cache::store('redis')->set($cacheKey, $result, 3600);
        return $result;
    }

    /**
     * 商品表数据更新
     * @param $requestData
     * @param $shopId
     * @return bool
     * @throws Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    private function handleUpdateGoods($requestData, $goods): bool
    {
        $goodsId          = $requestData['goods_id'];
        $goodsTableFields = ['goods_name', 'goods_cover_image_id', 'selling_point', 'category_id', 'content', 'sales_initial', 'sales_actual', 'goods_sort', 'is_enable_grade', 'is_alone_grade', 'alone_grade_equity', 'goods_status', 'goods_tags', 'is_recommend', 'max_buy', 'min_buy', 'extra_ids', 'extra_data', 'unit', 'service_content', 'service_mode', 'is_package_service', 'package_service_num', 'interview_mode', 'is_deposit', 'goods_comment_tags', 'service_num', 'staff_lock_days', 'other_fee', 'base_service_time', 'cook_mode', 'refund_rule', 'first_category_id', 'final_payment_type', 'final_payment_days', 'festival_pricing_rules'];
        for ($i = 0; $i < count($goodsTableFields); $i++) {
            //数据相同，则不需要更新
            if (isset($requestData[$goodsTableFields[$i]]) && ($goods[$goodsTableFields[$i]] != $requestData[$goodsTableFields[$i]])) {
                // if ($goodsTableFields[$i] == 'extra_data' || $goodsTableFields[$i] == 'festival_pricing_rules') {
                //     $updateData[$goodsTableFields[$i]] = json_decode($requestData[$goodsTableFields[$i]], true);
                //     $updateData[$goodsTableFields[$i]] = json_encode($updateData[$goodsTableFields[$i]]);
                // } else
                if ($goodsTableFields[$i] == 'goods_cover_image_id') {
                    $updateData['cover_image_id'] = $requestData[$goodsTableFields[$i]];
                    unset($requestData[$goodsTableFields[$i]]);
                } else if ($goodsTableFields[$i] == 'first_category_id') {
                    $updateData['operation_type']    = $requestData[$goodsTableFields[$i]] == 1 ? 2 : 1; //板块类型 1-非营养膳食；2-营养膳食
                    $updateData['first_category_id'] = $requestData[$goodsTableFields[$i]]; //板块类型 1-非营养膳食；2-营养膳食
                } else {
                    $updateData[$goodsTableFields[$i]] = $requestData[$goodsTableFields[$i]]; //一级分类ID,goods_category.first_category_id
                }
            }
        }
        if (!empty($updateData)) {
            //只更新这些字段，则不需要进行审核
            $notUpdateField = ['goods_status']; //维护此数组中的字段名称，来源与$goodsTableFields
            $updateDataKeys = array_keys($updateData);
            //$notUpdateField排序后array和 $updateDataKeys排序后array相等，更新不需要审核，否则更新需要审核
            sort($notUpdateField);
            sort($updateDataKeys);
            if (!($notUpdateField === $updateDataKeys)) {
                //走需要审核流程
                //处理重新审核逻辑
                $this->auditFlag = true;
            }
            //走不需要审核流程
            $updateData['update_time'] = time();
            //更新信息
            return $this->goodsModel->where(['goods_id' => $goodsId])->update($updateData) > 0;
        }
        return true;
    }

    /**
     * 商品商品图片表数据更新
     * @param $requestData
     * @param $shopId
     * @return bool
     * @throws Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function handleUpdateGoodsImage($requestData, $goods): bool
    {
        $goodsId = $requestData['goods_id'];
        if (!array_key_exists('goods_image_ids', $requestData)) {
            //如果goods_image字段不存在，则不需要进行商品图片维护，代码不需要进行向下执行
            return true;
        }
        $goodsImageListArr = $goods['goods_image'] ?? ""; //from DB,list
        $goodsImageIdsStr  = $requestData['goods_image_ids'] ?? ""; //from web,String 1,2,3
        if (!empty($goodsImageIdsStr)) {
            $goodsImageIdsArr = explode(',', $goodsImageIdsStr);
            //判断DB中有无数据
            if (!empty($goodsImageListArr)) {
                foreach ($goodsImageListArr as $value) {
                    $goodsImagesIdDb[] = $value['id'] . '';
                }
                $intersectIds = array_intersect($goodsImageIdsArr, $goodsImagesIdDb); //交集，保留
                $deleteIds    = array_diff($goodsImagesIdDb, $intersectIds); //差集，删除
                if (!empty($deleteIds)) {
                    //删除
                    foreach ($deleteIds as $value) {
                        $this->goodsImageModel->where(['image_id' => $value])->delete();
                    }
                }
                $addIds = array_diff($goodsImageIdsArr, $intersectIds); //差集，添加
                if (!empty($addIds)) {
                    //添加
                    $updateImageData['goods_image_ids'] = implode(',', $addIds);
                    $this->handleInsertGoodsImage($updateImageData, $goodsId);
                    //处理重新审核逻辑
                    $this->auditFlag = true;
                }
            } else {
                //添加
                $updateImageData['goods_image_ids'] = $goodsImageIdsStr;
                $this->handleInsertGoodsImage($updateImageData, $goodsId);
                //处理重新审核逻辑
                $this->auditFlag = true;
            }
        } else {
            //提交过来的goods_image_ids为空，DB中有记录才进行删除操作，删除掉原先所有已经保存的记录
            if (!empty($goodsImageListArr)) {
                $this->goodsImageModel->where(['goods_id' => $goodsId])->delete();
            }
        }
        return true;
    }

    /**
     * 商品sku表数据更新
     * @param $requestData
     * @param $shopId
     * @return bool
     * @throws Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     */
    private function handleUpdateGoodsSku($requestData): bool
    {
        $goodsId = $requestData['goods_id'];
        return $this->handleInsertGoodsSku($requestData, $goodsId);
    }

    private function redisConfig()
    {
        $config            = Config::get('api');
        $this->cacheSwitch = $config['cacheSwitch'];
        $this->timeout     = $config['timeout'];
    }

    // es缓存数据,查询商品数据写入es中，所需字段名在$field中维护
    public function getListForEs(array $param = []): array
    {
        //es:order by goods_sort asc,goods_id desc where goods_status = 10 limit 500;
        //es:返回字段 goods_id
        $field = [
            'goods_id',
            'shop_id',
            'category_id', //category_name 搜索分词字段
            'goods_name', //搜索分词字段
            'selling_point', //搜索分词字段
            'service_content', //搜索分词字段
            'goods_status',
            'goods_sort',
            'update_time',
        ];
        $query = GoodsModel::field($field);
        if (isset($param['last_time']) && !empty($param['last_time'])) {
            $query->where('update_time', '>=', strtotime($param['last_time']));
        }
        if (isset($param['goods_ids'])) {
            $query->whereIn('goods_id', $param['goods_ids']);
        }
        $rows = $query->paginate(intval($param['page_size'] ?? 10))->toArray();
        // 获取商品分类id
        $cids = array_column($rows['data'], 'category_id');
        // 去空去重
        $cids = array_filter(array_unique($cids));
        if (!empty($cids)) {
            $categoryMap        = [];
            $map                = GoodsCategoryBasicModel::getCategoryMap();
            $map                = array_column($map, 'name', 'c_id');
            $goodsCategoryQuery = GoodsCategoryModel::where('c_id', 'in', $cids)->field('c_id,first_category_id,second_category_id,third_category_id,third_category_unique_name')->select();
            if (!$goodsCategoryQuery->isEmpty()) {
                foreach ($goodsCategoryQuery as $item) {
                    $categoryMap[$item->c_id] = [
                        'first_category_name'        => $map[$item->first_category_id] ?? '',
                        'second_category_name'       => $map[$item->second_category_id] ?? '',
                        'third_category_name'        => $map[$item->third_category_id] ?? '',
                        'third_category_unique_name' => $item->third_category_unique_name,
                    ];
                }
            }
            if (!empty($categoryMap)) {
                foreach ($rows['data'] as &$item) {
                    if (isset($categoryMap[$item['category_id']])) {
                        $cat                   = array_values($categoryMap[$item['category_id']]);
                        $cat                   = array_filter(array_unique($cat));
                        $item['category_name'] = implode(';', $cat);
                    } else {
                        $item['category_name'] = "";
                    }
                    $item['goods_sort'] = intval($item['goods_sort']) == 0 ? 50 : intval($item['goods_sort']);
                    $item['service_content'] = (string)$item['service_content'];
                }
                unset($item);
            }
        }
        return $rows;
    }

    /**
     * 符合商品状态重置条件，进行商品审核状态重置为未审核（audit_status=0）
     * @param int $goodsId
     * @return bool
     */
    public function resetAudit(int $goodsId = 0): bool
    {
        if (empty($goodsId)) {
            return false;
        }
        //处理重新审核逻辑
        $goodsModel = new GoodsModel();
        $goods      = $goodsModel->field('audit_status,audit_reason,audit_remark,audit_time,audit_user_id')->where('goods_id', $goodsId)->findOrEmpty();
        if ($goods->audit_status) {
            $updateData['audit_remark'] = $goods->audit_remark . '[状态：' . $goods->audit_status . ',审核原因：' . $goods->audit_reason . ',审核时间：' . date('Y-m-d H:i:s', $goods->audit_time) . ',审核人ID：' . $goods->audit_user_id . ']'; //审核备注
        } else {
            $updateData['audit_remark'] = $goods->audit_remark;
        }
        $updateData['audit_status']  = 0; //重新编辑，此状态重置为0-待审核
        $updateData['goods_status']  = 20; //商品状态(10上架 20下架)
        $updateData['audit_reason']  = ''; //置空
        $updateData['audit_time']    = ''; //置空
        $updateData['audit_user_id'] = ''; //置空
        $goodsModel->where(['goods_id' => $goodsId])->update($updateData);
        return true;
    }

    /**
     * 商品绑定合同模板
     *
     * @param array $param 参数数组，包含：
     *                     - goods_id: 商品ID
     *                     - template_id: 合同模板ID
     *                     - shop_id: 门店ID
     *                     - admin_id: 管理员ID
     * @param string &$msg 错误信息
     * @return bool 操作结果
     */
    public function bindContract(array $param = [], string &$msg = ''): bool
    {
        // 1. 参数验证
        $goodsId = intval($param['goods_id'] ?? 0);
        $templateId = intval($param['template_id'] ?? 0);
        $shopId = intval($param['shop_id'] ?? 0);
        $adminId = intval($param['admin_id'] ?? 0);

        if (empty($goodsId) || empty($templateId) || empty($shopId)) {
            $msg = '参数错误：商品ID、模板ID和门店ID不能为空';
            return false;
        }

        // 2. 验证门店认证状态
        $shop = ShopModel::where('id', $shopId)
            ->field('id,audit_status,jzq_sign_id,jzq_status')
            ->findOrEmpty();

        if ($shop->isEmpty()) {
            $msg = '未找到门店相关信息';
            return false;
        }

        if ($shop->audit_status != 1 || $shop->jzq_sign_id == 0 || $shop->jzq_status != 1) {
            $msg = '门店未认证，无法绑定合同';
            return false;
        }

        // 3. 验证合同模板状态
        $contract = ContractTemplateModel::where('template_id', $templateId)
            ->where('shop_id', $shopId)
            ->field('template_id,audit_status')
            ->findOrEmpty();

        if ($contract->isEmpty()) {
            $msg = '未找到合同模版信息';
            return false;
        }

        if ($contract->audit_status != 1) {
            $msg = '合同模版未审核通过，请先完成审核';
            return false;
        }

        // 4. 执行绑定操作
        Db::startTrans();
        try {
            // 先关闭该商品的所有合同关联
            Db::name('contract_goods_relations')
                ->where('goods_id', $goodsId)
                ->update(['is_active' => 0]);

            // 判断是否已存在相同的合同模版关联
            $existingRelation = ContractGoodsRelationModel::where('goods_id', $goodsId)
                ->where('template_id', $templateId)
                ->findOrEmpty();

            if ($existingRelation->isEmpty()) {
                // 不存在则新增关联
                Db::name('contract_goods_relations')->insert([
                    'goods_id' => $goodsId,
                    'template_id' => $templateId,
                    'is_active' => 1,
                    'creator_id' => $adminId,
                ]);
            } else {
                // 存在则激活该关联
                Db::name('contract_goods_relations')
                    ->where('goods_id', $goodsId)
                    ->where('template_id', $templateId)
                    ->update(['is_active' => 1]);
            }

            // 更新商品表，标记该商品需要合同
            Db::name('goods')
                ->where('goods_id', $goodsId)
                ->update(['require_contract' => 1]);

            Db::commit();
            return true;
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            $msg = '绑定合同失败：' . $e->getMessage();
            return false;
        }
    }

    /**
     * 解除商品与合同模板的绑定
     *
     * @param array $param 参数数组，包含：
     *                     - goods_id: 商品ID
     *                     - template_id: 合同模板ID
     * @param string &$msg 错误信息
     * @return bool 操作结果
     */
    public function deleteContract(array $param = [], string &$msg = ''): bool
    {
        // 1. 参数验证
        $goodsId = intval($param['goods_id'] ?? 0);
        $templateId = intval($param['template_id'] ?? 0);

        if (empty($goodsId) || empty($templateId)) {
            $msg = '参数错误：商品ID和模板ID不能为空';
            return false;
        }

        // 2. 验证关联关系是否存在
        $relation = ContractGoodsRelationModel::where('goods_id', $goodsId)
            ->where('template_id', $templateId)
            ->where('is_active', 1)
            ->findOrEmpty();

        if ($relation->isEmpty()) {
            $msg = '未找到商品与该合同模板的绑定关系';
            return false;
        }

        // 3. 执行解绑操作
        Db::startTrans();
        try {
            // 将该商品与该合同模板的关联状态设为非激活
            Db::name('contract_goods_relations')
                ->where('goods_id', $goodsId)
                ->where('template_id', $templateId)
                ->update(['is_active' => 0]);

            // 检查该商品是否还有其他激活的合同模板关联
            $activeRelations = ContractGoodsRelationModel::where('goods_id', $goodsId)
                ->where('is_active', 1)
                ->count();

            // 如果没有其他激活的合同模板关联，则更新商品表，标记该商品不需要合同
            if ($activeRelations == 0) {
                Db::name('goods')
                    ->where('goods_id', $goodsId)
                    ->update(['require_contract' => 0]);
            }

            Db::commit();
            return true;
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            $msg = '解除合同绑定失败：' . $e->getMessage();
            return false;
        }
    }
}
