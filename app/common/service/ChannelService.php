<?php

namespace app\common\service;

use app\common\enum\Response;
use app\common\model\ChannelBdidModel;
use think\facade\Db;
use think\facade\Event;

class ChannelService
{
    // 基本字段
    public static $basicColumns = [
        'id',
        'channel_key',
        'channel_name',
        'bdid',
        'status',
        'open_type',
        'open_id',
        'create_time',
        'update_time'
    ];

    public static $openType = [
        1 => 'pages/index/index', //首页
        2 => 'service/store/store', //门店详情
        3 => 'service/service/detail', //商品详情
        4 => 'service/active/detail', //活动详情
        5 => 'school/util/device', //魔镜列表
    ];

    public static $openKey = [
        2 => 'id',
        3 => 'id',
        4 => 'id',
    ];

    /**
     * 获取渠道BDID列表
     * @param array $param 查询参数
     * @return array 列表数据
     */
    public function getBdidList(array $param = []): array
    {
        $where = [
            ['id', '>', 0]
        ];

        // 根据渠道ID查询
        if (isset($param['channel_key']) && !empty($param['channel_key'])) {
            $where[] = ['channel_key', '=', $param['channel_key']];
        }

        // 根据BDID查询
        if (isset($param['bdid']) && !empty($param['bdid'])) {
            $where[] = ['bdid', 'like', '%' . $param['bdid'] . '%'];
        }

        // 根据BDID查询
        if (isset($param['channel_name']) && !empty($param['channel_name'])) {
            $where[] = ['channel_name', 'like', '%' . $param['channel_name'] . '%'];
        }

        // 根据状态查询
        if (isset($param['status']) && $param['status'] !== '') {
            $where[] = ['status', '=', intval($param['status'])];
        }

        // 获取列表数据
        $list = ChannelBdidModel::where($where)
            ->field(self::$basicColumns)
            ->order('id desc')
            ->paginate(config('admin.limit'))
            ->toArray();

        // 处理时间格式
        foreach ($list['data'] as &$item) {
            // 如果是整数时间戳，则进行格式化
            if (is_numeric($item['create_time'])) {
                $item['create_time'] = date('Y-m-d H:i:s', intval($item['create_time']));
            }
            if (is_numeric($item['update_time'])) {
                $item['update_time'] = date('Y-m-d H:i:s', intval($item['update_time']));
            }
        }
        return [
            "rows" => $list['data'],
            "total" => $list['total'],
            "page" => $list['current_page'],
            "page_size" => $list['per_page'],
        ];
    }

    /**
     * 获取渠道BDID详情
     * @param int $id BDID记录ID
     * @return array 详情数据
     */
    public function getBdid(int $id): array
    {
        if ($id <= 0) {
            return fail(Response::REQUEST_PARAM_ERROR, 'ID不能为空');
        }

        $bdid = ChannelBdidModel::where('id', $id)->find();
        if (empty($bdid)) {
            return fail(Response::REQUEST_ERROR, '记录不存在');
        }

        $data = $bdid->toArray();

        // 如果是整数时间戳，则进行格式化
        if (is_numeric($data['create_time'])) {
            $data['create_time'] = date('Y-m-d H:i:s', intval($data['create_time']));
        }
        if (is_numeric($data['update_time'])) {
            $data['update_time'] = date('Y-m-d H:i:s', intval($data['update_time']));
        }
        $page = self::$openType[$data['open_type']];
        $data['page'] = $page;
        $scene = [
            'bdid' => $data['bdid'],
        ];
        if (isset(self::$openKey[$data['open_type']])) {
            $scene[self::$openKey[$data['open_type']]] = $data['open_id'];
        }
        if (!empty($data)) {
            // 小程序码
            $params = [
                'scene' => http_build_query($scene),
                'page' => $page,
                'check_path' => false,
            ];
            $msg = "";
            $wechatService = new WechatService();
            $array = $wechatService->getWXACodeUnlimit('miniapp', $params, $msg);
            $data['qrcode'] =  $array;
        }

        return success($data);
    }

    /**
     * 添加渠道BDID
     * @param array $param 添加参数
     * @return array 添加结果
     */
    public function addBdid(array $param): array
    {
        // 参数验证
        if (empty($param['channel_key'])) {
            return fail(Response::REQUEST_PARAM_ERROR, '渠道ID不能为空');
        }

        if (empty($param['channel_name'])) {
            return fail(Response::REQUEST_PARAM_ERROR, '渠道name不能为空');
        }

        if (empty($param['bdid'])) {
            return fail(Response::REQUEST_PARAM_ERROR, 'BDID不能为空');
        }

        // 检查BDID是否已存在
        $exists = ChannelBdidModel::where('bdid', $param['bdid'])->where('channel_key', $param['channel_key'])->find();
        if (!empty($exists)) {
            return fail(Response::REQUEST_ERROR, 'BDID已存在');
        }

        // 开始事务
        Db::startTrans();
        try {
            $now = date('Y-m-d H:i:s');
            $data = [
                'channel_key' => $param['channel_key'],
                'channel_name' => $param['channel_name'],
                'bdid' => $param['bdid'],
                'open_type' => intval($param['open_type'] ?? 1),
                'open_id' => intval($param['open_id'] ?? 0),
                'status' => isset($param['status']) ? intval($param['status']) : 1,
                'create_time' => $now,
                'update_time' => $now
            ];

            $id = ChannelBdidModel::insertGetId($data);
            if ($id <= 0) {
                throw new \Exception('添加失败');
            }

            // 记录日志
            if (isset($param['admin_id'])) {
                Event::trigger('AdminLog', [
                    'admin_id' => $param['admin_id'],
                    'shop_id' => $param['shop_id'] ?? 0,
                    'remark' => '添加渠道BDID: ' . $param['bdid']
                ]);
            }

            // 提交事务
            Db::commit();
            return $this->getBdid($id);
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return fail(Response::REQUEST_ERROR, $e->getMessage());
        }
    }

    /**
     * 编辑渠道BDID
     * @param array $param 编辑参数
     * @return array 编辑结果
     */
    public function editBdid(array $param): array
    {
        // 参数验证
        if (empty($param['id']) || intval($param['id']) <= 0) {
            return fail(Response::REQUEST_PARAM_ERROR, 'ID不能为空');
        }

        // 检查记录是否存在
        $bdid = ChannelBdidModel::where('id', $param['id'])->find();
        if (empty($bdid)) {
            return fail(Response::REQUEST_ERROR, '记录不存在');
        }

        // 如果修改了BDID，检查是否已存在
        if (isset($param['bdid']) && $param['bdid'] != $bdid->bdid) {
            $exists = ChannelBdidModel::where('bdid', $param['bdid'])
                ->where('channel_key', $param['channel_key'] ?? $bdid->channel_key)
                ->where('id', '<>', $param['id'])
                ->find();
            if (!empty($exists)) {
                return fail(Response::REQUEST_ERROR, 'BDID已存在');
            }
        }

        // 开始事务
        Db::startTrans();
        try {
            $data = [
                'update_time' => date('Y-m-d H:i:s')
            ];

            // 可编辑字段
            if (isset($param['channel_key'])) {
                $data['channel_key'] = $param['channel_key'];
            }

            if (isset($param['channel_name'])) {
                $data['channel_name'] = $param['channel_name'];
            }

            if (isset($param['bdid'])) {
                $data['bdid'] = $param['bdid'];
            }

            if (isset($param['open_type'])) {
                $data['open_type'] = $param['open_type'];
            }

            if (isset($param['open_id'])) {
                $data['open_id'] = $param['open_id'];
            }

            if (isset($param['status'])) {
                $data['status'] = intval($param['status']);
            }

            $result = ChannelBdidModel::where('id', $param['id'])->update($data);
            if ($result === false) {
                throw new \Exception('编辑失败');
            }

            // 记录日志
            if (isset($param['admin_id'])) {
                Event::trigger('AdminLog', [
                    'admin_id' => $param['admin_id'],
                    'shop_id' => $param['shop_id'] ?? 0,
                    'remark' => '编辑渠道BDID: ' . ($param['bdid'] ?? $bdid->bdid)
                ]);
            }

            // 提交事务
            Db::commit();
            return $this->getBdid($param['id']);
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return fail(Response::REQUEST_ERROR, $e->getMessage());
        }
    }

    /**
     * 删除渠道BDID
     * @param array $param 删除参数
     * @return array 删除结果
     */
    public function deleteBdid(array $param): array
    {
        // 参数验证
        if (empty($param['id']) || intval($param['id']) <= 0) {
            return fail(Response::REQUEST_PARAM_ERROR, 'ID不能为空');
        }

        // 检查记录是否存在
        $bdid = ChannelBdidModel::where('id', $param['id'])->find();
        if (empty($bdid)) {
            return fail(Response::REQUEST_ERROR, '记录不存在');
        }

        // 开始事务
        Db::startTrans();
        try {
            $result = ChannelBdidModel::where('id', $param['id'])->delete();
            if (!$result) {
                throw new \Exception('删除失败');
            }

            // 记录日志
            if (isset($param['admin_id'])) {
                Event::trigger('AdminLog', [
                    'admin_id' => $param['admin_id'],
                    'shop_id' => $param['shop_id'] ?? 0,
                    'remark' => '删除渠道BDID: ' . $bdid->bdid
                ]);
            }

            // 提交事务
            Db::commit();
            return success([], '删除成功');
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return fail(Response::REQUEST_ERROR, $e->getMessage());
        }
    }

    /**
     * 批量删除渠道BDID
     * @param array $param 删除参数
     * @return array 删除结果
     */
    public function batchDeleteBdid(array $param): array
    {
        // 参数验证
        if (empty($param['ids']) || !is_array($param['ids'])) {
            return fail(Response::REQUEST_PARAM_ERROR, 'ID列表不能为空');
        }

        // 检查记录是否存在
        $bdids = ChannelBdidModel::where('id', 'in', $param['ids'])->select()->toArray();
        if (empty($bdids)) {
            return fail(Response::REQUEST_ERROR, '记录不存在');
        }

        // 开始事务
        Db::startTrans();
        try {
            $result = ChannelBdidModel::where('id', 'in', $param['ids'])->delete();
            if (!$result) {
                throw new \Exception('删除失败');
            }

            // 记录日志
            if (isset($param['admin_id'])) {
                $bdidList = array_column($bdids, 'bdid');
                Event::trigger('AdminLog', [
                    'admin_id' => $param['admin_id'],
                    'shop_id' => $param['shop_id'] ?? 0,
                    'remark' => '批量删除渠道BDID: ' . implode(',', $bdidList)
                ]);
            }

            // 提交事务
            Db::commit();
            return success([], '删除成功');
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return fail(Response::REQUEST_ERROR, $e->getMessage());
        }
    }

    /**
     * 更新渠道BDID状态
     * @param array $param 状态参数
     * @return array 更新结果
     */
    public function updateBdidStatus(array $param): array
    {
        // 参数验证
        if (empty($param['id']) || intval($param['id']) <= 0) {
            return fail(Response::REQUEST_PARAM_ERROR, 'ID不能为空');
        }

        if (!isset($param['status']) || !in_array($param['status'], [0, 1])) {
            return fail(Response::REQUEST_PARAM_ERROR, '状态参数错误');
        }

        // 检查记录是否存在
        $bdid = ChannelBdidModel::where('id', $param['id'])->find();
        if (empty($bdid)) {
            return fail(Response::REQUEST_ERROR, '记录不存在');
        }

        // 开始事务
        Db::startTrans();
        try {
            $data = [
                'status' => intval($param['status']),
                'update_time' => date('Y-m-d H:i:s')
            ];

            $result = ChannelBdidModel::where('id', $param['id'])->update($data);
            if ($result === false) {
                throw new \Exception('更新状态失败');
            }

            // 记录日志
            if (isset($param['admin_id'])) {
                $statusText = $param['status'] == 1 ? '启用' : '禁用';
                Event::trigger('AdminLog', [
                    'admin_id' => $param['admin_id'],
                    'shop_id' => $param['shop_id'] ?? 0,
                    'remark' => $statusText . '渠道BDID: ' . $bdid->bdid
                ]);
            }

            // 提交事务
            Db::commit();
            return $this->getBdid($param['id']);
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return fail(Response::REQUEST_ERROR, $e->getMessage());
        }
    }

    /**
     * 批量更新渠道BDID状态
     * @param array $param 状态参数
     * @return array 更新结果
     */
    public function batchUpdateBdidStatus(array $param): array
    {
        // 参数验证
        if (empty($param['ids']) || !is_array($param['ids'])) {
            return fail(Response::REQUEST_PARAM_ERROR, 'ID列表不能为空');
        }

        if (!isset($param['status']) || !in_array($param['status'], [0, 1])) {
            return fail(Response::REQUEST_PARAM_ERROR, '状态参数错误');
        }

        // 检查记录是否存在
        $bdids = ChannelBdidModel::where('id', 'in', $param['ids'])->select()->toArray();
        if (empty($bdids)) {
            return fail(Response::REQUEST_ERROR, '记录不存在');
        }

        // 开始事务
        Db::startTrans();
        try {
            $data = [
                'status' => intval($param['status']),
                'update_time' => date('Y-m-d H:i:s')
            ];

            $result = ChannelBdidModel::where('id', 'in', $param['ids'])->update($data);
            if ($result === false) {
                throw new \Exception('更新状态失败');
            }

            // 记录日志
            if (isset($param['admin_id'])) {
                $statusText = $param['status'] == 1 ? '批量启用' : '批量禁用';
                $bdidList = array_column($bdids, 'bdid');
                Event::trigger('AdminLog', [
                    'admin_id' => $param['admin_id'],
                    'shop_id' => $param['shop_id'] ?? 0,
                    'remark' => $statusText . '渠道BDID: ' . implode(',', $bdidList)
                ]);
            }

            // 提交事务
            Db::commit();
            return success([], '更新状态成功');
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return fail(Response::REQUEST_ERROR, $e->getMessage());
        }
    }
}
