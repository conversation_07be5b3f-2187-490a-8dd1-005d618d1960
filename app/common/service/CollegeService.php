<?php

declare(strict_types=1);

namespace app\common\service;

use app\api\service\MemberSer;
use app\common\model\MemberModel;

/**
 * <AUTHOR>
 * @description CollegeService
 * @date 2025/3/21
 */
class CollegeService extends BaseService
{

    /**
     * 首页导航
     * @return array
     */
    public static function getIndexTopMenu(): array
    {
        $result = curlGet(self::getDomain() . self::getInterface('getIndexTopMenu'));
        return json_decode($result, true);
    }

    /**
     * 课程中心首页列表
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getCourseIndex(): array
    {
        $result = curlGet(self::getDomain() . self::getInterface('getCourseIndex'));
        $result = json_decode($result, true);
        if ($result['code'] == 1) {
            $data = $result['data'];
            if (!empty($data['employmentStory'])) {
                foreach ($data['employmentStory'] as $key => $value) {
                    $data['employmentStory'][$key]['cover_image_url'] = formatAvatar(UploadService::get($value['cover_image_id'], "600x600"));
                }
            }
            if (!empty($data['freeCourse'])) {
                foreach ($data['freeCourse'] as $key => $value) {
                    $data['freeCourse'][$key]['cover_image_url'] = formatAvatar(UploadService::get($value['cover_image_id'], "300x300"));
                }
            }
            if (!empty($data['hotCertificate'])) {
                foreach ($data['hotCertificate'] as $key => $value) {
                    $data['hotCertificate'][$key]['cover_image_url'] = formatAvatar(UploadService::get($value['cover_image_id'], "300x300"));
                    $data['hotCertificate'][$key]['image_url'] = array_values(UploadService::getByIds(explode(',', $value['image_id'])));
                }
            }
            if (!empty($data['hotCourse'])) {
                foreach ($data['hotCourse'] as $key => $value) {
                    $data['hotCourse'][$key]['certificate_cover_image_url'] = formatAvatar(UploadService::get($value['certificate_cover_image_id'], "300x300"));
                    $data['hotCourse'][$key]['course_auth_tag'] = TagService::getByIds(explode(',', $value['course_auth_tag_id']));
                    $data['hotCourse'][$key]['certificate_tag'] = TagService::getByIds(explode(',', $value['certificate_tag_id']));
                }
            }
            if (!empty($data['skillTraining'])) {
                foreach ($data['skillTraining'] as $key => $value) {
                    $data['skillTraining'][$key]['icon_url'] = formatAvatar(UploadService::get($value['icon_id']));
                }
            }
            if (!empty($data['thinkTank'])) {
                foreach ($data['thinkTank'] as $key => $value) {
                    $data['thinkTank'][$key]['icon_url'] = formatAvatar(UploadService::get($value['icon_id']));
                }
            }
            //unset($result['data']);
            $result['data'] = $data;
        }
        return $result;
    }

    /**
     * 课程添加
     * @param array $data
     * @return array
     */
    public static function createCourse(array $data): array
    {
        $result = curlPost(self::getDomain() . self::getInterface('postCourse'), $data);
        return json_decode($result, true);
    }

    /**
     * 课程列表
     * @param array $params
     * @return array
     */
    public static function getCourse($params = []): array
    {
        $result = curlGet(self::getDomain() . self::getInterface('getCourse'), 0, $params);
        $result = json_decode($result, true);
        if ($result['code'] == 1) {
            $data = $result['data']['rows'];
            foreach ($data as $key => $value) {
                $data[$key]['cover_image_url'] = formatAvatar(UploadService::get($value['cover_image_id']));
                $data[$key]['auth_tag'] = TagService::getByIds(explode(',', $value['auth_tag_id']));
            }
            $result['data']['rows'] = $data;
        }
        return dealPaginateData($result);
    }

    /**
     * 课程详情
     * @param int $id
     * @return array
     */
    public static function getSdkCourseById(int $id): array
    {
        $result = curlGet(self::getDomain() . self::getInterface('getSdkCourseById'), $id);
        $result = json_decode($result, true);
        if ($result['code'] == 1) {
            $data = $result['data'];
            $data['cover_image_url'] = formatAvatar(UploadService::get($data['cover_image_id']));
            $data['auth_tag'] = TagService::getByIds(explode(',', $data['auth_tag_id'])); //图片标签
            $data['video_url'] = UploadService::get((int)$data['video_id']); //视频URL
            $result['data'] = $data;
        }
        return $result;
    }

    /**
     * 宝妈智库列表
     * @param int $id
     * @param array $params
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getArticleList(int $id, array $params): array
    {
        $result = curlGet(self::getDomain() . self::getInterface('getArticleList'), $id, $params);
        $result = json_decode($result, true);
        if ($result['code'] == 1) {
            $data = $result['data'];
            if (!empty($data['course_list'])) {
                foreach ($data['course_list'] as $key => $value) {
                    $data['course_list'][$key]['cover_image_url'] = formatAvatar(UploadService::get($value['cover_image_id']));
                    $data['course_list'][$key]['auth_tag'] = TagService::getByIds(explode(',', $value['auth_tag_id'])); //图片标签
                }
                unset($data['course_list'][$key]['cover_image_id'], $data['course_list'][$key]['auth_tag_id']);
            }
            $result['data'] = $data;
        }
        return $result;
    }

    /**
     * 课程列表（技能培训列表）
     * @param int $id
     * @param array $params
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getCourseList(int $id, array $params): array
    {
        $result = curlGet(self::getDomain() . self::getInterface('getCourseList'), $id, $params);
        $result = json_decode($result, true);
        if ($result['code'] == 1) {
            $data = $result['data'];
            if (!empty($data['course_list'])) {
                foreach ($data['course_list'] as $key => $value) {
                    $data['course_list'][$key]['cover_image_url'] = formatAvatar(UploadService::get($value['cover_image_id']));
                    $data['course_list'][$key]['auth_tag'] = TagService::getByIds(explode(',', $value['auth_tag_id'])); //图片标签
                    $expert = [];
                    if (!empty($value['expert_detail'])) {
                        $expert['id'] = $value['expert_detail']['id'];
                        $expert['name'] = $value['expert_detail']['name'];
                        $expert['avatar_url'] = formatAvatar(UploadService::get($value['expert_detail']['avatar_id']));
                        $expert['auth_tag'] = TagService::getByIds(explode(',', $value['expert_detail']['auth_tag_id']));; //图片标签
                        $expert['expert_tag'] = TagService::getByIds(explode(',', $value['expert_detail']['expert_tag_id'])); //文字标签
                    }
                    unset($data['course_list'][$key]['expert_detail']);
                    $data['course_list'][$key]['expert_detail'] = $expert;
                    unset($expert);
                }
                unset($data['course_list'][$key]['cover_image_id'], $data['course_list'][$key]['auth_tag_id']);
            }
            $result['data'] = $data;
        }
        return $result;
    }

    /**
     * 好课试听列表
     * @param int $id
     * @param array $params
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getFreeCourseList(int $id, array $params): array
    {
        $result = curlGet(self::getDomain() . self::getInterface('getFreeCourseList'), $id, $params);
        $result = json_decode($result, true);
        if ($result['code'] == 1) {
            if (empty($result['data'])) {
                return $result;
            }
            $data = $result['data']['rows'];
            if (!empty($data)) {
                foreach ($data as $key => $value) {
                    $data[$key]['cover_image_url'] = formatAvatar(UploadService::get($value['cover_image_id'], "300x300"));
                    $data[$key]['auth_tag'] = TagService::getByIds(explode(',', $value['auth_tag_id'])); //图片标签
                    $expert = [];
                    if (!empty($value['expert_detail'])) {
                        $expert['id'] = $value['expert_detail']['id'];
                        $expert['name'] = $value['expert_detail']['name'];
                        $expert['avatar_url'] = formatAvatar(UploadService::get($value['expert_detail']['avatar_id']));
                        $expert['auth_tag'] = TagService::getByIds(explode(',', $value['expert_detail']['auth_tag_id']));; //图片标签
                        $expert['expert_tag'] = TagService::getByIds(explode(',', $value['expert_detail']['expert_tag_id'])); //文字标签
                    }
                    unset($data[$key]['expert_detail']);
                    $data[$key]['expert_detail'] = $expert;
                    unset($expert);
                }
                unset($data[$key]['cover_image_id'], $data[$key]['auth_tag_id']);
            }
            $result['data'] = $data;
        }
        return $result;
    }

    /**
     * 课程列表（热门好课列表）
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getHotCourseList(array $params): array
    {
        $result = curlGet(self::getDomain() . self::getInterface('getHotCourseList'), 0, $params);
        $result = json_decode($result, true);
        if ($result['code'] == 1) {
            if (empty($result['data'])) {
                return $result;
            }
            $data = $result['data']['rows'];
            if (!empty($data)) {
                foreach ($data as $key => $value) {
                    $data[$key]['course_auth_tag'] = TagService::getByIds(explode(',', $value['course_auth_tag_id']));
                    $data[$key]['students_image_url'] = []; //最多5个
                    $members = MemberModel::where('id', 'in', $value['students_member_id'])->column('avatar');
                    if (!empty($members)) {
                        foreach ($members as $k => $v) {
                            $data[$key]['students_image_url'][$k]['avatar'] = $v;
                        }
                    }
                    if (!empty($value['certificate_detail'])) {
                        $data[$key]['certificate_detail']['cover_image_url'] = formatAvatar(UploadService::get($value['certificate_detail']['cover_image_id']));
                        $data[$key]['certificate_detail']['tag'] = TagService::getByIds(explode(',', $value['certificate_detail']['tag_id']));

                        // 过滤富文本，减少传输大小
                        if (isset($data[$key]['certificate_detail']['details'])) {
                            unset($data[$key]['certificate_detail']['details']);
                        }
                    }
                }
            }
        }
        $result['data'] = $data;
        return $result;
    }

    /**
     * 文章列表（就业故事）
     * @return array
     */
    public static function getStoryList(array $params): array
    {
        $result = curlGet(self::getDomain() . self::getInterface('getStoryList'), 0, $params);
        $result = json_decode($result, true);
        if ($result['code'] == 1) {
            if (empty($result['data'])) {
                return $result;
            }
            $data = $result['data']['rows'];
            if (!empty($data)) {
                foreach ($data as $key => $value) {
                    $data[$key]['cover_image_url'] = formatAvatar(UploadService::get($value['cover_image_id']));
                }
            }
            $result['data'] = $data;
        }
        return $result;
    }

    /**
     * 课程详情
     * @param int $id
     * @param array $params
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public static function getCourseById(int $id, array $params): array
    {
        $result = curlGet(self::getDomain() . self::getInterface('getCourseById'), $id, $params);
        $result = json_decode($result, true);
        if ($result['code'] == 1) {
            $data = $result['data'];
            $data['cover_image_url'] = formatAvatar(UploadService::get($data['cover_image_id']));
            //类型 1-课程；2-文章
            if ($data['type'] == 1) {
                $data['video_url'] = UploadService::get((int)$data['video_id']); //视频URL
            } else if ($data['type'] == 2) {
                if (!empty($data['more_article_list'])) {
                    $moreArticles = $data['more_article_list'];
                    foreach ($moreArticles as $k => $v) {
                        $data['more_article_list'][$k]['cover_image_url'] = formatAvatar(UploadService::get($v['cover_image_id']));
                        // 如果是文章则 是文字标签
                        $data['more_article_list'][$k]['auth_tag'] = TagService::getByIds(explode(',', $v['auth_tag_id']));;
                    }
                }
                $result['data'] = $data;
                return $result;
            }
            if (!empty($data['publisher'])) {
                $data['publisher']['avatar_url'] = formatAvatar(UploadService::get($data['publisher']['avatar_id']));
                $data['publisher']['expert_tag'] = TagService::getByIds(explode(',', $data['publisher']['expert_tag_id']));
                $data['publisher']['auth_tag'] = TagService::getByIds(explode(',', $data['publisher']['auth_tag_id'])); //图片标签
            }
            if (!empty($data['course_section'])) {
                $course_section = $data['course_section'];
                foreach ($course_section as $key => $value) {
                    $videoTime = 0;
                    $file = UploadService::getById((int)$value['video_id']);
                    if (!empty($file) & !empty($file['expand_field'])) {
                        $expandField = json_decode($file['expand_field'], true);
                        $videoTime = $expandField['duration'] ?? 0; //视频时长，单位秒
                    }
                    $value['video_time'] = $videoTime;
                    $value['price'] = 0.0;
                    $value['lesson_state'] = 1; // - 学习状态， 0-不可学习，1-可学习；
                    $data['course_section'][$key] = $value;
                }
            }
            if (!empty($data['more_video_list'])) {
                $moreArticles = $data['more_video_list'];
                foreach ($moreArticles as $k => $v) {
                    $data['more_video_list'][$k]['cover_image_url'] = formatAvatar(UploadService::get($v['cover_image_id']));
                    $data['more_video_list'][$k]['auth_tag'] = TagService::getByIds(explode(',', $data['more_video_list'][$k]['auth_tag_id'])); //图片标签
                    if (!empty($moreArticles[$k]['expert_detail'])) {
                        $data['more_video_list'][$k]['expert_detail']['avatar_url'] = formatAvatar(UploadService::get($data['more_video_list'][$k]['expert_detail']['avatar_id']));;
                        $data['more_video_list'][$k]['expert_detail']['expert_tag'] = TagService::getByIds(explode(',', $data['more_video_list'][$k]['expert_detail']['expert_tag_id']));
                        $data['more_video_list'][$k]['expert_detail']['auth_tag'] = TagService::getByIds(explode(',', $data['more_video_list'][$k]['expert_detail']['auth_tag_id']));
                    }
                }
            }
            $result['data'] = $data;
        }
        return $result;
    }

    /**
     * 课程更新
     * @param int $id
     * @param array $data
     * @return array
     */
    public static function updateCourseById(int $id, array $data): array
    {
        $result = curlPut(self::getDomain() . self::getInterface('putCourse') . "/" . $id, $data);
        return json_decode($result, true);
    }

    /**
     * 课程更新排序
     * @param int $id
     * @param array $data
     * @return array
     */
    public static function updateCourseSortById(int $id, array $data): array
    {
        $result = curlPut(self::getDomain() . self::getInterface('putCourseSort') . "/" . $id, $data);
        return json_decode($result, true);
    }

    /**
     * 课程更新阅读次数
     * @param int $id
     * @return array
     */
    public static function updateCourseViewCount(int $id): array
    {
        $result = curlPut(self::getDomain() . self::getInterface('putCourseViewCount') . "/" . $id);
        return json_decode($result, true);
    }

    /**
     * 课程删除
     * @param int $id
     * @return array
     */
    public static function deleteCourseById(int $id): array
    {
        $result = curlDelete(self::getDomain() . self::getInterface('deleteCourse'), $id);
        return json_decode($result, true);
    }

    /**
     * 课程章节添加
     * @param array $data
     * @return array
     */
    public static function createCourseSection(array $data): array
    {
        $result = curlPost(self::getDomain() . self::getInterface('postCourseSection'), $data);
        return json_decode($result, true);
    }

    /**
     * 通过课程ID获取章节列表
     * @param int $id
     * @return array
     */
    public static function getCourseSectionByCourseId(int $id, array $params): array
    {
        $result = curlGet(self::getDomain() . self::getInterface('getCourseSectionByCourseId'), $id, $params);
        $result = json_decode($result, true);
        if ($result['code'] == 1) {
            $data = $result['data']['rows'];
            if (!empty($data)) {
                foreach ($data as $key => $value) {
                    $value['type'] = $value['type']; //类型 1-课程(视频)；2-文章',
                    $videoTime = 0;
                    $file = UploadService::getById($value['video_id']);
                    if (!empty($file) & !empty($file['expand_field'])) {
                        $expandField = json_decode($file['expand_field'], true);
                        $videoTime = $expandField['duration'] ?? 0; //视频时长，单位秒
                    }
                    $value['video_time'] = $videoTime;
                    $value['price'] = 0.0;
                    $value['lesson_state'] = 1; // - 学习状态， 0-不可学习，1-可学习；
                    $value['video_url'] = UploadService::get((int)$value['video_id']); //视频URL
                    $data[$key] = $value;
                }
            }
            $result['data']['rows'] = $data;
        }
        return dealPaginateData($result);
    }

    /**
     * 通过章节ID获取详情
     * @param int $id
     * @return array
     */
    public static function getCourseSectionById(int $id, array $params = []): array
    {
        $result = curlGet(self::getDomain() . self::getInterface('getCourseSectionById'), $id, $params);
        $result = json_decode($result, true);
        if ($result['code'] == 1) {
            $result['data']['video_url'] = UploadService::get((int)$result['data']['video_id']); //视频URL
        }
        return $result;
    }

    /**
     * 课程章节更新
     * @param int $id
     * @param array $data
     * @return array
     */
    public static function updateCourseSectionById(int $id, array $data): array
    {
        $result = curlPut(self::getDomain() . self::getInterface('putCourseSection') . "/" . $id, $data);
        return json_decode($result, true);
    }

    /**
     * 课程章节更新排序
     * @param int $id
     * @param array $data
     * @return array
     */
    public static function updateCourseSectionSortById(int $id, array $data): array
    {
        $result = curlPut(self::getDomain() . self::getInterface('putCourseSectionSort') . "/" . $id, $data);
        return json_decode($result, true);
    }

    /**
     * 课程章节删除
     * @param int $id
     * @return array
     */
    public static function deleteCourseSectionById(int $id): array
    {
        $result = curlDelete(self::getDomain() . self::getInterface('deleteCourseSection'), $id);
        return json_decode($result, true);
    }

    /**
     * 分类添加
     * @param array $data
     * @return array
     */

    public static function createCategory(array $data): array
    {
        $result = curlPost(self::getDomain() . self::getInterface('postCategory'), $data);
        return json_decode($result, true);
    }

    /**
     * 分类列表
     * @return array
     */
    public static function getCategory(): array
    {
        $result = curlGet(self::getDomain() . self::getInterface('getCategory'));
        $result = json_decode($result, true);
        //halt($result);
        if ($result['code'] == 1) {
            $data = $result['data'];
            self::processTreeNodes($data);
            $result['data'] = $data;
        }
        return $result;
    }

    /**
     * 递归处理icon_id=>icon_url
     * @param $nodes
     * @return void
     */
    private static function processTreeNodes(&$nodes): void
    {
        foreach ($nodes as &$node) {
            // 处理当前节点的icon_id
            if (!empty($node['icon_id'])) {
                try {
                    $uploadResult = UploadService::get($node['icon_id']);
                    $node['icon_url'] = formatAvatar($uploadResult);
                } catch (\Exception $e) {
                    error_log("Icon处理失败: " . $e->getMessage());
                    $node['icon_url'] = '';
                }
            } else {
                $node['icon_url'] = '';
            }
            // 递归处理子节点（假设子节点存储在children键下）
            if (!empty($node['children']) && is_array($node['children'])) {
                self::processTreeNodes($node['children']);
            }
        }
    }

    /**
     * 分类详情
     * @param int $id
     * @return array
     */
    public static function getCategoryById(int $id): array
    {
        $result = curlGet(self::getDomain() . self::getInterface('getCategoryById'), $id);
        $result = json_decode($result, true);
        if ($result['code'] == 1) {
            $data = $result['data'];
            $data['icon_url'] = formatAvatar(UploadService::get($data['icon_id']));
            $result['data'] = $data;
        }
        return $result;
    }

    /**
     * 分类更新
     * @param int $id
     * @param array $data
     * @return array
     */
    public static function updateCategoryById(int $id, array $data): array
    {
        $result = curlPut(self::getDomain() . self::getInterface('putCategory') . "/" . $id, $data);
        return json_decode($result, true);
    }

    /**
     * 分类更新排序
     * @param int $id
     * @param array $data
     * @return array
     */
    public static function updateCategorySortById(int $id, array $data): array
    {
        $result = curlPut(self::getDomain() . self::getInterface('putCategorySort') . "/" . $id, $data);
        return json_decode($result, true);
    }

    /**
     * 分类删除
     * @param int $id
     * @return array
     */
    public static function deleteCategoryById(int $id): array
    {
        $result = curlDelete(self::getDomain() . self::getInterface('deleteCategory'), $id);
        return json_decode($result, true);
    }

    /**
     * 课程学习添加
     * @param array $data
     * @return array
     */
    public static function createMemberCourseRecord(array $data): array
    {
        $result = curlPost(self::getDomain() . self::getInterface('postMemberCourseRecord'), $data);
        return json_decode($result, true);
    }

    /**
     * 课程学习列表
     * @return array
     */
    public static function getMemberCourseRecord(array $params): array
    {
        $result = curlGet(self::getDomain() . self::getInterface('getMemberCourseRecord'), 0, $params);
        $result = json_decode($result, true);
        if ($result['code'] == 1) {
            $data = $result['data']['rows'];
            foreach ($data as $key => $value) {
                if (empty($data[$key]['member_id'])) {
                    $data[$key]['member_name'] = "";
                    $data[$key]['member_name'] = "";
                } else {
                    $memberId = $data[$key]['member_id'];
                    $data[$key]['member_name'] = MemberModel::where(['id' => $memberId])->value("nickname") ?? "";
                    $data[$key]['member_mobile'] = MemberModel::where(['id' => $memberId])->value("mobile") ?? "";
                }
            }
            $result['data']['rows'] = $data;
        }

        return dealPaginateData($result);
    }

    /**
     * 课程学习详情
     * @param int $id
     * @return array
     */
    public static function getMemberCourseRecordById(int $id): array
    {
        $result = curlGet(self::getDomain() . self::getInterface('getMemberCourseRecordById'), $id);
        return json_decode($result, true);
    }

    /**
     * 课程学习详情
     * @param int $id
     * @return array
     */
    public static function getMemberCourseRecordId(int $id): array
    {
        $result = curlGet(self::getDomain() . self::getInterface('getMemberCourseRecordById'), $id);
        return json_decode($result, true);
    }

    /**
     * 课程学习更新
     * @param int $id
     * @param array $data
     * @return array
     */
    public static function updateMemberCourseRecordById(int $id, array $data): array
    {
        $result = curlPut(self::getDomain() . self::getInterface('putMemberCourseRecord') . "/" . $id, $data);
        return json_decode($result, true);
    }

    /**
     * 课程学习更新排序
     * @param int $id
     * @param array $data
     * @return array
     */
    public static function updateMemberCourseRecordSortById(int $id, array $data): array
    {
        $result = curlPut(self::getDomain() . self::getInterface('putMemberCourseRecordSort') . "/" . $id, $data);
        return json_decode($result, true);
    }

    /**
     * 课程学习删除
     * @param int $id
     * @return array
     */
    public static function deleteMemberCourseRecordById(int $id): array
    {
        $result = curlDelete(self::getDomain() . self::getInterface('deleteMemberCourseRecord'), $id);
        return json_decode($result, true);
    }

    /**
     * 专家添加
     * @param array $data
     * @return array
     */

    public static function createExpert(array $data): array
    {
        $result = curlPost(self::getDomain() . self::getInterface('postExpert'), $data);
        return json_decode($result, true);
    }

    /**
     * 专家列表
     * @return array
     */
    public static function getExpert(array $params): array
    {
        $result = curlGet(self::getDomain() . self::getInterface('getExpert'), 0, $params);
        $result = json_decode($result, true);
        if ($result['code'] == 1) {
            $data = $result['data']['rows'];
            foreach ($data as $key => $value) {
                $data[$key]['avatar_url'] = formatAvatar(UploadService::get($value['avatar_id']));
                $data[$key]['expert_tag'] = TagService::getByIds(explode(',', $value['expert_tag_id']));;
                $data[$key]['auth_tag'] = TagService::getByIds(explode(',', $value['auth_tag_id']));;
            }
            $result['data']['rows'] = $data;
        }
        return dealPaginateData($result);
    }

    /**
     * 专家详情
     * @param int $id
     * @return array
     */
    public static function getExpertById(int $id): array
    {
        $result = curlGet(self::getDomain() . self::getInterface('getExpertById'), $id);
        $result = json_decode($result, true);
        if ($result['code'] == 1) {
            $data = $result['data'];
            $data['auth_category_image_url'] = formatAvatar(UploadService::getByIds(explode(',', $data['auth_category_image_id'])));
            $data['avatar_url'] = formatAvatar(UploadService::get($data['avatar_id']));
            $data['image_url'] = formatAvatar(UploadService::getByIds(explode(',', $data['image_id'])));
            $result['data'] = $data;
        }
        return $result;
    }

    /**
     * 专家分类列表（全科专家团）
     * @return array
     */
    public static function getExpertCategoryList(): array
    {
        $result = curlGet(self::getDomain() . self::getInterface('getExpertCategoryList'));
        $result = json_decode($result, true);
        if ($result['code'] == 1) {
            $data = $result['data'];
            if (!empty($data)) {
                foreach ($data as $key => $value) {
                    $data[$key]['icon_url'] = formatAvatar(UploadService::get($value['icon_id']));
                    unset($data[$key]['icon_id']);
                }
            }
            $result['data'] = $data;
        }
        return $result;
    }

    /**
     * 专家列表（热门专家）
     * @return array
     */
    public static function getHotExpertList(array $params): array
    {
        $result = curlGet(self::getDomain() . self::getInterface('getHotExpertList'), 0, $params);
        $result = json_decode($result, true);
        if ($result['code'] == 1) {
            $data = $result['data']['rows'];
            foreach ($data as $key => $value) {
                $data[$key]['avatar_url'] = formatAvatar(UploadService::get($value['avatar_id']));
                $data[$key]['auth_tag'] = TagService::getByIds(explode(',', $value['auth_tag_id'])); //图片标签
                $data[$key]['expert_tag'] = TagService::getByIds(explode(',', $value['expert_tag_id']));
            }
            $result['data'] = $data;
        }
        return $result;
    }

    /**
     * 专家列表（全科专家团分类列表）
     * @return array
     */
    public static function getTreeExpertList(int $id): array
    {
        $result = curlGet(self::getDomain() . self::getInterface('getTreeExpertList'), $id);
        $result = json_decode($result, true);
        if ($result['code'] == 1) {
            $data = $result['data'];
            if (!empty($data['expert_list'])) {
                $expert_list = $data['expert_list'];
                foreach ($expert_list as $key => $value) {
                    $expert_list[$key]['avatar_url'] = formatAvatar(UploadService::get($expert_list[$key]['avatar_id']));
                    $expert_list[$key]['auth_tag'] = TagService::getByIds(explode(',', $value['auth_tag_id'])); //图片标签
                    $expert_list[$key]['expert_tag'] = TagService::getByIds(explode(',', $value['expert_tag_id']));
                }
                $data['expert_list'] = $expert_list;
            }
            $result['data'] = $data;
        }
        return $result;
    }

    /**
     * 专家详情
     * @param int $id
     * @return array
     */
    public static function getExpertId(int $id): array
    {
        $result = curlGet(self::getDomain() . self::getInterface('getExpertById'), $id);
        return json_decode($result, true);
    }

    /**
     * 专家详情（课程列表&文章列表）
     * @param int $id
     * @return array
     */
    public static function getExpertCourseById(int $id): array
    {
        $result = curlGet(self::getDomain() . self::getInterface('getExpertCourseById'), $id);
        $result = json_decode($result, true);
        if ($result['code'] == 1) {
            $data = $result['data'];
            $image = formatAvatar(UploadService::getByIds(explode(',', $data['image_id'])));
            $data['image_url'] = [];
            if (!empty($image) && is_array($image)) {
                foreach ($image as $key => $value) {
                    $data["image_url"][] = ['id' => $key, 'url' => $value];
                }
            }

            $categoryImage = formatAvatar(UploadService::getByIds(explode(',', $data['auth_category_image_id'])));
            $data['auth_category_image'] = [];
            if (!empty($categoryImage) && is_array($categoryImage)) {
                foreach ($categoryImage as $key => $value) {
                    $data["auth_category_image"][] = ['id' => $key, 'url' => $value];
                }
            }
            $data['avatar_url'] = formatAvatar(UploadService::get($data['avatar_id']));
            $data['auth_tag'] = TagService::getByIds(explode(',', $data['auth_tag_id'])); //图片标签
            $data['expert_tag'] = TagService::getByIds(explode(',', $data['expert_tag_id']));
            $data['verification'] = $data['create_time'];

            if (!empty($data['course'])) {
                foreach ($data['course'] as $key => $value) {
                    $data["course"][$key]['cover_image_url'] = formatAvatar(UploadService::get($value['cover_image_id']));
                    $data["course"][$key]['auth_tag'] = TagService::getByIds(explode(',', $value['auth_tag_id']));
                }
            }
            if (!empty($data['article'])) {
                foreach ($data['article'] as $key => $value) {
                    $data["article"][$key]['cover_image_url'] = formatAvatar(UploadService::get($value['cover_image_id']));
                    $data["article"][$key]['auth_tag'] = TagService::getByIds(explode(',', $value['auth_tag_id']));
                }
            }
            $result['data'] = $data;
        }
        return $result;
    }

    /**
     * 专家更新
     * @param int $id
     * @param array $data
     * @return array
     */
    public static function updateExpertById(int $id, array $data): array
    {
        $result = curlPut(self::getDomain() . self::getInterface('putExpert') . "/" . $id, $data);
        return json_decode($result, true);
    }

    /**
     * 专家更新排序
     * @param int $id
     * @param array $data
     * @return array
     */
    public static function updateExpertSortById(int $id, array $data): array
    {
        $result = curlPut(self::getDomain() . self::getInterface('putExpertSort') . "/" . $id, $data);
        return json_decode($result, true);
    }

    /**
     * 专家删除
     * @param int $id
     * @return array
     */
    public static function deleteExpertById(int $id): array
    {
        $result = curlDelete(self::getDomain() . self::getInterface('deleteExpert'), $id);
        return json_decode($result, true);
    }

    /**
     * 证书添加
     * @param array $data
     * @return array
     */

    public static function createCertificate(array $data): array
    {
        $result = curlPost(self::getDomain() . self::getInterface('postCertificate'), $data);
        return json_decode($result, true);
    }

    /**
     * 证书列表
     * @param array $params
     * @return array
     */
    public static function getCertificate(array $params): array
    {
        $result = curlGet(self::getDomain() . self::getInterface('getCertificate'), 0, $params);
        $result = json_decode($result, true);
        if ($result['code'] == 1) {
            $data = $result['data']['rows'];
            foreach ($data as $key => $value) {
                $data[$key]['cover_image_url'] = formatAvatar(UploadService::get($value['cover_image_id']));
            }
            $result['data']['rows'] = $data;
        }
        return dealPaginateData($result);
    }

    /**
     * 证书详情
     * @return array
     */
    public static function getCertificateById(int $id): array
    {
        $result = curlGet(self::getDomain() . self::getInterface('getCertificateById'), $id);
        $result = json_decode($result, true);
        if ($result['code'] == 1) {
            $data = $result['data'];
            $data['image_url'] = formatAvatar(UploadService::getByIds(explode(',', $data['image_id'])));
            $data['cover_image_url'] = formatAvatar(UploadService::get($data['cover_image_id']));
            $result['data'] = $data;
        }
        return $result;
    }

    /**
     * 证书列表（热门证书）
     * @return array
     */
    public static function getHotCertificateList(array $params): array
    {
        $result = curlGet(self::getDomain() . self::getInterface('getHotCertificateList'), 0, $params);
        $result = json_decode($result, true);
        if ($result['code'] == 1) {
            if (empty($result['data'])) {
                return $result;
            }
            $data = $result['data']['rows'];
            if (!empty($data)) {
                foreach ($data as $key => $value) {
                    $data[$key]['cover_image_url'] = formatAvatar(UploadService::get((int)$value['cover_image_id']));
                    if (!empty($data[$key]['course_list'])) {
                        foreach ($data[$key]['course_list'] as $kkey => $vvalue) {
                            $data[$key]['course_list'][$kkey]['cover_image_url'] = formatAvatar(UploadService::get($vvalue['cover_image_id']));
                            $data[$key]['course_list'][$kkey]['students_image_url'] = []; //最多5个
                            $members = MemberModel::where('id', 'in', $vvalue['students_member_id'])->column('avatar');
                            if (!empty($members)) {
                                foreach ($members as $k => $v) {
                                    $data[$key]['course_list'][$kkey]['students_image_url'][$k]['avatar'] = $v;
                                }
                            }
                        }
                    }
                    if (isset($data[$key]['details'])) {
                        // 过滤富文本，减少传输大小
                        unset($data[$key]['details']);
                    }
                }
            }
            $result['data'] = $data;
        }
        return $result;
    }

    /**
     * 证书列表
     * @return array
     */
    public static function getTreeCertificateList(): array
    {
        $result = curlGet(self::getDomain() . self::getInterface('getTreeCertificateList'));
        return json_decode($result, true);
    }

    /**
     * 证书详情(包含课程列表)
     * @param int $id
     * @return array
     */
    public static function getCertificateCourseById(int $id): array
    {
        $result = curlGet(self::getDomain() . self::getInterface('getCertificateCourseById'), $id);
        $result = json_decode($result, true);
        if ($result['code'] == 1) {
            $data = $result['data'];
            $image = UploadService::getByIds(explode(',', $data['image_id']));
            $data["image_url"] = [];
            if (!empty($image)) {
                foreach ($image as $key => $value) {
                    $data["image_url"][] = ['id' => $key, 'url' => $value];
                }
            }
            $data['students_got_image_url'] = [];
            if (!empty($data['courses'])) {
                $courses = $data['courses'];
                foreach ($courses as $key => $value) {
                    $data['courses'][$key]['cover_image_url'] = formatAvatar(UploadService::get((int)$value['cover_image_id']));
                    $data['courses'][$key]['students_image_url'] = [];
                    $members = MemberModel::where('id', 'in', $value['students_member_id'])->column('avatar');
                    if (!empty($members)) {
                        foreach ($members as $k => $v) {
                            $data['courses'][$key]['students_image_url'][$k]['avatar'] = $v;
                        }
                    }
                }
            }
            $result['data'] = $data;
        }

        return $result;
    }

    /**
     * 证书更新
     * @param int $id
     * @param array $data
     * @return array
     */
    public static function updateCertificateById(int $id, array $data): array
    {
        $result = curlPut(self::getDomain() . self::getInterface('putCertificate') . "/" . $id, $data);
        return json_decode($result, true);
    }

    /**
     * 证书更新
     * @param int $id
     * @param array $data
     * @return array
     */
    public static function updateCertificateSortById(int $id, array $data): array
    {
        $result = curlPut(self::getDomain() . self::getInterface('putCertificateSort') . "/" . $id, $data);
        return json_decode($result, true);
    }

    /**
     * 证书更新排序
     * @param int $id
     * @param array $data
     * @return array
     */
    public static function updateCertificateSort(int $id, array $data): array
    {
        $result = curlPut(self::getDomain() . self::getInterface('putCertificateSort') . "/" . $id, $data);
        return json_decode($result, true);
    }

    /**
     * 证书删除
     * @param int $id
     * @return array
     */
    public static function deleteCertificateById(int $id): array
    {
        $result = curlDelete(self::getDomain() . self::getInterface('deleteCertificate'), $id);
        return json_decode($result, true);
    }
}
