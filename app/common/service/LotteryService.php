<?php

namespace app\common\service;

use app\common\model\ActivityModel;
use app\common\model\ActivitySignupModel;

class LotteryService
{

    // 创建抽奖活动
    public function createLottery($tenantType, $token, $param = [], &$insertId = 0): bool
    {
        // period 不能为空
        if (!$param['period']) {
            throw new \Exception('period不能为空');
            return false;
        }

        // start_time 不能为空 ， end_time 不能为空， start_time 必须小于 end_time
        // if ($param['start_time'] != "" || !$param['end_time'] != "") {
        //     // throw new \Exception('start_time 和 end_time 不能为空');
        //     // return false;
        //     if ($param['start_time'] >= $param['end_time']) {
        //         throw new \Exception('start_time 必须小于 end_time');
        //         return false;
        //     }
        // }

        // is_must 不能为空，只能是 0 或 1
        if (!isset($param['is_must']) || ($param['is_must'] != 0 && $param['is_must'] != 1)) {
            throw new \Exception('is_must 不能为空，只能是 0 或 1');
            return false;
        }
        $data = [
            'tenant_type' => $tenantType,
            'period' => $param['period'],
            'start_time' => $param['start_time'],
            'end_time' => $param['end_time'],
            'is_must' => $param['is_must'],
            'remark' => $param['remark'] ?? '',
            'template_id' => $param['template_id'] ?? 0,
        ];
        $config = config('admin.services');
        $res =  curlPostApiContentByUrlAndParams($config['Member']['BaseURL'] . $config['Member']['Api']['CreateLottery'], $data, ['Authorization: Bearer ' . $token]);
        if (empty($res)) {
            throw new \Exception('请求失败');
            return false;
        }
        if ($res['code'] != 1) {
            throw new \Exception($res['msg']);
            return false;
        }
        $insertId = $res['data']['id'] ?? 0;
        return true;
    }

    // 修改抽奖活动
    public function updateLottery($tenantType, $token, $param = []): bool
    {
        // id 不能为空
        if (!$param['id']) {
            throw new \Exception('id不能为空');
            return false;
        }

        $data = [
            'id' => $param['id'],
            'tenant_type' => $tenantType,
        ];

        //start_time 存在就判断，end_time 存在就判断，start_time 必须小于 end_time
        if (isset($param['start_time']) && isset($param['end_time'])) {
            if ($param['start_time'] >= $param['end_time']) {
                throw new \Exception('start_time 必须小于 end_time');
                return false;
            }
            $data['start_time'] = $param['start_time'];
            $data['end_time'] = $param['end_time'];
        }

        if (isset($param['is_must'])) {
            // is_must 只能是 0 或 1
            if ($param['is_must'] != 0 && $param['is_must'] != 1) {
                throw new \Exception('is_must 只能是 0 或 1');
                return false;
            }
            $data['is_must'] = $param['is_must'];
        }

        if (isset($param['remark'])) {
            $data['remark'] = $param['remark'];
        }

        $config = config('admin.services');
        $res =  curlPostApiContentByUrlAndParams($config['Member']['BaseURL'] . $config['Member']['Api']['UpdateLottery'], $data, ['Authorization: Bearer ' . $token], true);
        if (empty($res)) {
            throw new \Exception('请求失败');
            return false;
        }
        if ($res['code'] != 1) {
            throw new \Exception($res['msg']);
            return false;
        }
        return true;
    }

    // 创建抽奖活动奖品
    public function createLotteryPrize($tenantType, $token, $param = []): bool
    {
        //period 不能为空
        if (!$param['period']) {
            throw new \Exception('period不能为空');
            return false;
        }
        //name 不能为空
        if (!$param['name']) {
            throw new \Exception('name不能为空');
            return false;
        }
        // image_id 不能为空
        if (!$param['image_id']) {
            throw new \Exception('image_id不能为空');
            return false;
        }
        //p_data 不能为空
        if (!$param['p_data']) {
            throw new \Exception('p_data不能为空');
            return false;
        }
        // limit_stock 不能为空, 只能是0或1 ,0表示不限制库存，1表示限制库存
        if (!isset($param['limit_stock']) || ($param['limit_stock'] != 0 && $param['limit_stock'] != 1)) {
            throw new \Exception('limit_stock 不能为空，只能是0或1');
            return false;
        }
        // limit_stock=1  stock 必须大于0
        if ($param['limit_stock'] == 1 && $param['stock'] <= 0) {
            throw new \Exception('limit_stock=1  stock 必须大于0');
            return false;
        }
        // per 中奖概率，必须大于0，小于等于100
        if ($param['per'] <= 0 || $param['per'] > 100) {
            throw new \Exception('per 中奖概率，必须大于0，小于等于100');
            return false;
        }
        $data = [
            'tenant_type' => $tenantType,
            'period' => $param['period'],
            'name' => $param['name'],
            'image_id' => $param['image_id'],
            'p_data' => $param['p_data'],
            'limit_stock' => $param['limit_stock'],
            'stock' => $param['stock'] ?? 0,
            'per' => $param['per'],
            'sort_order' => $param['sort_order'] ?? 0,
        ];
        $config = config('admin.services');
        $res =  curlPostApiContentByUrlAndParams($config['Member']['BaseURL'] . $config['Member']['Api']['CreateLotteryPrize'], $data, ['Authorization: Bearer ' . $token]);
        if (empty($res)) {
            throw new \Exception('请求失败');
            return false;
        }
        if ($res['code'] != 1) {
            throw new \Exception($res['msg']);
            return false;
        }
        return true;
    }

    // 修改抽奖活动奖品
    public function updateLotteryPrize($tenantType, $token, $param = []): bool
    {
        // id 不能为空
        if (!$param['id']) {
            throw new \Exception('id不能为空');
            return false;
        }

        $data = [
            'id' => $param['id'],
            'tenant_type' => $tenantType,
        ];
        if (isset($param['name'])) {
            $data['name'] = $param['name'];
        }
        if (isset($param['image_id'])) {
            $data['image_id'] = $param['image_id'];
        }
        if (isset($param['p_data'])) {
            $data['p_data'] = $param['p_data'];
        }
        if (isset($param['limit_stock'])) {
            // limit_stock 只能是 0 或 1
            if ($param['limit_stock'] != 0 && $param['limit_stock'] != 1) {
                throw new \Exception('limit_stock 只能是 0 或 1');
                return false;
            }
            $data['limit_stock'] = $param['limit_stock'];
        }
        if (isset($param['stock'])) {
            // limit_stock=1  stock 必须大于0
            if ($param['limit_stock'] == 1 && $param['stock'] <= 0) {
                throw new \Exception('limit_stock=1  stock 必须大于0');
                return false;
            }
            $data['stock'] = $param['stock'];
        }
        if (isset($param['per'])) {
            // per 中奖概率，必须大于0，小于等于100
            if ($param['per'] <= 0 || $param['per'] > 100) {
                throw new \Exception('per 中奖概率，必须大于0，小于等于100');
                return false;
            }
            $data['per'] = $param['per'];
        }
        if (isset($param['sort_order'])) {
            $data['sort_order'] = $param['sort_order'];
        }
        $config = config('admin.services');
        $res =  curlPostApiContentByUrlAndParams($config['Member']['BaseURL'] . $config['Member']['Api']['UpdateLotteryPrize'], $data, ['Authorization: Bearer ' . $token], true);
        if (empty($res)) {
            throw new \Exception('请求失败');
            return false;
        }
        if ($res['code'] != 1) {
            throw new \Exception($res['msg']);
            return false;
        }
        return true;
    }


    // 获取抽奖活动列表
    public function getLotteryPrizeList($tenantType, $token, $param = []): array
    {
        $data = [
            'tenant_type' => $tenantType,
            'pageNum' => $param['page_num'] ?? 1,
            'pageSize' => $param['pageSize'] ?? 10,
        ];
        if (isset($param['start_time'])) {
            $data['start_time'] = $param['start_time'];
        }
        if (isset($param['end_time'])) {
            $data['end_time'] = $param['end_time'];
        }
        if (isset($param['period'])) {
            $data['period'] = $param['period'];
        }
        if (isset($param['status'])) {
            $data['status'] = $param['status'];
        }
        $config = config('admin.services');
        $res =  curlGetApiContentByUrl($config['Member']['BaseURL'] . $config['Member']['Api']['GetLotteryList'], $data, ['Authorization: Bearer ' . $token]);
        if (empty($res)) {
            throw new \Exception('请求失败');
            return [];
        }
        if ($res['code'] != 1) {
            throw new \Exception($res['msg']);
            return [];
        }
        $imageIds = [];
        foreach ($res['data']['rows'] as $key => $value) {
            if (empty($value['prizes'])) {
                continue;
            }
            foreach ($value['prizes'] as $k => $v) {
                if ($v['image_id'] > 0) {
                    $imageIds[] = $v['image_id'];
                }
                if ($v['win_popup_image_id'] > 0) {
                    $imageIds[] = $v['image_id'];
                }
            }
        }
        if (!empty($imageIds)) {
            $file = new UploadService();
            $files = $file->getByIds($imageIds);
            foreach ($res['data']['rows'] as $key => &$value) {
                if (empty($value['prizes'])) {
                    continue;
                }
                foreach ($value['prizes'] as $k => &$v) {
                    if ($v['image_id'] > 0) {
                        $v['image_url'] = $files[$v['image_id']] ?? '';
                    }
                    if ($v['win_popup_image_id'] > 0) {
                        $v['win_popup_image_url'] = $files[$v['win_popup_image_id']] ?? '';
                    }
                }
            }
        }
        return $res['data'];
    }


    // 获取抽奖活动详情
    public function getLotteryPrizeDetail($tenantType, $token, $id): array
    {
        if (!$id) {
            throw new \Exception('id不能为空');
            return [];
        }
        $data = [
            'tenant_type' => $tenantType,
        ];
        $config = config('admin.services');
        $url = sprintf($config['Member']['BaseURL'] . $config['Member']['Api']['GetLotteryDetail'], $id);
        $res =  curlGetApiContentByUrl($url, $data, ['Authorization: Bearer ' . $token]);
        if (empty($res)) {
            throw new \Exception('请求失败');
            return [];
        }
        if ($res['code'] != 1) {
            throw new \Exception($res['msg']);
            return [];
        }
        $imageIds = [];

        foreach ($res['data']['prizes'] as $k => $v) {
            if ($v['image_id'] > 0) {
                $imageIds[] = $v['image_id'];
            }
            if ($v['win_popup_image_id'] > 0) {
                $imageIds[] = $v['image_id'];
            }
        }
        if (!empty($imageIds)) {
            $file = new UploadService();
            $files = $file->getByIds($imageIds);
            foreach ($res['data']['prizes'] as $k => &$v) {
                if ($v['image_id'] > 0) {
                    $v['image_url'] = $files[$v['image_id']] ?? '';
                }
                if ($v['win_popup_image_id'] > 0) {
                    $v['win_popup_image_url'] = $files[$v['win_popup_image_id']] ?? '';
                }
            }
        }
        return $res['data'];
    }

    public function deleteLottery($tenantType, $token, $id): bool
    {
        if (!$id) {
            throw new \Exception('id不能为空');
            return false;
        }
        $data = [
            'id' => $id,
            'tenant_type' => $tenantType,
        ];
        $config = config('admin.services');
        $url = sprintf($config['Member']['BaseURL'] . $config['Member']['Api']['DeleteLottery'], $id);
        $res =  curlGetApiContentByUrl($url, $data, ['Authorization: Bearer ' . $token], true);
        if (empty($res)) {
            throw new \Exception('请求失败');
            return false;
        }
        if ($res['code'] != 1) {
            throw new \Exception($res['msg']);
            return false;
        }
        return true;
    }

    public function deleteLotteryPrize($tenantType, $token, $id): bool
    {
        if (!$id) {
            throw new \Exception('id不能为空');
            return false;
        }
        $data = [
            'id' => $id,
            'tenant_type' => $tenantType,
        ];
        $config = config('admin.services');
        $url = sprintf($config['Member']['BaseURL'] . $config['Member']['Api']['DeleteLotteryPrize'], $id);
        $res =  curlGetApiContentByUrl($url, $data, ['Authorization: Bearer ' . $token], true);
        if (empty($res)) {
            throw new \Exception('请求失败');
            return false;
        }
        if ($res['code'] != 1) {
            throw new \Exception($res['msg']);
            return false;
        }
        return true;
    }

    public function getPrizeRecord($tenantType, $token, $param = []): array
    {
        $data = [
            'tenant_type' => $tenantType,
            'pageNum' => $param['page_num'] ?? 1,
            'pageSize' => $param['pageSize'] ?? 10,
        ];
        if (isset($param['period'])) {
            $data['period'] = $param['period'];
        }
        if (isset($param['status'])) {
            $data['status'] = $param['status'];
        }
        if (isset($param['send_status'])) {
            $data['send_status'] = $param['send_status'];
        }
        if (isset($param['source_id']) && $param['source_id'] > 0) {
            $data['source_type'] = 'activity';
            $data['source_id'] = $param['source_id'];

            if (isset($param['contact_mobile'])) {
                $signup = ActivitySignupModel::where(['activity_id' => $param['source_id'], 'contact_mobile' => $param['contact_mobile']])
                    ->field('member_id')
                    ->select()
                    ->toArray();
                $memberIds = array_column($signup, 'member_id');
                if (empty($memberIds)) {
                    $data['member_ids'] = "0";
                } else {
                    $data['member_ids'] = implode(',', $memberIds);
                }
            }

            if (isset($param['verification_code'])) {
                $data['verification_code'] = $param['verification_code'];
            }
        }
        $config = config('admin.services');
        $res =  curlGetApiContentByUrl($config['Member']['BaseURL'] . $config['Member']['Api']['GetMemberPrizeList'], $data, ['Authorization: Bearer ' . $token]);
        if (empty($res)) {
            throw new \Exception('请求失败');
            return [];
        }
        if ($res['code'] != 1) {
            throw new \Exception($res['msg']);
            return [];
        }
        // 获取活动
        if (!empty($res['data']['rows'])) {
            $sourceIds = [];
            $memberIds = [];
            foreach ($res['data']['rows'] as $k => $v) {
                if ($v['source_type'] == 'activity' && $v['source_id'] > 0) {
                    array_push($sourceIds, $v['source_id']);
                    array_push($memberIds, $v['member_id']);
                }
            }
            if (!empty($sourceIds)) {
                $signup = ActivitySignupModel::whereIn('activity_id', $sourceIds)->whereIn('member_id', $memberIds)->field('activity_id,member_id,contact_name,contact_mobile,contact_wechat,status')->select()->toArray();
                $signupData = [];
                foreach ($signup as $v) {
                    $signupData[$v['activity_id'] . "_" . $v['member_id']] = $v;
                }
                foreach ($res['data']['rows'] as $k => &$v) {
                    if (!isset($signupData[$v['source_id'] . "_" . $v['member_id']])) {
                        continue;
                    }
                    $v['contact_name'] = $signupData[$v['source_id'] . "_" . $v['member_id']]['contact_name'] ?? '';
                    $v['contact_mobile'] = $signupData[$v['source_id'] . "_" . $v['member_id']]['contact_mobile'] ?? '';
                    $v['contact_wechat'] = $signupData[$v['source_id'] . "_" . $v['member_id']]['contact_wechat'] ?? '';
                }
            }
        }
        return $res['data'];
    }

    public function prizeRecordUpdate($tenantType, $token, $param = []): bool
    {
        if (!isset($param['id'])) {
            throw new \Exception('id不能为空');
            return false;
        }
        $data = [
            'tenant_type' => $tenantType,
            'id' => $param['id'],
        ];
        if (isset($param['verification_code'])) {
            $data['verification_code'] = $param['verification_code'];
        }
        $config = config('admin.services');
        $res =  curlPostApiContentByUrlAndParams($config['Member']['BaseURL'] . $config['Member']['Api']['MemberPrizeSend'], $data, ['Authorization: Bearer ' . $token], true);
        if (empty($res)) {
            throw new \Exception('请求失败');
            return false;
        }
        if ($res['code'] != 1) {
            throw new \Exception($res['msg']);
            return false;
        }
        return true;
    }
}
