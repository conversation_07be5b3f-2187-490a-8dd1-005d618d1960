<?php

declare(strict_types=1);

namespace app\Common\command;

use app\api\service\Order\OrderSer;
use app\common\model\OrderPaymentsModel;
use think\console\Command;
use think\console\Input;
use think\console\Output;

use think\facade\Log;

class PayTimeout extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('pay timeout')
            ->setDescription('用户生成支付信息，又不支付，则超时半个小时自动取消订单');
    }

    protected function execute(Input $input, Output $output)
    {
        // 查询超时的支付计划
        $timeoutPayments = OrderPaymentsModel::where('status', 'pending')
            ->where('create_time', '<', date('Y-m-d H:i:s', time() - 1800))
            ->field('id, order_id')
            ->order('id', 'asc')
            ->limit(100)
            ->select()
            ->toArray();

        if (empty($timeoutPayments)) {
            $output->writeln('No timeout payments found');
            return;
        }

        $output->writeln('Found ' . count($timeoutPayments) . ' timeout payments');

        // 处理超时的支付计划
        foreach ($timeoutPayments as $payment) {
            $output->writeln('Processing payment ID: ' . $payment['id'] . ', order ID: ' . $payment['order_id']);

            $eMsg = '';
            $param = [
                'order_id' => $payment['order_id'],
                'reason' => '支付超时,自动取消',
            ];

            // 取消订单
            $ret = OrderSer::orderCancel($param, $eMsg);

            if ($ret) {
                $output->writeln('Order cancelled successfully: ' . $payment['order_id']);
                Log::info('Payment timeout: Order cancelled successfully: ' . $payment['order_id']);
            } else {
                $output->writeln('Failed to cancel order: ' . $payment['order_id'] . ', error: ' . $eMsg);
                Log::error('Payment timeout: Failed to cancel order: ' . $payment['order_id'] . ', error: ' . $eMsg);
            }
        }

        $output->writeln('Payment timeout check completed');
    }
}
