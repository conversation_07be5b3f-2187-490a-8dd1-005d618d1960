<?php

namespace app\common\enum;

class Response
{
    const SUCCESS = 1;
    const ERROR   = 0;

    //请求异常
    const REQUEST_ERROR = 50000;
    //非法请求被禁止
    const ILLEGAL_REQUEST = 50001;
    //请求参数错误
    const REQUEST_PARAM_ERROR = 50002;
    //model层错误
    const MODEL_ERROR = 50003;
    //短信发送失败
    const SMS_SEND_ERROR = 50004;
    // 找不到数据
    const DATA_NOT_FOUND = 50005;
    // 上传图片失败
    const UPLOAD_IMAGE_FAIL = 50006;
    // 上传文件不存在
    const UPLOAD_FILE_NOT_EXIST = 50007;

    //未登录
    const NOT_LOGIN = 40001;
    //没有权限
    const NOT_PERMISSION = 40002;
    //黑名单ip Forbidden
    const FORBIDDEN_IP = 40003;

    const TOKEN_CREATE_ERROR = 10000;
    // 账号密码错误
    const ACCOUNT_PASSWORD_ERROR = 10001;
    // 您的账户已被禁用
    const ACCOUNT_FORBIDDEN = 10002;
    // 您的账户不可编辑
    const ACCOUNT_EDIT_FORBIDDEN = 10003;
    // 注册失败
    const REGISTER_FAIL = 10004;
    // 修改密码失败
    const CHANGE_PASSWORD_FAIL = 10005;
    const ACCOUNT_EXPIRE       = 10006;
    // 提现失败
    const CASH_FAIL = 10007;
    // 更新账户失败
    const ACCOUNT_UPDATE_FAIL = 10008;
    // 余额不足
    const ACCOUNT_BALANCE_NOT_ENOUGH = 10009;
    // 账户不存在
    const ACCOUNT_NOT_EXIST = 10010;
    // 服务操作失败
    const SERVICE_FAIL = 10011;


    // 权限不存在
    const PERMISSION_NOT_EXIST = 20001;
    const PERMISSION_EXIST     = 20002;
    const PERMISSION_SAVE_FAIL = 20003;
    // 角色不存在
    const ROLE_NOT_EXIST = 20004;
    const ROLE_EXIST     = 20005;
    const ROLE_SAVE_FAIL = 20006;
    // 管理员不存在
    const ADMIN_NOT_EXIST = 20007;
    const ADMIN_EXIST     = 20008;
    const ADMIN_SAVE_FAIL = 20009;

    // 门店相关
    const BANNER_NOT_EXIST = 30001;
    const BANNER_EXIST     = 30002;
    const BANNER_SAVE_FAIL = 30003;
    const BANNER_LIMIT     = 30004;
    const RULE_NOT_EXIST   = 30005;
    const RULE_EXIST       = 30006;
    const RULE_SAVE_FAIL   = 30007;
    // 规则已经存在
    const RULE_EXISTED = 30012;
    // 企业认证失败
    const COMPANY_AUTH_FAIL = 30013;

    // 共享员工操作失败
    const SHARE_STAFF_FAIL = 30008;
    // 提现审核失败
    const CASH_CHECK_FAIL = 30009;
    const TAG_SAVE_FAIL   = 30010;
    const TAG_NOT_EXIST   = 30011;


    //商品ID不存在
    const GOODS_ID_NOT_EXIST     = 30101;
    const GOODS_INFO_UPDATE_FAIL = 30102;

    //订单评论初始化失败
    const ORDER_COMMENT_INIT_FAIL = 30103;

    // 800+ 用户端错误
    const WX_LOGIN_ERROR    = 80001;
    const MEMBER_NOT_EXIST  = 80002;
    const MEMBER_NOT_WORK   = 80003;
    const MEMBER_LOGIN_FAIL = 80004;
    const MEMBER_NOT_LOGIN  = 80005;
    // 添加地址失败
    const ADDRESS_ADD_FAIL = 80006;
    // 修改地址失败
    const ADDRESS_EDIT_FAIL = 80007;
    // 删除地址失败
    const ADDRESS_DEL_FAIL = 80008;
    // 商品库存不足
    const GOODS_STOCK_NOT_ENOUGH = 80009;
    // 商品起订量变更
    const GOODS_MIN_ORDER_CHANGE = 80010;

    // 订单请勿重复发送请求
    const ORDER_REPEAT_SEND = 80011;
    //不支持的支付方式
    const PAY_TYPE_NOT_SUPPORT = 80012;
    //订单请添加收货地址
    const ORDER_ADD_ADDRESS = 80013;
    //开票信息错误
    const ORDER_INVOICE_ERROR = 80014;
    // 无效员工
    const INVALID_STAFF = 80015;

    const GOODS_SERVICE_DATE_CHANGE = 80016;
    const GOODS_AREA_CHANGE         = 80017;

    // 取消订单失败
    const ORDER_CANCEL_FAIL = 80018;

    const INVALID_INTERVIEW_TIME = 80019;
    // 创建面试失败
    const INTERVIEW_CREATE_FAIL = 80020;

    // 确认订单失败
    const ORDER_CONFIRM_FAIL = 80021;
    const ORDER_CART_EMPTY   = 80022;

    const MEMBER_EDIT_FAIL = 80023;

    // 冻结库存失败
    const STOCK_FREEZE_FAIL = 80024;
    // 冻结库存失败(参数问题)
    const STOCK_FREEZE_FAIL_PARAM = 80025;
    // 库存回滚失败
    const STOCK_ROLLBACK_FAIL       = 80026;
    const STOCK_ROLLBACK_FAIL_PARAM = 80027;

    // 收藏失败
    const MEMBER_COLLECT_FAIL     = 80028;
    const INTERVIEW_KICK_USER_OUT = 80029;
    const ORDER_REFUND_APPLY_FAIL = 80030;

    // 活动出错了
    const ACTIVITY_ERROR = 80038;
    // 活动超时
    const ACTIVITY_TIMEOUT = 80031;
    // 已报名，不要重复报名
    const ACTIVITY_REPEAT_SIGN_UP = 80032;

    // 合同模版不存在
    const CONTRACT_TEMPLATE_NOT_EXIST = 80033;
    // 合同模版删除失败
    const CONTRACT_TEMPLATE_DEL_FAIL = 80034;
    // 合同模版新增失败
    const CONTRACT_TEMPLATE_ADD_FAIL = 80035;
    // 合同模版编辑失败
    const CONTRACT_TEMPLATE_EDIT_FAIL = 80036;
    const CONTRACT_TEMPLATE_AUDIT_FAIL = 80037;






    const ResponseCode = [
        self::SUCCESS                    => '请求成功',
        self::ERROR                      => '请求失败',

        //500+ 系统相关
        self::ILLEGAL_REQUEST            => '非法请求被禁止',
        self::REQUEST_PARAM_ERROR        => '请求参数错误',
        self::MODEL_ERROR                => 'model层错误',
        self::SMS_SEND_ERROR             => '短信发送失败',
        self::DATA_NOT_FOUND             => '找不到数据',
        self::UPLOAD_IMAGE_FAIL          => '上传图片失败',
        self::UPLOAD_FILE_NOT_EXIST      => '上传文件不存在',

        //400+ 检查登录相关
        self::NOT_LOGIN                  => '未登录',
        self::NOT_PERMISSION             => '没有权限',
        self::FORBIDDEN_IP               => 'SDK Forbidden,请添加IP至白名单',

        //100+ 用户相关
        self::TOKEN_CREATE_ERROR         => 'token生成失败',
        self::ACCOUNT_PASSWORD_ERROR     => '账号密码错误',
        self::ACCOUNT_FORBIDDEN          => '您的账户已被禁用',
        self::ACCOUNT_EDIT_FORBIDDEN     => '您的账户不可编辑',
        self::REGISTER_FAIL              => '注册失败',
        self::CHANGE_PASSWORD_FAIL       => '修改密码失败',
        self::ACCOUNT_EXPIRE             => '账号已过期',
        self::CASH_FAIL                  => '提现失败',
        self::ACCOUNT_UPDATE_FAIL        => '更新账户失败',
        self::ACCOUNT_BALANCE_NOT_ENOUGH => '余额不足',
        self::ACCOUNT_NOT_EXIST          => '账户不存在',
        self::SERVICE_FAIL               => '服务操作失败',

        //200+ 权限相关
        self::PERMISSION_NOT_EXIST       => '权限不存在',
        self::PERMISSION_EXIST           => '权限已存在',
        self::PERMISSION_SAVE_FAIL       => '权限保存失败',
        self::ROLE_NOT_EXIST             => '角色不存在',
        self::ROLE_EXIST                 => '角色已存在',
        self::ROLE_SAVE_FAIL             => '角色保存失败',
        self::ADMIN_NOT_EXIST            => '管理员不存在',
        self::ADMIN_EXIST                => '管理员已存在',
        self::ADMIN_SAVE_FAIL            => '管理员保存失败',

        //300+ 门店相关
        self::BANNER_NOT_EXIST           => 'banner不存在',
        self::BANNER_EXIST               => 'banner已存在',
        self::BANNER_SAVE_FAIL           => 'banner保存失败',
        self::BANNER_LIMIT               => 'banner数量已达到上限',
        self::RULE_NOT_EXIST             => '规则不存在',
        self::RULE_EXIST                 => '规则已存在',
        self::RULE_SAVE_FAIL             => '规则保存失败',
        self::RULE_EXISTED               => '规则已存在',
        self::COMPANY_AUTH_FAIL          => '公司认证失败',
        self::SHARE_STAFF_FAIL           => '共享员工操作失败',
        self::CASH_CHECK_FAIL            => '提现审核失败',
        self::TAG_SAVE_FAIL              => '标签保存失败',
        self::TAG_NOT_EXIST              => '标签不存在',


        self::GOODS_ID_NOT_EXIST     => '商品ID不存在',
        self::GOODS_INFO_UPDATE_FAIL => '商品编辑失败',

        self::WX_LOGIN_ERROR    => '微信登录失败',
        self::MEMBER_NOT_EXIST  => '用户不存在',
        self::MEMBER_NOT_WORK   => '用户未激活',
        self::MEMBER_LOGIN_FAIL => '用户登录失败',
        self::MEMBER_NOT_LOGIN  => '用户未登录',
        self::ADDRESS_ADD_FAIL  => '添加地址失败',
        self::ADDRESS_EDIT_FAIL => '修改地址失败',
        self::ADDRESS_DEL_FAIL  => '删除地址失败',

        self::GOODS_STOCK_NOT_ENOUGH    => '商品库存不足',
        self::GOODS_MIN_ORDER_CHANGE    => '商品起订量变更',
        self::ORDER_REPEAT_SEND         => '订单请勿重复发送请求',
        self::PAY_TYPE_NOT_SUPPORT      => '支付方式不支持',
        self::ORDER_ADD_ADDRESS         => '订单请添加收货地址',
        self::ORDER_INVOICE_ERROR       => '发票信息错误',
        self::INVALID_STAFF             => '无效的员工',
        self::GOODS_SERVICE_DATE_CHANGE => '商品服务日期变更',
        self::GOODS_AREA_CHANGE         => '商品服务区域变更',
        self::ORDER_CANCEL_FAIL         => '取消订单失败',
        self::INVALID_INTERVIEW_TIME    => '无效的面试时间',
        self::INTERVIEW_CREATE_FAIL     => '创建面试失败',
        self::INTERVIEW_KICK_USER_OUT   => '成员踢出面试房间失败',
        self::ORDER_CONFIRM_FAIL        => '确认订单失败',
        self::ORDER_CART_EMPTY          => '购物车为空',

        self::ORDER_COMMENT_INIT_FAIL   => '订单评论初始化失败，原因：已经初始化或订单不存在',
        self::MEMBER_EDIT_FAIL          => '用户编辑失败',
        self::STOCK_FREEZE_FAIL         => '库存冻结失败',
        self::STOCK_ROLLBACK_FAIL       => '库存回滚失败',
        self::STOCK_FREEZE_FAIL_PARAM   => '库存冻结失败',
        self::STOCK_ROLLBACK_FAIL_PARAM => '库存回滚失败',
        self::MEMBER_COLLECT_FAIL       => '用户收藏操作失败',
        self::ORDER_REFUND_APPLY_FAIL   => '订单退款申请失败',

        self::ACTIVITY_TIMEOUT      => '活动已结束',
        self::ACTIVITY_REPEAT_SIGN_UP => '活动重复报名',

        self::CONTRACT_TEMPLATE_NOT_EXIST => '合同模板不存在',
        self::CONTRACT_TEMPLATE_DEL_FAIL => '合同模板删除失败',
        self::CONTRACT_TEMPLATE_ADD_FAIL => '合同模板新增失败',
        self::CONTRACT_TEMPLATE_EDIT_FAIL => '合同模板编辑失败',
        self::CONTRACT_TEMPLATE_AUDIT_FAIL => '合同模板审核失败',
    ];
}
