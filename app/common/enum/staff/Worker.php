<?php

namespace app\common\enum\staff;

use think\facade\Config;
use think\facade\Db;

/**
 * 职工类型
 */
class Worker
{
    public static function WorkerCode()
    {
        $rows = Db::table('clark_type')
            ->field('id,name')
            ->order('id', 'asc')
            ->select()
            ->toArray();
        return array_column($rows, 'name', 'id');
    }

    public static function getList($type = "")
    {
        $limit = $type == "index" ? 5 : 99;
        $rows = Db::table('clark_type')
            ->field('id,name,url,type')
            ->order('sort', 'asc')
            ->limit($limit)
            ->select()
            ->toArray();
        if ($rows) {
            foreach ($rows as $key => &$value) {
                if (!empty($value['url'])) {
                    $domain       = Config::get('upload.ysa.domain');
                    $value['url'] = $domain . 'worker/' . $value['url'] . '.png';
                }
            }
            unset($value);
        }
        // if ($type == "index") {
        //     array_push($rows, [
        //         "id" => 0,
        //         "name" => "其他",
        //         "url" => "",
        //         "type" => ""
        //     ]);
        // }
        return $rows;
    }
}
