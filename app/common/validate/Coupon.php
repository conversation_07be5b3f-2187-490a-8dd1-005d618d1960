<?php
declare(strict_types=1);

namespace app\common\validate;


use think\Validate;

class Coupon extends Validate
{

    /**
     * 定义验证规则
     * 格式：'字段名' =>  ['规则1','规则2'...]
     *
     * @var array
     */
    protected $rule = [
        'id'          => 'require|integer',
        'template_id' => 'require|integer',
        'shop_id'     => 'require|integer',
        'member_id'   => 'require|integer',
        'price'       => 'require|float',
    ];

    /**
     * 定义错误信息
     * 格式：'字段名.规则名' =>  '错误信息'
     *
     * @var array
     */
    protected $message = [
        'id.require'          => 'ID不能为空',
        'id.integer'          => 'ID必须是整数',
        'template_id.require' => '模板ID不能为空',
        'template_id.integer' => '模板ID必须是整数',
        'shop_id.require'     => '门店ID不能为空',
        'shop_id.integer'     => '门店ID必须是整数',
        'member_id.require'   => '用户ID不能为空',
        'member_id.integer'   => '用户ID必须是整数',
        'order_id.require'    => '订单ID不能为空',
        'order_id.integer'    => '订单ID必须是整数',
        'status.require'      => '优惠券状态不能为空',
        'price.require'       => 'price不能为空',
    ];


    protected $scene = [
        'id'                => ['id'],
        'createCoupon'      => ['template_id', 'member_id'],
        'memberId'          => ['member_id'],
        'updateCoupon'      => ['member_id', 'order_id', 'status'],
        'couponListByOrder' => ['member_id', 'price'],
    ];
}
