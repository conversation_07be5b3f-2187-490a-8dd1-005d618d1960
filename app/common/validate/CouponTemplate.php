<?php
declare(strict_types=1);

namespace app\common\validate;


use think\Validate;

class CouponTemplate extends Validate
{

    /**
     * 定义验证规则
     * 格式：'字段名' =>  ['规则1','规则2'...]
     *
     * @var array
     */
    protected $rule = [
        'name'             => 'require|max:50',
        'type'             => 'require|in:PLATFORM,SHOP',
        'amount'           => 'require|float|gt:0',
        'min_order_amount' => 'require|float|egt:0',
        'total_count'      => 'require|integer|egt:-1',
        'user_limit'       => 'require|integer|gt:0',
        'cost_ratio'       => 'require|regex:/^\d+:\d+$/',
        'valid_type'       => 'require|in:1,2',
        'start_time'       => 'requireIf:valid_type,1|dateFormat:Y-m-d H:i:s',
        'end_time'         => 'requireIf:valid_type,1|dateFormat:Y-m-d H:i:s|checkEndTime',
        'valid_days'       => 'requireIf:valid_type,2|integer|gt:0',
        'scope_type'       => 'require|in:1,2,3',
        'shop_id'          => 'requireIf:type,SHOP|number',
        'status'           => 'require',
    ];

    /**
     * 定义错误信息
     * 格式：'字段名.规则名' =>  '错误信息'
     *
     * @var array
     */
    protected $message = [
        'name.require'             => '模板名称不能为空',
        'name.max'                 => '模板名称最多不能超过50个字符',
        'type.require'             => '优惠券类型不能为空',
        'type.in'                  => '优惠券类型只能是平台券或店铺券',
        'amount.require'           => '面值不能为空',
        'amount.float'             => '面值必须是数字',
        'amount.gt'                => '面值必须是必须大于0',
        'min_order_amount.require' => '使用门槛金额不能为空',
        'min_order_amount.float'   => '使用门槛金额必须是数字',
        'min_order_amount.egt'     => '使用门槛金额必须大于等于0',
        'total_count.require'      => '总发放数量不能为空',
        'total_count.integer'      => '总发放数量必须是整数',
        'total_count.egt'          => '总发放数量必须大于等于-1',
        'user_limit.require'       => '单用户领取上限不能为空',
        'user_limit.integer'       => '单用户领取上限必须是整数',
        'user_limit.gt'            => '单用户领取上限必须大于0',
        'cost_ratio.require'       => '成本分摊比例不能为空',
        'cost_ratio.regex'         => '成本分摊比例格式错误，正确格式如：70:30',
        'valid_type.require'       => '有效期类型不能为空',
        'valid_type.in'            => '有效期类型只能是固定时间或领取后N天有效',
        'start_time.requireIf'     => '固定有效期开始时间不能为空',
        'start_time.dateFormat'    => '固定有效期开始时间格式错误',
        'end_time.requireIf'       => '固定有效期结束时间不能为空',
        'end_time.dateFormat'      => '固定有效期结束时间格式错误',
        'valid_days.requireIf'     => '领取后有效天数不能为空',
        'valid_days.integer'       => '领取后有效天数必须是整数',
        'valid_days.gt'            => '领取后有效天数必须大于0',
        'scope_type.require'       => '适用范围不能为空',
        'scope_type.in'            => '适用范围只能是全场通用、指定品类或指定商品',
        'shop_id.requireIf'        => '店铺券必须关联商家ID',
        'shop_id.number'           => '商家ID必须是数字',
        'status.require'           => '状态不能为空',

    ];

    /**
     * 验证结束时间必须大于开始时间
     * @param $value
     * @param $rule
     * @param $data
     * @return bool|string
     */
    protected function checkEndTime($value, $rule, $data = [])
    {
        if (isset($data['start_time']) && strtotime($value) <= strtotime($data['start_time'])) {
            return '结束时间必须大于开始时间';
        }
        return true;
    }

    protected $scene = [
        'id'                         => ['id'],
        'createCouponTemplate'       => ['type', 'name', 'shop_id', 'amount', 'min_order_amount', 'total_count', 'user_limit', 'cost_ratio', 'valid_type', 'start_time', 'end_time', 'valid_days', 'scope_type'],
        'updateCouponTemplate'       => ['id', 'type', 'name', 'shop_id', 'amount', 'min_order_amount', 'total_count', 'user_limit', 'cost_ratio', 'valid_type', 'start_time', 'end_time', 'valid_days', 'scope_type'],
        'updateCouponTemplateStatus' => ['id', 'status'],
    ];
}
