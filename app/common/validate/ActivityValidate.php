<?php

declare(strict_types=1);

namespace app\common\validate;

use think\Validate;

class ActivityValidate extends Validate
{
    protected $rule = [
        'id'             => 'require|integer', // 活动ID，必填且必须是整数
        'type'           => 'require|in:1,2', // 活动类型：1=店铺活动，2=平台活动
        'shop_id'        => 'requireIf:type,1|integer', // 店铺ID，只有在店铺活动时必填
        'title'          => 'require|max:100', // 活动标题，必填，最大长度100
        'cover_image_id' => 'require|integer', // 活动封面图ID
        'start_date'     => 'require|date', // 开始日期，必填，必须是有效日期
        'end_date'       => 'require|date|egt:start_date', // 结束日期，必填，必须是有效日期且大于开始日期
        'start_time'     => 'require|dateFormat:H:i:s', // 开始时间，必填，格式为H:i
        //'end_time'       => 'require|dateFormat:H:i|gt:start_time', // 结束时间，必填，格式为H:i且大于开始时间
        'end_time'       => 'require|dateFormat:H:i:s', // 结束时间，必填，格式为H:i
        'province_code'  => 'require|max:20', // 省份编码，必填
        'province_name'  => 'require|max:50', // 省份名称，必填
        'city_code'      => 'require|max:20', // 城市编码，必填
        'city_name'      => 'require|max:50', // 城市名称，必填
        'address'        => 'require|max:255', // 活动地址，必填，最大长度255
        'content'        => 'require', // 活动内容，必填
        'init_join_num'  => 'integer|>=:0', // 初始参与人数，非负整数
        'sort'           => 'integer|>=:0', // 排序，非负整数

        'activity_id'    => 'require|integer', // 活动ID，必填且必须是整数
        'member_id'      => 'integer', // 用户ID，必填且必须是整数
        'contact_name'   => 'require|max:50', // 联系人姓名，必填且最大长度为50
        'contact_mobile' => 'require|regex:/^1[3-9]\d{9}$/', // 联系人电话，必填且必须符合手机号格式
        'contact_wechat' => 'require|max:50', // 联系人微信号，最大长度为50
        'remark'         => 'max:255' // 备注信息，最大长度为255
    ];

    protected $message = [
        'id.require'             => '活动ID不能为空',
        'id.integer'             => '活动ID必须是整数',
        'type.require'           => '活动类型不能为空',
        'type.in'                => '活动类型不合法',
        'shop_id.requireIf'      => '店铺ID不能为空',
        'shop_id.integer'        => '店铺ID必须是整数',
        'title.require'          => '活动标题不能为空',
        'title.max'              => '活动标题不能超过100个字符',
        'cover_image_id.require' => '活动封面图不能为空',
        'cover_image_id.integer' => '活动封面图必须是数字',
        'start_date.require'     => '开始日期不能为空',
        'start_date.date'        => '开始日期格式不正确',
        'end_date.require'       => '结束日期不能为空',
        'end_date.date'          => '结束日期格式不正确',
        'end_date.egt'           => '结束日期必须大于等于开始日期',
        'start_time.require'     => '开始时间不能为空',
        'start_time.dateFormat'  => '开始时间格式不正确',
        'end_time.require'       => '结束时间不能为空',
        'end_time.dateFormat'    => '结束时间格式不正确',
        'province_code.require'  => '省份编码不能为空',
        'province_code.max'      => '省份编码不能超过20个字符',
        'province_name.require'  => '省份名称不能为空',
        'province_name.max'      => '省份名称不能超过50个字符',
        'city_code.require'      => '城市编码不能为空',
        'city_code.max'          => '城市编码不能超过20个字符',
        'city_name.require'      => '城市名称不能为空',
        'city_name.max'          => '城市名称不能超过50个字符',
        'address.require'        => '活动地址不能为空',
        'address.max'            => '活动地址不能超过255个字符',
        'content.require'        => '活动内容不能为空',
        'init_join_num.integer'  => '初始参与人数必须是整数',
        'init_join_num.>=:0'     => '初始参与人数不能小于0',
        'sort.integer'           => '排序必须是整数',
        'sort.>=:0'              => '排序不能小于0',

        'activity_id.require'    => '活动ID不能为空',
        'activity_id.integer'    => '活动ID必须是整数',
        // 'member_id.require'      => '用户ID不能为空',
        'member_id.integer'      => '用户ID必须是整数',
        'contact_name.require'   => '联系人姓名不能为空',
        'contact_name.max'       => '联系人姓名不能超过50个字符',
        'contact_mobile.require' => '联系人电话不能为空',
        'contact_mobile.regex'   => '联系人电话格式不正确',
        'contact_wechat.require' => '联系人微信号不能不能为空',
        'contact_wechat.max'     => '联系人微信号不能超过50个字符',
        'remark.max'             => '备注信息不能超过255个字符',

    ];

    protected $scene = [
        'create' => ['type', 'shop_id', 'title', 'cover_image_id', 'start_date', 'end_date', 'start_time', 'end_time', 'province_code', 'province_name', 'city_code', 'city_name', 'address', 'content', 'init_join_num', 'sort'],
        'signup' => ['activity_id', 'member_id', 'contact_name', 'contact_mobile', 'contact_wechat'],
        'detailApi' => ['id', 'member_id'],
        'detailSDK' => ['id'],
    ];
}
