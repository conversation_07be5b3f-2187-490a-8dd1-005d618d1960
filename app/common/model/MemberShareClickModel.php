<?php

namespace app\common\model;

class MemberShareClickModel extends BaseModel
{
    protected $name = 'member_share_clicks';
    protected $pk = 'id';

    // 网络类型
    const NETWORK_WIFI = 'WiFi';
    const NETWORK_4G = '4G';
    const NETWORK_5G = '5G';
    const NETWORK_3G = '3G';
    const NETWORK_2G = '2G';
    const NETWORK_OTHER = 'Other';

    const NETWORK_TEXT = [
        self::NETWORK_WIFI => 'WiFi',
        self::NETWORK_4G => '4G',
        self::NETWORK_5G => '5G',
        self::NETWORK_3G => '3G',
        self::NETWORK_2G => '2G',
        self::NETWORK_OTHER => '其他',
    ];

    // 转化状态
    const CONVERSION_STATUS_NONE = 0;      // 未转化
    const CONVERSION_STATUS_CONVERTED = 1; // 已转化

    const CONVERSION_STATUS_TEXT = [
        self::CONVERSION_STATUS_NONE => '未转化',
        self::CONVERSION_STATUS_CONVERTED => '已转化',
    ];

    // 转化类型
    const CONVERSION_TYPE_ORDER = 'order';        // 下单
    const CONVERSION_TYPE_REGISTER = 'register';  // 注册
    const CONVERSION_TYPE_CONTACT = 'contact';    // 留资
    const CONVERSION_TYPE_COLLECT = 'collect';    // 收藏
    const CONVERSION_TYPE_VIEW = 'view';          // 浏览

    const CONVERSION_TYPE_TEXT = [
        self::CONVERSION_TYPE_ORDER => '下单',
        self::CONVERSION_TYPE_REGISTER => '注册',
        self::CONVERSION_TYPE_CONTACT => '留资',
        self::CONVERSION_TYPE_COLLECT => '收藏',
        self::CONVERSION_TYPE_VIEW => '浏览',
    ];

    /**
     * 关联分享记录
     */
    public function share()
    {
        return $this->belongsTo(MemberShareModel::class, 'share_id', 'share_id');
    }

    /**
     * 关联点击用户
     */
    public function member()
    {
        return $this->belongsTo(MemberModel::class, 'member_id', 'id');
    }

    /**
     * 获取网络类型文本
     */
    public function getNetworkTypeTextAttr($value, $data)
    {
        return self::NETWORK_TEXT[$data['network_type']] ?? '未知';
    }

    /**
     * 获取转化状态文本
     */
    public function getConversionStatusTextAttr($value, $data)
    {
        return self::CONVERSION_STATUS_TEXT[$data['conversion_status']] ?? '未知';
    }

    /**
     * 获取转化类型文本
     */
    public function getConversionTypeTextAttr($value, $data)
    {
        return self::CONVERSION_TYPE_TEXT[$data['conversion_type']] ?? '';
    }

    /**
     * 获取格式化的点击时间
     */
    public function getFormatClickTimeAttr($value, $data)
    {
        return date('Y-m-d H:i:s', strtotime($data['click_time']));
    }

    /**
     * 获取格式化的转化时间
     */
    public function getFormatConversionTimeAttr($value, $data)
    {
        return !empty($data['conversion_time']) ? date('Y-m-d H:i:s', strtotime($data['conversion_time'])) : '';
    }

    /**
     * 检查是否为新用户
     */
    public function isNewUser()
    {
        return $this->is_new_user == 1;
    }

    /**
     * 检查是否为首次点击
     */
    public function isFirstClick()
    {
        return $this->is_first_click == 1;
    }

    /**
     * 检查是否已转化
     */
    public function isConverted()
    {
        return $this->conversion_status == self::CONVERSION_STATUS_CONVERTED;
    }

    /**
     * 设置转化信息
     */
    public function setConversion($type, $id = null)
    {
        $this->conversion_status = self::CONVERSION_STATUS_CONVERTED;
        $this->conversion_time = date('Y-m-d H:i:s');
        $this->conversion_type = $type;
        if ($id) {
            $this->conversion_id = $id;
        }
        return $this->save();
    }
}
