<?php
declare(strict_types=1);

namespace app\common\model;

class ActivitySignupShopChoicesModel extends BaseModel
{
    protected $name = 'activity_signup_shop_choices';
    protected $pk = 'id';

    // 定义与报名记录的关联
    public function signup()
    {
        return $this->belongsTo(ActivitySignupModel::class, 'signup_id');
    }

    // 定义与门店的关联
    public function shop()
    {
        return $this->belongsTo(ShopModel::class, 'shop_id');
    }
}
