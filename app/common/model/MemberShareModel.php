<?php

namespace app\common\model;

class MemberShareModel extends BaseModel
{
    protected $name = 'member_shares';
    protected $pk = 'id';

    // 分享内容类型
    const SHARE_TYPE_ARTICLE = 1;    // 文章
    const SHARE_TYPE_SHOP = 2;       // 门店
    const SHARE_TYPE_GOODS = 3;      // 商品
    const SHARE_TYPE_ACTIVITY = 4;   // 活动
    const SHARE_TYPE_OTHER = 5;      // 其他

    const SHARE_TYPE_TEXT = [
        self::SHARE_TYPE_ARTICLE => '文章',
        self::SHARE_TYPE_SHOP => '门店',
        self::SHARE_TYPE_GOODS => '商品',
        self::SHARE_TYPE_ACTIVITY => '活动',
        self::SHARE_TYPE_OTHER => '其他',
    ];

    // 分享渠道
    const SHARE_CHANNEL_WECHAT_FRIEND = 1;  // 微信好友
    const SHARE_CHANNEL_WECHAT_MOMENT = 2;  // 朋友圈
    const SHARE_CHANNEL_QQ = 3;             // QQ
    const SHARE_CHANNEL_WEIBO = 4;          // 微博
    const SHARE_CHANNEL_QRCODE = 5;         // 二维码
    const SHARE_CHANNEL_OTHER = 6;          // 其他

    const SHARE_CHANNEL_TEXT = [
        self::SHARE_CHANNEL_WECHAT_FRIEND => '微信好友',
        self::SHARE_CHANNEL_WECHAT_MOMENT => '朋友圈',
        self::SHARE_CHANNEL_QQ => 'QQ',
        self::SHARE_CHANNEL_WEIBO => '微博',
        self::SHARE_CHANNEL_QRCODE => '二维码',
        self::SHARE_CHANNEL_OTHER => '其他',
    ];

    // 状态
    const STATUS_INVALID = 0;    // 已失效
    const STATUS_VALID = 1;      // 有效
    const STATUS_DISABLED = 2;   // 管理员禁用

    const STATUS_TEXT = [
        self::STATUS_INVALID => '已失效',
        self::STATUS_VALID => '有效',
        self::STATUS_DISABLED => '管理员禁用',
    ];

    // 设备平台
    const PLATFORM_IOS = 'iOS';
    const PLATFORM_ANDROID = 'Android';
    const PLATFORM_WINDOWS = 'Windows';
    const PLATFORM_MAC = 'Mac';
    const PLATFORM_OTHER = 'Other';

    const PLATFORM_TEXT = [
        self::PLATFORM_IOS => 'iOS',
        self::PLATFORM_ANDROID => 'Android',
        self::PLATFORM_WINDOWS => 'Windows',
        self::PLATFORM_MAC => 'Mac',
        self::PLATFORM_OTHER => '其他',
    ];

    /**
     * 关联分享点击记录
     */
    public function clicks()
    {
        return $this->hasMany(MemberShareClickModel::class, 'share_id', 'share_id');
    }

    /**
     * 关联分享者
     */
    public function sharer()
    {
        return $this->belongsTo(MemberModel::class, 'sharer_id', 'id');
    }

    /**
     * 获取分享类型文本
     */
    public function getShareTypeTextAttr($value, $data)
    {
        return self::SHARE_TYPE_TEXT[$data['share_type']] ?? '未知';
    }

    /**
     * 获取分享渠道文本
     */
    public function getShareChannelTextAttr($value, $data)
    {
        return self::SHARE_CHANNEL_TEXT[$data['share_channel']] ?? '未知';
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        return self::STATUS_TEXT[$data['status']] ?? '未知';
    }

    /**
     * 获取格式化的分享时间
     */
    public function getFormatShareTimeAttr($value, $data)
    {
        return date('Y-m-d H:i:s', strtotime($data['share_time']));
    }

    /**
     * 获取格式化的过期时间
     */
    public function getFormatExpireTimeAttr($value, $data)
    {
        return !empty($data['expire_time']) ? date('Y-m-d H:i:s', strtotime($data['expire_time'])) : '永久有效';
    }

    /**
     * 检查分享是否有效
     */
    public function isValid()
    {
        if ($this->status != self::STATUS_VALID) {
            return false;
        }

        if (!empty($this->expire_time) && strtotime($this->expire_time) < time()) {
            return false;
        }

        return true;
    }

    /**
     * 生成唯一分享ID
     */
    public static function generateShareId()
    {
        return md5(uniqid('share_', true) . microtime(true) . mt_rand(1000, 9999));
    }
}
