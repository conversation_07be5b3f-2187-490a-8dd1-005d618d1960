<?php

namespace app\common\model;

use think\facade\Db;

class TransactionModel extends BaseModel
{
    protected $name = 'transaction';
    protected $pk = 'id';

    //状态 0未执行 1执行中 2执行成功 3执行失败
    const STATUS_NOT_EXEC     = 0;
    const STATUS_EXECING      = 1;
    const STATUS_EXEC_SUCCESS = 2;
    const STATUS_EXEC_FAIL    = 3;


    /**
     * @param array $action 定时任务要执行的方法全路径（例如:['method'=>'app\api\service\InterviewService::createRoom','param'=> [$this->$orderId]]）,method为作为回调函数（callback）调用,把参数数组作（param）为回调函数的的参数传入。
     * @param string $tag 标记
     * @param string $remark 任务描述
     * @param int $delayTime 延迟秒数（时间戳）= 要执行时间 - 当前时间
     * @param int $maxExeNum 最大需要执行的次数
     * @return bool|string
     */
    public static function addOneData(array $action = [], string $tag = '', string $remark = '', int $delayTime = 0, int $maxExeNum = 3): bool|string
    {
        $actionStr = serialize($action);
        $actionKey = md5($actionStr);
        $model     = self::where('id', $actionKey)->findOrEmpty();
        if (!$model->isEmpty()) {
            //如果记录存在，则进行更新
            return self::updateOneData($tag, $delayTime);
        }
        $nowTime = time();

        $data = [
            'id'          => $actionKey,
            'tag'         => $tag,
            'remark'      => $remark,
            'action'      => $actionStr,
            'max_exe_num' => $maxExeNum,
            'status'      => self::STATUS_NOT_EXEC,
            'delay_time'  => $delayTime > 0 ? date('Y-m-d H:i:s', $nowTime + $delayTime) : date('Y-m-d H:i:s', $nowTime),
            'create_time' => date('Y-m-d H:i:s', $nowTime),
            'update_time' => date('Y-m-d H:i:s', $nowTime),
        ];

        $insertId = self::insertGetId($data);
        if ($insertId <= 0) {
            return false;
        }
        return $actionKey;
    }

    /**
     * 通过tag标记维护记录执行时间
     * @param string $tag
     * @param int $delayTime
     * @return bool
     */
    public static function updateOneData(string $tag = '', int $delayTime = 0): bool
    {
        if (empty($tag)) {
            return false;
        }
        $where = [
            'tag'    => $tag,
            'status' => 0,//状态：0-未执行；1-执行中；2-执行成功；3-执行失败
        ];
        $model = (new TransactionModel)->where($where)->findOrEmpty();
        if ($model->isEmpty()) {
            return false;
        }
        $nowTime = time();
        $data    = [
            'delay_time' => $delayTime > 0 ? date('Y-m-d H:i:s', $nowTime + $delayTime) : date('Y-m-d H:i:s', $nowTime),
        ];
        return (new TransactionModel)->where($where)->update($data) > 0;
    }
}
