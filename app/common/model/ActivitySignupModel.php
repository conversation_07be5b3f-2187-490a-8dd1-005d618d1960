<?php

declare(strict_types=1);

namespace app\common\model;

class ActivitySignupModel extends BaseModel
{
    protected $name = 'activity_signup';


    // 定义与活动的关联
    public function activity()
    {
        return $this->belongsTo(ActivityModel::class, 'activity_id');
    }

    // 定义与用户的关联
    public function member()
    {
        return $this->belongsTo(MemberModel::class, 'member_id', 'id');
    }

    // 定义与门店选择记录的关联
    public function shopChoices()
    {
        return $this->hasMany(ActivitySignupShopChoicesModel::class, 'signup_id');
    }
}
