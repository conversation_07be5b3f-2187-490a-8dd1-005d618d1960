<?php

namespace app\common\model;


class OpenBdidRecordModel extends BaseModel
{
    protected $name = 'open_bdid_record';

    /**
     * 添加记录
     */
    public static function addOneData($bdid = "", $clicker_id = 0, $ip = "", $device = ""): bool
    {
        if ($bdid == "" || $clicker_id == 0) {
            return false;
        }
        $now = date('Y-m-d H:i:s');
        // 查找数据是否存在
        $model = self::where([
            'bdid' => $bdid,
            'clicker_id' => $clicker_id
        ])->findOrEmpty();
        if (!$model->isEmpty()) {
            //如果记录存在，则进行更新
            self::where(['id' => $model->id])->update(['last_click_time' => $now]);
            return false;
        }
        $data = [
            'bdid' => $bdid,
            'clicker_id' => $clicker_id,
            'clicker_ip' => $ip,
            'clicker_device' => $device,
            'first_click_time' => $now,
        ];
        $insertId = self::insertGetId($data);
        return $insertId > 0;
    }
}
