<?php

declare(strict_types=1);

namespace app\common\model;


use app\common\service\UploadService;
use think\facade\Cache;

class GoodsCategoryBasicModel extends BaseModel
{
    protected $name = 'goods_category_basic';
    protected $pk = 'c_id';
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';

    /**
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getAll(): array
    {
        $list = Cache::get('goodsCategoryAll');
        if (empty($list)) {
            $result = [];
            $list   = $this->where(['is_delete' => 0])->order("sort asc,c_id asc")->select();
            if (!$list->isEmpty()) {
                $result = arrayToTree($list->toArray(), 0, 'c_id', 'parent_id');
                Cache::set('goodsCategoryAll', $result, 3600 * 60);
            }
            return $result;
        }
        return $list;
    }

    public static function getCategoryMap($levels = [])
    {
        $cacheKey = 'goodsCategoryMap_' . implode('_', $levels);
        $map      = Cache::store('redis')->get($cacheKey);
        if (!empty($map)) {
            return $map;
        }

        $where = [
            ['is_delete', '=', 0],
        ];
        if ($levels) {
            $where[] = ['level', 'in', $levels];
        }
        $map = self::where($where)
            ->order("sort asc,c_id asc")
            ->field('c_id, name, parent_id, level,icon_id, sort, content_image_id, content, service_object')
            ->select()
            ->toArray();

        //只处理level=1的icon_url
        if (!empty($map)) {
            $icons = array_column($map, 'icon_id');
            $content_image_ids = array_column($map, 'content_image_id');
            $mergeIds = array_merge($icons, $content_image_ids);
            $file = UploadService::getByIds($mergeIds, "259x205");
            foreach ($map as $key => &$value) {
                $value['icon_url'] = $file[intval($value['icon_id'])] ?? '';
                $value['content_image_url'] = $file[intval($value['content_image_id'])] ?? '';
            }
            unset($value);
        }
        Cache::store('redis')->set($cacheKey, $map, 3600 * 60);
        return $map;
    }
}
