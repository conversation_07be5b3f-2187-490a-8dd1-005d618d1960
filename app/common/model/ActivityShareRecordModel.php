<?php

namespace app\common\model;

use think\cache\driver\Redis;
use think\facade\Cache;

class ActivityShareRecordModel extends BaseModel
{
    protected $name = 'activity_share_record';

    /**
     * 添加分享记录(强制用户id)
     */
    public static function addOneData($activity_id = 0, $sharer_id = 0, $clicker_id = 0, $ip = "", $device = "", $token = ""): bool
    {
        if (empty($activity_id) || empty($sharer_id) || empty($device) || empty($token)) {
            return false;
        }
        // 查找活动
        $activity = ActivityModel::field('id, end_date, lottery_period')->where([
            'id' => $activity_id,
            'audit_status' => 1,
            'status' => 20,
        ])->findOrEmpty();
        if ($activity->isEmpty() || empty($activity->lottery_period) || $activity->end_date < date('Y-m-d')) {
            return false;
        }
        // 处理分享
        $p = [
            'activity_id' => $activity_id,
            'sharer_id' => $sharer_id,
            'clicker_device' => $device
        ];
        $key = "ShareActivityKey:" . $sharer_id . ":" . $clicker_id. ":". $device;
        $json = json_encode($p);
        $redis = Cache::store('redis')->handler();
        if ($redis->get($key)) {
            return false;
        }
        Cache::store('redis')->set($key,  $json, 86400); // 24小时频率限制
        $now = date('Y-m-d H:i:s');
        // 查找数据是否存在
        $todo = false;
        $model = self::where([
            'activity_id' => $activity_id,
            'sharer_id' => $sharer_id,
            'clicker_device' => $device
        ])->findOrEmpty();
        if (!$model->isEmpty()) {
            $param = [
                'last_click_time' => $now,
            ];
            if ($model->clicker_id == 0 && $clicker_id > 0 && $sharer_id != $clicker_id) {
                $param['clicker_id'] = $clicker_id;
                 $todo = true;
            }
            // 如果记录存在，则进行更新
            $res = self::where(['id' => $model->id])->update($param);
            if ($res &&  $todo) {
                return self::pushShareLotteryPeriod($activity->lottery_period, $activity->id, $sharer_id, $token);
            }
            return false;
        }
        $data = [
            'activity_id' => $activity_id,
            'sharer_id' => $sharer_id,
            'clicker_id' => $clicker_id,
            'clicker_ip' => $ip,
            'clicker_device' => $device,
            'first_click_time' => $now,
        ];
        $insertId = self::insertGetId($data);
        if ($insertId > 0 && $clicker_id > 0 && $sharer_id != $clicker_id) {
            return self::pushShareLotteryPeriod($activity->lottery_period, $activity->id, $sharer_id, $token);
        }
        return false;
    }

    private static function pushShareLotteryPeriod($lotteryPeriod = "", $activityId = 0, $memberId = 0, $token = "") :bool {
        if (empty($lotteryPeriod) || $activityId <= 0 || $memberId <= 0 || empty($token)) {
            return false;
        }
        $data = [
            'period' => $lotteryPeriod,
            'type' => 'share',  
            'source_type' => 'activity',
            'source_id' => $activityId,
            'add_member_id' => $memberId,
        ];
        $config = config('api.services.Member');
        $url = $config['BaseURL'] . $config['Api']['AddChance'];
        $res = curlPostApiContentByUrlAndParams($url, $data, ['Authorization: Bearer ' . $token], false);
        if (empty($res) || $res['code'] != 1) {
            return false;
        }
        return true;
    }
}
