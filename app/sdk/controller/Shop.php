<?php

namespace app\sdk\controller;

use app\common\controller\Base;
use app\common\enum\Response;
use app\common\service\ShopService;
use app\common\service\TagService;
use think\response\Json;

class Shop extends Base
{
    public function index(): Json
    {
        $param   = $this->request->get();
        $service = new ShopService();
        $rows    = $service->getShopList($param);
        return json(success($rows));
    }

    public function add(): Json
    {
        $param = $this->request->post();
        if (!isset($param['user_name']) || empty($param['user_name'])) {
            return json(fail(Response::ERROR, 'user_name不能为空'));
        }
        if (!isset($param['password']) || empty($param['password'])) {
            return json(fail(Response::ERROR, 'password不能为空'));
        }
        if (!isset($param['shop_name']) || empty($param['shop_name'])) {
            return json(fail(Response::ERROR, 'shop_name不能为空'));
        }
        if (!isset($param['linkman']) || empty($param['linkman'])) {
            return json(fail(Response::ERROR, 'linkman不能为空'));
        }
        if (!isset($param['phone']) || empty($param['phone'])) {
            return json(fail(Response::ERROR, 'phone不能为空'));
        }
        $token          = $this->request->token;
        $param['token'] = $token;
        if (empty($token)) {
            return json(fail(Response::ERROR, 'token不能为空'));
        }
        $msg     = "";
        $service = new ShopService();
        $res     = $service->addShop($param, $msg);
        if ($res === false) {
            return json(fail(Response::ERROR, $msg));
        }
        return json(success());
    }

    public function detail(): Json
    {
        $shopId  = $this->request->get('shop_id');
        $service = new ShopService();
        $shop    = $service->getShop($shopId, 1);
        return json($shop);
    }

    public function update(): Json
    {
        $param   = $this->request->post();
        $service = new ShopService();
        $res     = $service->editShopForSys($param);
        if ($res === false) {
            return json(fail(Response::ERROR, '更新失败'));
        }
        return json(success());
    }

    // 添加微信分账列表
    public function addReceiver(): Json
    {
        $param  = $this->request->post();
        $shopId = $param['shop_id'] ?? 0;
        if ($shopId <= 0) {
            return json(fail(Response::ERROR, '门店id不能为空'));
        }
        $msg     = '';
        $service = new ShopService();
        $res     = $service->addReceiverByShopId($shopId, $msg);
        if ($res === false) {
            return json(fail(Response::ERROR, $msg));
        }
        return json(success());
    }

    /**
     * 根据shopId新增&更新店铺服务区域
     * @return Json
     */
    public function saveServiceZone(): Json
    {
        $param  = $this->request->post();
        $shopId = $param['shop_id'] ?? 0;
        if ($shopId <= 0) {
            return json(fail(Response::ERROR, '门店id不能为空'));
        }
        $msg     = '';
        $service = new ShopService();
        $res     = $service->saveServiceZone($shopId, $param);
        if ($res === false) {
            return json(fail(Response::ERROR, $msg));
        }
        return json(success());
    }


    /**
     * 通过typeId获取列表
     * @return Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getTagList()
    {
        $typeId = $this->request->get("type_id");
        $tags   = TagService::getById($typeId);
        return json(success($tags));
    }

    /**
     * 根据shopId新增&更新店铺认证标签
     * @return Json
     */
    public function saveAuthTag(): Json
    {
        $param  = $this->request->post();
        $shopId = $param['shop_id'] ?? 0;
        if ($shopId <= 0) {
            return json(fail(Response::ERROR, '门店id不能为空'));
        }
        if (isset($param['auth_tag']) && empty($param['auth_tag'])) {
            return json(fail(Response::ERROR, '门店认证标签不能为空'));
        }
        $msg  = '';
        $data = [
            'id'          => $shopId,
            'auth_tag'    => $param['auth_tag'],
            'update_time' => time()
        ];

        $res = (new ShopService())->saveShop($data);
        if ($res === false) {
            return json(fail(Response::ERROR, $msg));
        }
        return json(success());
    }
}
