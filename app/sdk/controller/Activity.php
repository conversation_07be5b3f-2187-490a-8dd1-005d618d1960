<?php

declare(strict_types=1);

namespace app\sdk\controller;

use app\common\controller\Base;
use app\common\enum\Response;
use app\common\service\ActivityService;
use app\common\validate\ActivityValidate;
use think\response\Json;

class Activity extends Base
{

    /**
     * 审核活动
     * @return Json
     * @throws \think\Exception
     */
    public function audit(): Json
    {
        $audit_user_id = 0;
        if (isset($params['audit_user_id'])) {
            $audit_user_id = intval($params['audit_user_id']);
        } else {
            $audit_user_id = intval($this->request->user['userId'] ?? 0);
        }
        $params = $this->request->post();
        $result = (new ActivityService())->audit(
            (int)$params['id'],
            (int)$params['audit_status'],
            $audit_user_id,
            $params['audit_remark'] ?? ''
        );
        return json(success([$result]));
    }

    /**
     * 列表页
     * @return Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function index()
    {
        $param = $this->request->get();
        $list  = (new ActivityService())->getList($param);
        return json(success($list));
    }

    /**
     * 创建活动
     * @return \think\response\Json
     */
    public function create(): Json
    {
        $params   = $this->request->post();
        $activity = (new ActivityService())->create($params);
        if ($activity) {
            return json(success(['创建活动成功']));
        }
        return json(fail());
    }

    /**
     * 活动详情
     * @return Json
     */
    public function detail(): Json
    {
        $param    = $this->param;
        $validate = new ActivityValidate();
        if (!$validate->scene('detailSDK')->check($param)) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }
        $activity = (new ActivityService())->detail($param);
        return json(success($activity));
    }

    /**
     * 活动编辑
     * @return Json
     */
    public function update(): Json
    {
        try {
            $param    = $this->param;
            $activity = (new ActivityService())->update($param);
            if ($activity) {
                return json(success(['活动编辑成功']));
            }
            return json(fail());
        } catch (\Exception $e) {
            return json(fail(Response::DATA_NOT_FOUND, $e->getMessage()));
        }
    }

    /**
     * 活动删除
     * @return Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function softDelete(): Json
    {
        try {
            $param            = $this->param;
            $param['shop_id'] = 0; //平台活动,shop_id=0;
            $activity         = (new ActivityService())->softDelete($param);
            if ($activity) {
                return json(success(['活动删除成功']));
            }
            return json(fail());
        } catch (\Exception $e) {
            return json(fail(Response::DATA_NOT_FOUND, $e->getMessage()));
        }
    }

    /**
     * 活动报名列表
     * @return Json
     * @throws \think\db\exception\DbException
     */
    public function signupList(): Json
    {
        $param = $this->param;
        $list  = (new ActivityService())->signupList($param);
        return json(success($list));
    }
}
