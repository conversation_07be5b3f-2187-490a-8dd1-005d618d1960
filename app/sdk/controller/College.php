<?php
declare(strict_types=1);

namespace app\sdk\controller;

use app\common\controller\Base;
use app\common\service\CollegeService;
use app\common\service\TagService;
use app\common\service\UploadService;
use think\facade\Cache;
use think\facade\Request;
use think\response\Json;

class College extends Base
{

    /**
     * 母婴学社标签列表（用于添加页面）
     * @return Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getTag(): Json
    {
        // 配置项（数据来源于DB）
        $result = [
            'course_auth_tags'      => TagService::getById(9),//课程认证
            'article_tags'          => TagService::getById(10),//文章类型
            'expert_category_tags'  => TagService::getById(11),//专家职业
            'expert_auth_tags'      => TagService::getById(12),//专家认证
            'certificate_auth_tags' => TagService::getById(13),//证书优势
            //用于管理后台判断添加的是视频还是文章，数据来源category表，level=1
            'category_first_level'  => [
                'id_type_video'   => [2],//2-技能培训-课程
                'id_type_article' => [1, 60], //1-宝妈智库-文章,60-就业故事
            ]
        ];
        return json(success($result));
    }

    /**
     * 课程添加
     * @return Json
     */
    public function createCourse(): Json
    {
        $params             = $this->request->post();
        $params['video_id'] = 0;
        if (!empty($params['video_info'])) {
            $type               = 3;
            $videoInfo          = $params['video_info'];
            $params['video_id'] = UploadService::saveFileData($type, $videoInfo);
        }
        $request  = [
            'title'            => $params['title'] ?? '',
            'publisher_id'     => $params['publisher_id'] ?? '',
            'cover_image_id'   => $params['cover_image_id'] ?? '',
            'type'             => $params['type'] ?? '',
            'price'            => $params['price'] ?? '',
            'students_learned' => $params['students_learned'] ?? '',
            'video_id'         => $params['video_id'],
            'text_intro'       => $params['text_intro'] ?? '',
            'summary'          => $params['summary'] ?? '',
            'is_trial'         => $params['is_trial'] ?? '',
            'is_hot'           => $params['is_hot'] ?? '',
            'total_lessons'    => $params['total_lessons'] ?? '',
            'published_time'   => $params['published_time'] ?? '',
            'category_id'      => $params['category_id'] ?? '',
            'auth_tag_id'      => $params['auth_tag_id'] ?? '',
            'certificate_id'   => $params['certificate_id'] ?? '',
            'view_count'       => $params['view_count'] ?? '',
            'sort'             => $params['sort'] ?? '',
        ];
        $response = CollegeService::createCourse($request);

        if ($response['code'] == 1) {
            return json(success());
        }
        return json(fail($response['code'], $response['msg']));
    }

    /**
     * 课程列表
     * @return Json
     */
    public function getCourse(): Json
    {
        $params['page_type'] = 1;//1-传统分页；2-游标分页
        $params['pageNum']  = intval($this->param['pageNum'] ?? 1);
        $params['title']     = $this->param['title'] ?? ""; // 课程标题
        $params['category_id']     = intval($this->param['category_id'] ?? 0); // 分类ID
        $params['publisher_id']     = intval($this->param['publisher_id'] ?? 0); // 发布人ID
        $response            = CollegeService::getCourse($params);
        if ($response['code'] == 1) {
            return json(success($response['data']));
        }
        return json(fail($response['code'], $response['msg']));
    }

    /**
     * 课程详情
     * @return Json
     */
    public function getSdkCourseById(): Json
    {
        $id       = intval($this->param['id'] ?? 0);
        $response = CollegeService::getSdkCourseById($id);
        if ($response['code'] == 1) {
            return json(success($response['data']));
        }
        return json(fail($response['code'], $response['msg']));
    }

    /**
     * 课程更新
     * @return Json
     */
    public function updateCourseById(): Json
    {
        $id                 = Request::param("id", null, 'intval'); // 转为int
        $params             = $this->request->put();
        $params['video_id'] = 0;
        if (!empty($params['video_info'])) {
            $type               = 3;
            $videoInfo          = $params['video_info'];
            $params['video_id'] = UploadService::saveFileData($type, $videoInfo);
        }
        $request  = [
            'title'            => $params['title'] ?? '',
            'publisher_id'     => $params['publisher_id'] ?? '',
            'cover_image_id'   => $params['cover_image_id'] ?? '',
            'type'             => $params['type'] ?? '',
            'price'            => $params['price'] ?? '',
            'students_learned' => $params['students_learned'] ?? '',
            'video_id'         => $params['video_id'],
            'text_intro'       => $params['text_intro'] ?? '',
            'summary'          => $params['summary'] ?? '',
            'is_trial'         => $params['is_trial'] ?? '',
            'is_hot'           => $params['is_hot'] ?? '',
            'total_lessons'    => $params['total_lessons'] ?? '',
            'published_time'   => $params['published_time'] ?? '',
            'category_id'      => $params['category_id'] ?? '',
            'auth_tag_id'      => $params['auth_tag_id'] ?? '',
            'certificate_id'   => $params['certificate_id'] ?? '',
            'view_count'       => $params['view_count'] ?? '',
            'sort'             => $params['sort'] ?? '',
        ];
        $response = CollegeService::updateCourseById($id, $request);
        if ($response['code'] == 1) {
            return json(success());
        }
        return json(fail($response['code'], $response['msg']));
    }

    /**
     * 课程排序更新
     * @return Json
     */
    public function updateCourseSortById(): Json
    {
        $id       = Request::param("id", null, 'intval'); // 转为int
        $params   = $this->request->put();
        $request  = [
            'sort' => $params['sort'] ?? 0,
        ];
        $response = CollegeService::updateCourseSortById($id, $request);
        if ($response['code'] == 1) {
            return json(success());
        }
        return json(fail($response['code'], $response['msg']));
    }

    /**
     * 课程删除
     * @return Json
     */
    public function deleteCourseById(): Json
    {
        $id       = Request::param("id", null, 'intval'); // 转为int
        $response = CollegeService::deleteCourseById($id);
        if ($response['code'] == 1) {
            return json(success());
        }
        return json(fail($response['code'], $response['msg']));
    }

    /**
     * 课程章节添加
     * @return Json
     */
    public function createCourseSection(): Json
    {
        $params             = $this->request->post();
        $params['video_id'] = 0;
        $type               = 3;
        if (!empty($params['video_info'])) {
            $videoInfo          = $params['video_info'];
            $params['video_id'] = UploadService::saveFileData($type, $videoInfo);
        }
        $request  = [
            'course_id'    => $params['course_id'] ?? '',
            'title'        => $params['title'] ?? '',
            'type'         => $params['type'] ?? '',
            'video_id'     => $params['video_id'],
            //'video_time'   => $params['video_time'] ?? '',
            'text_content' => $params['text_content'] ?? '',
            'sort'         => $params['sort'] ?? '',
        ];
        $response = CollegeService::createCourseSection($request);
        if ($response['code'] == 1) {
            // 写入redis，开始进行切片操作
            if (!empty($params['video_info']) && $type == 3) {
                $redis = Cache::store('redis')->handler();
                $redis->lpush("video_processing_queue", $videoInfo['file_name']);//实例：c69344d9b77aa34704278bfb69d2b7bc.mp4
            }
            return json(success());
        }
        return json(fail($response['code'], $response['msg']));
    }

    /**
     * 课程章节列表
     * @return Json
     */
    public function getCourseSectionByCourseId(): Json
    {
        $params['page_type']    = 1;//1-传统分页；2-游标分页
        $params['pageNum']     = intval($this->param['pageNum'] ?? 1);
        $id                     = intval($this->param['id'] ?? 0);
        $params['course_title'] = $this->param['course_title'] ?? "";
        $response               = CollegeService::getCourseSectionByCourseId($id, $params);
        if ($response['code'] == 1) {
            return json(success($response['data']));
        }
        return json(fail($response['code'], $response['msg']));
    }

    /**
     * 课程章节详情
     * @return Json
     */
    public function getCourseSectionById(): Json
    {
        $id       = intval($this->param['id'] ?? 0);
        $response = CollegeService::getCourseSectionById($id);
        if ($response['code'] == 1) {
            return json(success($response['data']));
        }
        return json(fail($response['code'], $response['msg']));
    }

    /**
     * 课程章节更新
     * @return Json
     */
    public function updateCourseSectionById(): Json
    {
        $id                 = Request::param("id", null, 'intval'); // 转为int
        $params             = $this->request->put();
        $params['video_id'] = 0;
        $type               = 3;
        if (!empty($params['video_info'])) {
            $videoInfo          = $params['video_info'];
            $params['video_id'] = UploadService::saveFileData($type, $videoInfo);
        }
        $request  = [
            'course_id'    => $params['course_id'] ?? '',
            'title'        => $params['title'] ?? '',
            'type'         => $params['type'] ?? '',
            'video_id'     => $params['video_id'],
            'video_time'   => $params['video_time'] ?? '',
            'text_content' => $params['text_content'] ?? '',
            'sort'         => $params['sort'] ?? '',
        ];
        $response = CollegeService::updateCourseSectionById($id, $request);
        if ($response['code'] == 1) {
            // 写入redis，开始进行切片操作
            if (!empty($params['video_info']) && $type == 3) {
                $redis = Cache::store('redis')->handler();
                $redis->lpush("video_processing_queue", $videoInfo['file_name']);//实例：c69344d9b77aa34704278bfb69d2b7bc.mp4
            }
            return json(success());
        }
        return json(fail($response['code'], $response['msg']));
    }

    /**
     * 课程章节排序更新
     * @return Json
     */
    public function updateCourseSectionSortById(): Json
    {
        $id       = Request::param("id", null, 'intval'); // 转为int
        $params   = $this->request->put();
        $request  = [
            'sort' => $params['sort'] ?? 0,
        ];
        $response = CollegeService::updateCourseSectionSortById($id, $request);
        if ($response['code'] == 1) {
            return json(success());
        }
        return json(fail($response['code'], $response['msg']));
    }

    /**
     * 课程章节删除
     * @return Json
     */
    public function deleteCourseSectionById(): Json
    {
        $id       = Request::param("id", null, 'intval'); // 转为int
        $response = CollegeService::deleteCourseSectionById($id);
        if ($response['code'] == 1) {
            return json(success());
        }
        return json(fail($response['code'], $response['msg']));
    }

    /**
     * 分类添加
     * @return Json
     */
    public function createCategory(): Json
    {
        $params   = $this->request->post();
        $request  = [
            'pid'     => $params['pid'] ?? '',
            'level'   => $params['level'] ?? '',
            'name'    => $params['name'] ?? '',
            'icon_id' => $params['icon_id'] ?? '',
            'sort'    => $params['sort'] ?? '',
            'type'    => $params['type'] ?? '',
        ];
        $response = CollegeService::createCategory($request);
        if ($response['code'] == 1) {
            return json(success());
        }
        return json(fail($response['code'], $response['msg']));
    }

    /**
     * 分类列表
     * @return Json
     */
    public function getCategory(): Json
    {
        $response = CollegeService::getCategory();
        if ($response['code'] == 1) {
            return json(success($response['data']));
        }
        return json(fail($response['code'], $response['msg']));
    }

    /**
     * 分类详情
     * @return Json
     */
    public function getCategoryById(): Json
    {
        $id       = intval($this->param['id'] ?? 0);
        $response = CollegeService::getCategoryById($id);
        if ($response['code'] == 1) {
            return json(success($response['data']));
        }
        return json(fail($response['code'], $response['msg']));
    }

    /**
     * 分类更新
     * @return Json
     */
    public function updateCategoryById(): Json
    {
        $id       = Request::param("id", null, 'intval'); // 转为int
        $params   = $this->request->put();
        $request  = [
            'pid'     => $params['pid'] ?? '',
            'level'   => $params['level'] ?? '',
            'name'    => $params['name'] ?? '',
            'icon_id' => $params['icon_id'] ?? '',
            'sort'    => $params['sort'] ?? '',
            'type'    => $params['type'] ?? '',
        ];
        $response = CollegeService::updateCategoryById($id, $request);
        if ($response['code'] == 1) {
            return json(success());
        }
        return json(fail($response['code'], $response['msg']));
    }

    /**
     * 分类排序更新
     * @return Json
     */
    public function updateCategorySortById(): Json
    {
        $id       = Request::param("id", null, 'intval'); // 转为int
        $params   = $this->request->put();
        $request  = [
            'sort' => $params['sort'] ?? 0,
        ];
        $response = CollegeService::updateCategorySortById($id, $request);
        if ($response['code'] == 1) {
            return json(success());
        }
        return json(fail($response['code'], $response['msg']));
    }

    /**
     * 分类删除
     * @return Json
     */
    public function deleteCategoryById(): Json
    {
        $id       = Request::param("id", null, 'intval'); // 转为int
        $response = CollegeService::deleteCategoryById($id);
        if ($response['code'] == 1) {
            return json(success());
        }
        return json(fail($response['code'], $response['msg']));
    }

    /**
     * 课程学习列表
     * @return Json
     */
    public function getMemberCourseRecord(): Json
    {
        $params['page_type']     = 1;//1-传统分页；2-游标分页
        $params['pageNum']      = intval($this->param['pageNum'] ?? 1);
        $params['member_mobile'] = $this->param['member_mobile'] ?? "";//用户手机号
        $params['course_title']  = $this->param['course_title'] ?? "";//课程标题
        $response                = CollegeService::getMemberCourseRecord($params);
        if ($response['code'] == 1) {
            return json(success($response['data']));
        }
        return json(fail($response['code'], $response['msg']));
    }

    /**
     * 课程学习详情
     * @return Json
     */
    public function getMemberCourseRecordById(): Json
    {
        $id       = intval($this->param['id'] ?? 0);
        $response = CollegeService::getMemberCourseRecordById($id);
        if ($response['code'] == 1) {
            return json(success($response['data']));
        }
        return json(fail($response['code'], $response['msg']));
    }

    /**
     * 课程学习更新
     * @return Json
     */
    public function updateMemberCourseRecordById(): Json
    {
        $id       = Request::param("id", null, 'intval'); // 转为int
        $params   = $this->request->put();
        $request  = [
            'pid'     => $params['pid'] ?? '',
            'level'   => $params['level'] ?? '',
            'name'    => $params['name'] ?? '',
            'icon_id' => $params['icon_id'] ?? '',
            'sort'    => $params['sort'] ?? '',
            'type'    => $params['type'] ?? '',
        ];
        $response = CollegeService::updateMemberCourseRecordById($id, $request);
        if ($response['code'] == 1) {
            return json(success());
        }
        return json(fail($response['code'], $response['msg']));
    }

    /**
     * 专家添加
     * @return Json
     */
    public function createExpert(): Json
    {
        $params   = $this->request->post();
        $request  = [
            'image_id'               => $params['image_id'] ?? '',
            'name'                   => $params['name'] ?? '',
            'avatar_id'              => $params['avatar_id'] ?? '',
            'expert_tag_id'          => $params['expert_tag_id'] ?? '',
            'auth_tag_id'            => $params['auth_tag_id'] ?? '',
            'skills_tag'             => $params['skills_tag'] ?? '',
            'summary'                => $params['summary'] ?? '',
            'details'                => $params['details'] ?? '',
            'auth_category_image_id' => $params['auth_category_image_id'] ?? '',
            'certificate_id'         => $params['certificate_id'] ?? '',
            'category_id'            => $params['category_id'] ?? '',
            'article_count'          => $params['article_count'] ?? '',
            'course_count'           => $params['course_count'] ?? '',
            'like_count'             => $params['like_count'] ?? '',
            'sort'                   => $params['sort'] ?? '',
            'is_hot'                 => $params['is_hot'] ?? '',
        ];
        $response = CollegeService::createExpert($request);

        if ($response['code'] == 1) {
            return json(success());
        }
        return json(fail($response['code'], $response['msg']));
    }

    /**
     * 专家列表
     * @return Json
     */
    public function getExpert(): Json
    {
        $params['page_type'] = 1;//1-传统分页；2-游标分页
        $params['pageNum']  = intval($this->param['pageNum'] ?? 1);
        $params['name']      = $this->param['name'] ?? "";
        $response            = CollegeService::getExpert($params);
        if ($response['code'] == 1) {
            return json(success($response['data']));
        }
        return json(fail($response['code'], $response['msg']));
    }

    /**
     * 专家详情
     * @return Json
     */
    public function getExpertById(): Json
    {
        $id       = intval($this->param['id'] ?? 0);
        $response = CollegeService::getExpertById($id);
        if ($response['code'] == 1) {
            return json(success($response['data']));
        }
        return json(fail($response['code'], $response['msg']));
    }

    /**
     * 专家更新
     * @return Json
     */
    public function updateExpertById(): Json
    {
        $id       = Request::param("id", null, 'intval'); // Ensure id is an integer
        $params   = $this->request->put();
        $request  = [
            'image_id'               => $params['image_id'] ?? '',
            'name'                   => $params['name'] ?? '',
            'avatar_id'              => $params['avatar_id'] ?? '',
            'expert_tag_id'          => $params['expert_tag_id'] ?? '',
            'auth_tag_id'            => $params['auth_tag_id'] ?? '',
            'skills_tag'             => $params['skills_tag'] ?? '',
            'summary'                => $params['summary'] ?? '',
            'details'                => $params['details'] ?? '',
            'auth_category_image_id' => $params['auth_category_image_id'] ?? '',
            'certificate_id'         => $params['certificate_id'] ?? '',
            'category_id'            => $params['category_id'] ?? '',
            'article_count'          => $params['article_count'] ?? '',
            'course_count'           => $params['course_count'] ?? '',
            'like_count'             => $params['like_count'] ?? '',
            'sort'                   => $params['sort'] ?? '',
            'is_hot'                 => $params['is_hot'] ?? '',
        ];
        $response = CollegeService::updateExpertById($id, $request);
        if ($response['code'] == 1) {
            return json(success());
        }
        return json(fail($response['code'], $response['msg']));
    }

    /**
     * 专家排序更新
     * @return Json
     */
    public function updateExpertSortById(): Json
    {
        $id       = Request::param("id", null, 'intval'); // Ensure id is an integer
        $params   = $this->request->put();
        $request  = [
            'sort' => $params['sort'] ?? 0,
        ];
        $response = CollegeService::updateExpertSortById($id, $request);
        if ($response['code'] == 1) {
            return json(success());
        }
        return json(fail($response['code'], $response['msg']));
    }

    /**
     * 专家删除
     * @return Json
     */
    public function deleteExpertById(): Json
    {
        $id       = Request::param("id", null, 'intval'); // Ensure id is an integer
        $response = CollegeService::deleteExpertById($id);
        if ($response['code'] == 1) {
            return json(success());
        }
        return json(fail($response['code'], $response['msg']));
    }


    /**
     * 证书添加
     * @return Json
     */
    public function createCertificate(): Json
    {
        $params   = $this->request->post();
        $request  = [
            'cover_image_id'       => $params['cover_image_id'] ?? '',
            'image_id'             => $params['image_id'] ?? '',
            'name'                 => $params['name'] ?? '',
            'difficulty'           => $params['difficulty'] ?? '',
            'category_id'          => $params['category_id'] ?? '',
            'issuing_organization' => $params['issuing_organization'] ?? '',
            'summary'              => $params['summary'] ?? '',
            'details'              => $params['details'] ?? '',
            'industry_recognition' => $params['industry_recognition'] ?? '',
            'total_courses'        => $params['total_courses'] ?? '',
            'tag_id'               => $params['tag_id'] ?? '',
            'rate'                 => $params['rate'] ?? '',
            'students_got'         => $params['students_got'] ?? '',
            'duration'             => $params['duration'] ?? '',
            'is_hot'               => $params['is_hot'] ?? '',
            'sort'                 => $params['sort'] ?? '',
        ];
        $response = CollegeService::createCertificate($request);

        if ($response['code'] == 1) {
            return json(success());
        }
        return json(fail($response['code'], $response['msg']));
    }

    /**
     * 证书列表
     * @return Json
     */
    public function getCertificate(): Json
    {
        $params['page_type'] = 1;//1-传统分页；2-游标分页
        $params['pageNum']  = intval($this->param['pageNum'] ?? 1);
        $params['name']      = $this->param['name'] ?? "";
        $response            = CollegeService::getCertificate($params);
        if ($response['code'] == 1) {
            return json(success($response['data']));
        }
        return json(fail($response['code'], $response['msg']));
    }

    /**
     * 证书详情
     * @return Json
     */
    public function getCertificateById(): Json
    {
        $id       = intval($this->param['id'] ?? 0);
        $response = CollegeService::getCertificateById($id);
        if ($response['code'] == 1) {
            return json(success($response['data']));
        }
        return json(fail($response['code'], $response['msg']));
    }

    /**
     * 证书更新
     * @return Json
     */
    public function updateCertificateById(): Json
    {
        $id       = Request::param("id", null, 'intval'); // Ensure id is an integer
        $params   = $this->request->put();
        $request  = [
            'cover_image_id'       => $params['cover_image_id'] ?? '',
            'image_id'             => $params['image_id'] ?? '',
            'name'                 => $params['name'] ?? '',
            'difficulty'           => $params['difficulty'] ?? '',
            'category_id'          => $params['category_id'] ?? '',
            'issuing_organization' => $params['issuing_organization'] ?? '',
            'summary'              => $params['summary'] ?? '',
            'details'              => $params['details'] ?? '',
            'industry_recognition' => $params['industry_recognition'] ?? '',
            'total_courses'        => $params['total_courses'] ?? '',
            'tag_id'               => $params['tag_id'] ?? '',
            'rate'                 => $params['rate'] ?? '',
            'students_got'         => $params['students_got'] ?? '',
            'duration'             => $params['duration'] ?? '',
            'is_hot'               => $params['is_hot'] ?? '',
            'sort'                 => $params['sort'] ?? '',
        ];
        $response = CollegeService::updateCertificateById($id, $request);
        if ($response['code'] == 1) {
            return json(success());
        }
        return json(fail($response['code'], $response['msg']));
    }

    /**
     * 证书排序更新
     * @return Json
     */
    public function updateCertificateSortById(): Json
    {
        $id       = Request::param("id", null, 'intval'); // Ensure id is an integer
        $params   = $this->request->put();
        $request  = [
            'sort' => $params['sort'] ?? 0,
        ];
        $response = CollegeService::updateCertificateSortById($id, $request);
        if ($response['code'] == 1) {
            return json(success());
        }
        return json(fail($response['code'], $response['msg']));
    }

    /**
     * 证书删除
     * @return Json
     */
    public function deleteCertificateById(): Json
    {
        $id       = Request::param("id", null, 'intval'); // Ensure id is an integer
        $response = CollegeService::deleteCertificateById($id);
        if ($response['code'] == 1) {
            return json(success());
        }
        return json(fail($response['code'], $response['msg']));
    }
}