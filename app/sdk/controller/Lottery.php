<?php

namespace app\sdk\controller;

use app\common\controller\Base;
use app\common\enum\Response;
use app\common\service\LotteryService;

class Lottery extends Base
{
    // 创建抽奖活动
    public function create()
    {
        $id = 0;
        try {
            $token = $this->request->token;
            $lotteryService = new LotteryService();
            $lotteryService->createLottery('platform', $token, $this->param, $id);
        } catch (\Exception $e) {
            return json(fail(Response::REQUEST_ERROR, $e->getMessage()));
        }
        return json(success(['id' => $id]));
    }

    // 修改抽奖活动
    public function update()
    {
        try {
            $token = $this->request->token;
            $lotteryService = new LotteryService();
            $lotteryService->updateLottery('platform', $token, $this->param);
        } catch (\Exception $e) {
            return json(fail(Response::REQUEST_ERROR, $e->getMessage()));
        }
        return json(success());
    }

    // 创建抽奖活动奖品
    public function PrizeCreate()
    {
        try {
            $token = $this->request->token;
            $lotteryService = new LotteryService();
            $lotteryService->createLotteryPrize('platform', $token, $this->param);
        } catch (\Exception $e) {
            return json(fail(Response::REQUEST_ERROR, $e->getMessage()));
        }
        return json(success());
    }

    // 修改抽奖活动奖品
    public function PrizeUpdate()
    {
        try {
            $token = $this->request->token;
            $lotteryService = new LotteryService();
            $lotteryService->updateLotteryPrize('platform', $token, $this->param);
        } catch (\Exception $e) {
            return json(fail(Response::REQUEST_ERROR, $e->getMessage()));
        }
        return json(success());
    }

    // 获取抽奖活动列表
    public function index()
    {
        $list =  [];
        try {
            $token = $this->request->token;
            $lotteryService = new LotteryService();
            $list = $lotteryService->getLotteryPrizeList('platform', $token, $this->param);
        } catch (\Exception $e) {
            return json(fail(Response::DATA_NOT_FOUND, $e->getMessage()));
        }
        return json(success($list));
    }

    // 获取抽奖活动详情
    public function detail()
    {
        $data =  [];
        try {
            $token = $this->request->token;
            $lotteryService = new LotteryService();
            $data = $lotteryService->getLotteryPrizeDetail('platform', $token, $this->param['id']);
        } catch (\Exception $e) {
            return json(fail(Response::DATA_NOT_FOUND, $e->getMessage()));
        }
        return json(success($data));
    }

    // 删除抽奖活动
    public function delete()
    {
        try {
            $token = $this->request->token;
            $lotteryService = new LotteryService();
            $lotteryService->deleteLottery('platform', $token, $this->param['id']);
        } catch (\Exception $e) {
            return json(fail(Response::REQUEST_ERROR, $e->getMessage()));
        }
        return json(success());
    }

    // 删除抽奖活动奖品
    public function PrizeDelete()
    {
        try {
            $token = $this->request->token;
            $lotteryService = new LotteryService();
            $lotteryService->deleteLotteryPrize('platform', $token, $this->param['id']);
        } catch (\Exception $e) {
            return json(fail(Response::REQUEST_ERROR, $e->getMessage()));
        }
        return json(success());
    }

    public function prizeRecord()
    {
        try {
            $token = $this->request->token;
            $lotteryService = new LotteryService();
            $list = $lotteryService->getPrizeRecord('platform', $token, $this->param);
        } catch (\Exception $e) {
            return json(fail(Response::DATA_NOT_FOUND, $e->getMessage()));
        }
        return json(success($list));
    }

    public function prizeRecordUpdate()
    {
        try {
            $token = $this->request->token;
            $lotteryService = new LotteryService();
            $lotteryService->prizeRecordUpdate('platform', $token, $this->param);
        } catch (\Exception $e) {
            return json(fail(Response::REQUEST_ERROR, $e->getMessage()));
        }
        return json(success());
    }
}
