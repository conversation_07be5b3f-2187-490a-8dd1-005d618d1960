<?php
/**
 * #############【总部端】#############
 */
// 只运行内网访问，防止恶意调用
use think\facade\Route;

// 白名单ip管理
Route::middleware(\app\middleware\WhiteList::class);
Route::get('shop', 'Shop/index');
Route::get('shop/get', 'Shop/detail');
Route::post('shop/update', 'Shop/update');
// 添加分账列表
Route::post('shop/add_receiver', 'Shop/addReceiver');
//根据shopId新增&更新店铺服务区域
Route::post('shop/saveServiceZone', 'Shop/saveServiceZone');
//通过typeId获取列表
Route::get('shop/getTagList', 'Shop/getTagList');
//根据shopId新增&更新店铺认证标签
Route::post('shop/saveAuthTag', 'Shop/saveAuthTag');


// 订单列表
Route::get('order', 'Order/index');
Route::get('order/detail', 'Order/detail');
Route::get('order/pay_list', 'Order/payList');
// 手动回调
Route::post('order/callback', 'Order/callback');
// 请求分账
Route::post('order/profitsharing', 'Order/profitsharing');
// 分账列表
Route::get('order/profitsharing_list', 'Order/profitsharingList');
// 手动结算
Route::post('order/settlement', 'Order/settlement');
// 用户申请退款列表
Route::get('order/refundApply', 'Order/refundApply'); // 用户申请退款列表
// 获取用户申请退款详情
Route::get('order/refundApplyGet', 'Order/refundApplyDetail');
//订单评论列表
Route::get('orderComment', 'OrderComment/index');
//订单评论详情
Route::get('orderComment/get', 'OrderComment/detail');

// 用户列表
Route::get('member', 'Member/index');

// 搜索
Route::get('search/staff', 'Search/searchNameForStaff');

// 获取服务类型
Route::get('goods/category', 'Goods/category');
// 获取商品列表
Route::get('goods/index', 'Goods/index');
// 获取商品详情
Route::get('goods/detail', 'Goods/detail');

// 查询分账结果
Route::post('order/profitsharing_query', 'Order/wxProfitsharingQuery');

// 查找服务人员
Route::get('staff', 'Staff/index');
Route::get('staff/get', 'Staff/detail');

// ES
Route::get('eslist/goods', 'Eslist/getGoods');
Route::get('eslist/staff', 'Eslist/getStaff');

// 活动添加
Route::post('activity', 'Activity/create');
// 活动列表
Route::get('activity', 'Activity/index');
// 活动详情
Route::get('activity/get', 'Activity/detail');
// 活动审核
Route::post('activity/auditPost', 'Activity/audit');
// 活动编辑
Route::post('activity/updatePost', 'Activity/update');
// 活动删除
Route::get('activity/deleteGet', 'Activity/softDelete');
// 活动报名列表
Route::get('activity/signupGet', 'Activity/signupList');
