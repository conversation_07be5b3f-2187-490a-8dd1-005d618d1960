<?php
/**
 * #############【总部端】#############
 */

// 只运行内网访问，防止恶意调用
use think\facade\Route;

Route::group('v1', function () {
    Route::middleware(\app\middleware\Token::class);
    Route::middleware(\app\middleware\SdkAuth::class); // dump($this->request->user); 获取登录用户

    Route::post('login', 'Auth/Login');
    Route::get('getInfo', 'Auth/GetInfo');
    Route::get('getRouters', 'Auth/GetRouters');
    Route::get('user/profile', 'Auth/GetProfileByLoginUser');
    Route::put('user/profile', 'Auth/UpdateProfileByLoginUser');
    Route::put('user/profile/updatePwd', 'Auth/UpdatePasswordByLoginUser');
    Route::post('user/profile/avatar', 'Auth/UploadAvatarByLoginUser');
    Route::post('logout', 'Auth/Logout');

    // 母婴学社标签列表（用于添加页面）
    Route::get('college/tag', 'College/getTag');
    // 课程
    // 课程添加
    Route::post('college/course', 'College/createCourse');
    // 课程列表
    Route::get('college/course', 'College/getCourse');
    // 课程详情
    Route::get('college/course/:id', 'College/getSdkCourseById');
    // 课程修改
    Route::put('college/course/:id', 'College/updateCourseById');
    // 课程修改
    Route::put('college/course/sort/:id', 'College/updateCourseSortById');
    // 课程删除
    Route::delete('college/course/:id', 'College/deleteCourseById');

    // 课程章节
    // 课程章节添加
    Route::post('college/courseSection', 'College/createCourseSection');
    // 课程章节列表
    Route::get('college/courseSection/:id', 'College/getCourseSectionByCourseId');
    // 课程章节详情
    Route::get('college/courseSection/detail/:id', 'College/getCourseSectionById');
    // 课程章节修改
    Route::put('college/courseSection/:id', 'College/updateCourseSectionById');
    // 课程章节排序修改
    Route::put('college/courseSection/sort/:id', 'College/updateCourseSectionSortById');
    // 课程章节删除
    Route::delete('college/courseSection/:id', 'College/deleteCourseSectionById');
    // 分类
    // 分类添加
    Route::post('college/category', 'College/createCategory');
    // 分类列表
    Route::get('college/category', 'College/getCategory');
    // 分类详情
    Route::get('college/category/:id', 'College/getCategoryById');
    // 分类修改
    Route::put('college/category/:id', 'College/updateCategoryById');
    // 分类排序修改
    Route::put('college/category/sort/:id', 'College/updateCategorySortById');
    // 分类删除
    Route::delete('college/category/:id', 'College/deleteCategoryById');
    // 课程学习
    // 课程学习列表
    Route::get('college/memberCourseRecord', 'College/getMemberCourseRecord');
    // 课程学习详情
    Route::get('college/memberCourseRecord/:id', 'College/getMemberCourseRecordById');
    // 专家
    // 专家添加
    Route::post('college/expert', 'College/createExpert');
    // 专家列表
    Route::get('college/expert', 'College/getExpert');
    // 专家详情
    Route::get('college/expert/:id', 'College/getExpertById');
    // 专家修改
    Route::put('college/expert/:id', 'College/updateExpertById');
    // 专家排序修改
    Route::put('college/expert/sort/:id', 'College/updateExpertSortById');
    // 专家删除
    Route::delete('college/expert/:id', 'College/deleteExpertById');
    // 证书
    // 证书添加
    Route::post('college/certificate', 'College/createCertificate');
    // 证书列表
    Route::get('college/certificate', 'College/getCertificate');
    // 证书详情
    Route::get('college/certificate/:id', 'College/getCertificateById');
    // 证书修改
    Route::put('college/certificate/:id', 'College/updateCertificateById');
    // 证书排序修改
    Route::put('college/certificate/sort/:id', 'College/updateCertificateSortById');
    // 证书删除
    Route::delete('college/certificate/:id', 'College/deleteCertificateById');

    // 许愿池
    Route::get('wishpool', 'WishPool/index')->name('wishPoolIndex');
    Route::post('wishpool/audit', 'WishPool/audit')->name('wishPoolAudit');
    Route::post('wishpool/award', 'WishPool/award')->name('wishPoolAward');
    // 许愿池END

    // 合同模版管理
    Route::get('contractTemplate', 'ContractTemplate/index')->name('contractTemplateIndex');
    // 获取合同模版详情
    Route::get('contractTemplate/:id', 'ContractTemplate/getContractTemplateById')->name('getContractTemplateById');
    // 审核合同模版
    Route::put('contractTemplate/:id/audit', 'ContractTemplate/audit')->name('contractTemplateAudit');
    // 合同模版管理END

    // 会员管理
    Route::get('member', 'Member/index');
    // 会员详情
    Route::get('member/:member_id', 'Member/detail');
    // 审核， 如果通过上传会员信息
    Route::put('member/:id/audit', 'Member/audit');
    // 个人账号转分销商
    Route::put('member/:member_id/transfer', 'Member/transfer');
    // 给会员添加积分
    Route::post('member/:member_id/add_integral', 'Member/addIntegral');
    // 获取会员积分记录
    Route::get('member/:member_id/get_integral_record', 'Member/getIntegralRecord');
    // 会员列表End

    // 门店管理
    Route::post('shop/add', 'Shop/add');
    Route::get('shop', 'Shop/index');
    Route::get('shop/get', 'Shop/detail');
    Route::post('shop/update', 'Shop/update');

    //根据shopId新增&更新店铺服务区域
    Route::post('shop/saveServiceZone', 'Shop/saveServiceZone');
    //通过typeId获取列表
    Route::get('shop/getTagList', 'Shop/getTagList');
    //根据shopId新增&更新店铺认证标签
    Route::post('shop/saveAuthTag', 'Shop/saveAuthTag');

    // 财务管理
    // 获取店铺统计
    Route::get('finance/shopStat/:id', 'Finance/getShopStat');
    // 获取分类销售统计数据
    Route::get('finance/shopCategoryStat/:id', 'Finance/getFinanceShopCategoryStat');
    // 获取订单尾款统计数据
    Route::get('finance/shopReceivableStat/:id', 'Finance/getFinanceShopReceivableStat');
    // 订单基础数据列表
    Route::get('finance/orderBasic/:id', 'Finance/getFinanceOrderBasic');
    // 订单基础数据详情
    Route::get('finance/orderBasicById/:id', 'Finance/getFinanceOrderBasicById');


    // 积分
    // 创建积分任务
    Route::post('integral/task', 'IntegralTask/create');
    // 修改任务
    Route::put('integral/task/:id', 'IntegralTask/update');
    // 积分任务列表
    Route::get('integral/task', 'IntegralTask/index');
    // 积分任务详情
    Route::get('integral/task/:id', 'IntegralTask/detail');
    // 激活任务
    Route::put('integral/task/:id/active', 'IntegralTask/active');
    // 展示任务
    Route::put('integral/task/:id/display', 'IntegralTask/setDisplay');

    // 添加积分兑换规则
    Route::post('integral/exchange', 'IntegralExchange/create');
    // 修改积分兑换规则
    Route::put('integral/exchange/:id', 'IntegralExchange/update');
    // 积分兑换规则列表
    Route::get('integral/exchange', 'IntegralExchange/index');
    // 获取积分兑换规则详情
    Route::get('integral/exchange/:id', 'IntegralExchange/detail');

    // 修改积分价值
    Route::put('integral/value/:id', 'IntegralValue/update');
    // 获取积分价值列表
    Route::get('integral/value', 'IntegralValue/index');
    // 获取积分价值详情
    Route::get('integral/value/:id', 'IntegralValue/detail');

    // 获取积分日统计数据 /admin/points/stats/daily
    Route::get('integral/stats/daily', 'IntegralStats/daily');
    // 获取积分趋势统计数据 /admin/points/stats/trend
    Route::get('integral/stats/trend', 'IntegralStats/trend');
    // 获取积分汇总统计数据 /admin/points/stats/summary
    Route::get('integral/stats/summary', 'IntegralStats/summary');

    // 积分end

    // 渠道推广
    // 获取渠道表
    Route::get('channel', 'Channel/index');
    // 获取渠道详情
    Route::get('channel/:id', 'Channel/detail');
    // 修改渠道
    Route::post('channel/update', 'Channel/update');
    // 添加渠道
    Route::post('channel', 'Channel/create');
    // 删除渠道
    Route::delete('channel/:id', 'Channel/delete');
    // 渠道推广end

    // 活动
    // 活动添加
    Route::post('activity', 'Activity/create');
    // 活动列表
    Route::get('activity', 'Activity/index');
    // 活动报名列表
    Route::get('activity/signup', 'Activity/signupList');
    // 活动详情
    Route::get('activity/:id', 'Activity/detail');
    // 活动审核
    Route::post('activity/auditPost', 'Activity/audit');
    // 活动编辑
    Route::put('activity/:id', 'Activity/update');
    // 活动删除
    Route::delete('activity/:id', 'Activity/softDelete');
    // 活动end

    // 抽奖活动配置
    // 创建抽奖活动奖品
    Route::post('lottery/prize', 'Lottery/PrizeCreate');
    // 修改抽奖活动奖品
    Route::put('lottery/prize/:id', 'Lottery/PrizeUpdate');
    // 创建抽奖活动
    Route::post('lottery', 'Lottery/create');
    // 修改抽奖活动
    Route::put('lottery/:id', 'Lottery/update');
    // 获取抽奖活动列表
    Route::get('lottery', 'Lottery/index');
    // 获取中奖记录列表
    Route::get('lottery/prizeRecord', 'Lottery/prizeRecord');
    // 后台发放奖品(核销)
    Route::put('lottery/prizeRecord/send', 'Lottery/prizeRecordUpdate');
    // 获取抽奖活动详情
    Route::get('lottery/:id', 'Lottery/detail');
    // 删除抽奖活动奖品
    Route::delete('lottery/prize/:id', 'Lottery/PrizeDelete');
    // 删除抽奖活动
    Route::delete('lottery/:id', 'Lottery/delete');
    // 抽奖活动配置 end

    //优惠券模板添加
    Route::post('couponTemplate', 'Coupon/createCouponTemplate');
    //优惠券模板列表
    Route::get('couponTemplate', 'Coupon/couponTemplateList');
    //优惠券模板更新（除状态字段）
    Route::put('couponTemplate/:id', 'Coupon/updateCouponTemplate');
    //优惠券模板状态更新
    Route::put('couponTemplateStatus/:id', 'Coupon/updateCouponTemplateStatus');
    //优惠券模板详情
    Route::get('couponTemplate/:id', 'Coupon/getCouponTemplateById');
    //优惠券模板删除
    Route::delete('couponTemplate/:id', 'Coupon/deleteCouponTemplate');
    //优惠券池添加,定时脚本（test,此处需要删除）
    Route::post('couponPool', 'Coupon/createCouponPool');
    //优惠券添加 （手动发放优惠券）
    Route::post('coupon', 'Coupon/createCoupon');
    //优惠券列表
    Route::get('coupon', 'Coupon/couponList');
    //优惠券详情
    Route::get('coupon/:id', 'Coupon/getCouponById');

});
