<?php
// 应用公共文件

/**
 * 返回成功
 * @param array $data
 * @param string $msg
 * @return array
 */

use app\common\enum\Response;
use app\common\exceptions\InvalidResponseException;
use app\common\library\CurlTool;
use app\common\library\snow\SnowFlake;
use app\common\service\LogService;
use think\facade\Config;

if (!function_exists('success')) {
    function success(array $data = [], string $msg = ''): array
    {
        $res = [
            // 本次请求消耗的时间
            'time_taken' => bcsub(sprintf("%.20f", microtime(true)), sprintf("%.20f", $_SERVER['REQUEST_TIME_FLOAT']), 8),
            'code'       => Response::SUCCESS,
            'msg'        => !empty($msg) ? $msg : Response::ResponseCode[Response::SUCCESS],
            'data'       => !empty($data) ? $data : [],
        ];
        LogService::writeLog($res, 200);
        return $res;
    }
}

/**
 * 返回失败
 * @param string $msg
 * @param int $code
 * @param array $data
 * @return array
 */
if (!function_exists('fail')) {
    function fail(int $code = Response::ERROR, string $msg = '', array $data = []): array
    {
        $res = [
            // 本次请求消耗的时间
            'time_taken' => bcsub(sprintf("%.20f", microtime(true)), sprintf("%.20f", $_SERVER['REQUEST_TIME_FLOAT']), 8),
            'code'       => $code,
            'msg'        => !empty($msg) ? $msg : Response::ResponseCode[$code],
            'data'       => !empty($data) ? $data : [],
        ];
        LogService::writeLog($res, $code);
        return $res;
    }
}

/**
 * 统一密码加密方式，如需变动直接修改此处
 * @param string $password
 * @param string $type
 * @return string
 */
if (!function_exists('makePassword')) {
    function makePassword(string $password, string $type = 'md5'): string
    {
        if ($type == 'md5') {
            return md5(md5('ysa' . $password));
        } else {
            return password_hash($password, PASSWORD_DEFAULT);
        }
    }
}

/**
 * Generates an unique access token.
 *
 * Implementing classes may want to override this function to implement
 * other access token generation schemes.
 *
 * @return
 * An unique access token.
 *
 * @ingroup oauth2_section_4
 */
if (!function_exists('generateAccessToken')) {
    function generateAccessToken()
    {
        $tokenLen = 40;
        if (@file_exists('/dev/urandom')) { // Get 100 bytes of random data
            $randomData = file_get_contents('/dev/urandom', false, null, 0, 100) . uniqid(mt_rand(), true);
        } else {
            $randomData = mt_rand() . mt_rand() . mt_rand() . mt_rand() . microtime(true) . uniqid(mt_rand(), true);
        }

        return substr(hash('sha512', $randomData), 0, $tokenLen);
    }
}

if (!function_exists('validateDate')) {
    function validateDate($date, $format = 'Y-m-d')
    {
        $res = DateTime::createFromFormat($format, $date);
        return $res && $res->format($format) === $date;
    }
}

/**
 * curl请求指定url (get)
 * @param $url
 * @param array $data
 * @return mixed
 */
if (!function_exists('curl')) {
    function curl($url, $data = [])
    {
        // 处理get数据
        if (!empty($data)) {
            $url = $url . '?' . http_build_query($data);
        }
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HEADER, false);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false); //这个是重点。
        $result = curl_exec($curl);
        curl_close($curl);
        LogService::requestLog($url, $data, 'get', $result);
        return $result;
    }
}

/**
 * curl请求指定url (get),Restful
 * @param $url
 * @param array $data
 * @return mixed
 */
if (!function_exists('curlGet')) {
    function curlGet(string $url, int $id = 0, array $data = [])
    {
        // 处理get数据
        if (!empty($id)) {
            $url = $url . '/' . $id;
        }
        // 处理get数据
        if (!empty($data)) {
            $url = $url . '?' . http_build_query($data);
        }
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HEADER, false);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false); //这个是重点。
        $result = curl_exec($curl);
        curl_close($curl);
        LogService::requestLog($url, $data, 'get', $result);
        return $result;
    }
}


/**
 * curl请求指定url (post)
 * @param $url
 * @param array $data
 * @return mixed
 */
if (!function_exists('curlPost')) {
    function curlPost($url, $data = [], $isJson = false)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        if ($isJson == true) {
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-type: application/json;'
            ]);
        }
        $result = curl_exec($ch);
        curl_close($ch);
        LogService::requestLog($url, $data, 'post', $result);
        return $result;
    }
}

/**
 * curl请求指定url (put)
 * @param $url
 * @param array $data
 * @return mixed
 */
if (!function_exists('curlPut')) {
    function curlPut(string $url, array $data = [])
    {
        //$url = 'https://api.example.com/resource/123';
        //$data = ['name' => 'New Name', 'value' => 'Updated Value'];
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL            => $url,
            CURLOPT_CUSTOMREQUEST  => 'PUT',       // 强制指定为 PUT 方法
            CURLOPT_POSTFIELDS     => http_build_query($data), // 发送表单数据
            CURLOPT_RETURNTRANSFER => true,       // 返回结果而非直接输出
            CURLOPT_HTTPHEADER     => [
                'Content-Type: application/x-www-form-urlencoded' // 根据数据类型调整
            ]
        ]);

        $result = curl_exec($ch);
        curl_close($ch);
        LogService::requestLog($url, $data, 'PUT', $result);
        return $result;
    }
}
/**
 * curl请求指定url (delete)
 * @param $url
 * @param array $data
 * @return mixed
 */
if (!function_exists('curlDelete')) {
    //$url = 'https://api.example.com/resource/123';
    function curlDelete(string $url, int $data)
    {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL            => $url . '/' . $data,
            CURLOPT_CUSTOMREQUEST  => 'DELETE',   // 强制指定为 DELETE 方法
            CURLOPT_RETURNTRANSFER => true
        ]);

        $result = curl_exec($ch);
        curl_close($ch);
        LogService::requestLog($url, $data, 'DELETE', $result);
        return $result;
    }
}


if (!function_exists('curlGetApiContentByUrl')) {
    /**
     * 使用GET/DELETE方法从API获取数据
     *
     * @param string $url 请求URL
     * @param array $data 请求参数
     * @param array $header 请求头
     * @param bool $isDelete 是否为DELETE请求
     * @param int $curlRetry 重试次数
     * @return array 响应数据
     */
    function curlGetApiContentByUrl($url, $data = [], $header = [], $isDelete = false, $curlRetry = 3)
    {
        // 将参数添加到URL
        if (!empty($data)) {
            $url = $url . '?' . http_build_query($data);
        }

        // 创建请求实例
        $curl = new \app\common\library\CurlTool();
        $curl->retry($curlRetry);

        // 执行请求
        if ($isDelete) {
            $ret = !empty($header) ? $curl->header($header)->delete($url) : $curl->delete($url);
        } else {
            $ret = !empty($header) ? $curl->header($header)->get($url) : $curl->get($url);
        }
        // 处理响应
        if (empty($ret) || !isset($ret['body']) || $ret['error'] != 0) {
            return [];
        }

        // 解析JSON响应
        $dataList = json_decode($ret['body'], true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return [];
        }

        return $dataList;
    }
}

if (!function_exists('curlPostApiContentByUrlAndParams')) {
    /**
     * 使用POST/PUT方法向API发送数据
     *
     * @param string $url 请求URL
     * @param array|string $params 请求参数
     * @param array $header 请求头
     * @param bool $isPut 是否为PUT请求
     * @param int $curlRetry 重试次数
     * @return array 响应数据
     */
    function curlPostApiContentByUrlAndParams($url, $params = [], $header = [], $isPut = false, $curlRetry = 3)
    {
        // 创建请求实例
        $curl = new \app\common\library\CurlTool();
        $curl->retry($curlRetry);

        // 设置请求头（如果有）
        if (!empty($header)) {
            $curl->header($header);
        }

        // 执行请求
        if ($isPut) {
            // 对于PUT请求
            if (!empty($params)) {
                // 如果有参数，设置参数
                $curl->post($params);
            }
            // 执行PUT请求
            $ret = $curl->put($url);
        } else {
            // 对于POST请求
            if (empty($params)) {
                // 如果没有参数，执行空的POST请求
                $ret = $curl->post([])->submit($url);
            } else {
                // 如果有参数，正常执行
                $ret = $curl->post($params)->submit($url);
            }
        }

        // 处理响应
        if (empty($ret) || !isset($ret['body']) || $ret['error'] != 0) {
            if ($ret['error'] != 0 && isset($ret['body'])) {
                return json_decode($ret['body'], true);
            }
            return [];
        }

        // 解析JSON响应
        $dataList = json_decode($ret['body'], true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return [];
        }

        return $dataList;
    }
}

/**
 * Tencent 多人音视频会议（POST）
 * @param $url
 * @param array $data
 * @return mixed
 */
if (!function_exists('curlPostTencent')) {
    function curlPostTencent($url, $data = [])
    {
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        $result = curl_exec($ch);
        curl_close($ch);
        return $result;
    }
}

/**
 * 格式化商品封面图片
 */
if (!function_exists('formatGoodsCover')) {
    function formatGoodsCover($file = false)
    {
        $domain       = Config::get('upload.ysa.domain');
        $defaultImage = Config::get('upload.defaultImage');
        return $file ? $file : $domain . $defaultImage['goodsCover'];
    }
}

/**
 * 格式化门店logo
 */
if (!function_exists('formatLogo')) {
    function formatLogo($file = false)
    {
        $domain       = Config::get('upload.ysa.domain');
        $defaultImage = Config::get('upload.defaultImage');
        return $file ? $file : $domain . $defaultImage['shopLogo'];
    }
}

/**
 * 格式化头像
 */
if (!function_exists('formatAvatar')) {
    function formatAvatar($file = false)
    {
        $domain       = Config::get('upload.ysa.domain');
        $defaultImage = Config::get('upload.defaultImage');
        return $file ? $file : $domain . $defaultImage['avatar'];
    }
}

// 根据日期范围循环出每一天日期和星期
if (!function_exists('formatDateRange')) {
    function formatDateRange($start, $end)
    {
        $WEEKS = ['Monday' => 1, 'Tuesday' => 2, 'Wednesday' => 3, 'Thursday' => 4, 'Friday' => 5, 'Saturday' => 6, 'Sunday' => 7];
        $start = new DateTime($start);
        $end   = new DateTime($end);
        $end->add(new DateInterval('P1D')); // 添加一天来包含结束日期
        $interval = new DateInterval('P1D');
        $period   = new DatePeriod($start, $interval, $end);
        $dates    = [];
        foreach ($period as $date) {
            // 日期 => 星期
            $dates[$date->format('Y-m-d')] = $WEEKS[$date->format('l')];
        }
        return $dates;
    }
}
if (!function_exists('arrayToTree')) {
    function arrayToTree($items, $parentId = 0, $name = 'id', $pname = 'pid')
    {
        $tree = [];
        foreach ($items as $item) {
            if ($item[$pname] == $parentId) {
                $children = arrayToTree($items, $item[$name], $name, $pname);
                if ($children) {
                    $item['children'] = $children;
                }
                $tree[] = $item;
            }
        }
        return $tree;
    }
}

/**
 * 生成订单号(雪花算法)
 * @param string $prefix
 * @return string
 */
if (!function_exists('makeOrderSn')) {
    function makeOrderSn(string $prefix = ''): string
    {
        return $prefix . SnowFlake::createOnlyId();
    }
}
if (!function_exists('changeEnumKeyValue')) {
    function changeEnumKeyValue($data)
    {
        if (empty($data)) return [];
        foreach ($data as $key => $value) {
            $list[] = [
                'key'   => $key,
                'value' => $value
            ];
        }
        return $list;
    }
}

/**
 * 数组转JSON字符串
 * @param array $data
 * @return string Json字符串
 */
if (!function_exists('arrayToJson')) {
    function arrayToJson(array $data)
    {
        $json = json_encode($data, JSON_UNESCAPED_UNICODE);
        return $json === '[]' ? '{}' : $json;
    }
}

/**
 * 解析JSON内容到数组
 * @param string $json
 * @return array
 */
if (!function_exists('jsonToArray')) {
    function jsonToArray(string $json)
    {
        $result = json_decode($json, true);
        if (empty($result)) {
            throw new InvalidResponseException('invalid response.', '0');
        }
        if (!empty($result['errcode'])) {
            throw new InvalidResponseException($result['errmsg'], $result['errcode'], $result);
        }
        return $result;
    }
}

/**
 * 获取随机数
 * @param int $num
 * @return string
 */
if (!function_exists('makeRandStr')) {
    function makeRandStr(int $num = 8): string
    {
        return substr(implode('', array_map('ord', str_split(substr(uniqid(), 7, 13), 1))), 0, $num);
    }
}

if (!function_exists('getLuaFile')) {
    function getLuaFile($name)
    {
        return base_path() . 'common/script/' . $name . '.lua';
    }
}
/**
 * go项目分页适配tp分页的数据结构
 */
if (!function_exists('dealPaginateData')) {
    function dealPaginateData($data = [])
    {
        return $data;
        if (empty($data)) {
            return $data;
        }
        if (empty($data['data'])) {
            return $data;
        }
        $result['total']        = $data['data']['total'];
        $result['per_page']     = $data['data']['page_size'];
        $result['current_page'] = $data['data']['page'];
        $result['last_page']    = $data['data']['last_page'];
        $result['data']         = $data['data']['rows'];
        $data['data']           = $result;
        return $data;
    }
}


// 数字专人民币
if (!function_exists('numberToRmb')) {
    function numberToRmb($number)
    {
        $cny_arr = array("零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖");
        $dw_arr  = array("", "拾", "佰", "仟", "万", "拾", "佰", "仟", "亿");
        $unit    = array("元", "角", "分");
        $str     = ""; // 转化为字符串
        $number  = number_format($number, 2, '.', '');
        $number  = explode('.', $number);
        $int     = $number[0];
        $dec     = $number[1];
        $dec_len = strlen($dec);
        if ($int == 0) {
            $str = "零元";
        } else {
            // 整数部分
            $str = convertInt($int);
            $str .= "元";
        }
        // 小数部分
        if ($dec_len == 1) {
            $str .= $cny_arr[$dec[0]] . $unit[1];
        } else if ($dec_len == 2) {
            $str .= $cny_arr[$dec[0]] . $unit[1];
            $str .= $cny_arr[$dec[1]] . $unit[2];
        }
        return $str;
    }
}

if (!function_exists('convertInt')) {
    function convertInt($int)
    {
        $cny_arr = array("零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖");
        $dw_arr  = array("", "拾", "佰", "仟", "万", "拾", "佰", "仟", "亿");
        $str     = "";
        $int_len = strlen($int);
        $dw      = $int_len - 1;
        for ($i = 0; $i < $int_len; $i++) {
            $str .= $cny_arr[$int[$i]] . $dw_arr[$dw];
            $dw--;
        }
        return $str;
    }
}
