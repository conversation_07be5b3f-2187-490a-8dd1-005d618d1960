<?php

declare(strict_types=1);

namespace app\admin\controller;

use app\common\controller\Base;
use app\common\enum\Response;
use app\common\service\ActivityService;
use app\common\validate\ActivityValidate;
use think\response\Json;

class Activity extends Base
{

    /**
     * 创建活动
     * @return \think\response\Json
     */
    public function create(): Json
    {
        $param         = $this->param;
        $param['type'] = 1;
        $validate      = new ActivityValidate();
        if (!$validate->scene('create')->check($param)) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }
        $activity = (new ActivityService())->create($param);
        if ($activity) {
            return json(success(['创建活动成功']));
        }
        return json(fail());
    }

    /**
     * 列表页
     * @return Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function index(): Json
    {
        $param = $this->param;
        $list  = (new ActivityService())->getList($param);
        return json(success($list));
    }

    /**
     * 活动报名列表
     * @return Json
     * @throws \think\db\exception\DbException
     */
    public function signupList(): Json
    {
        $param = $this->param;
        $list  = (new ActivityService())->signupList($param);
        return json(success($list));
    }

    /**
     * 活动详情
     * @return Json
     */
    public function detail(): Json
    {
        $param    = $this->param;
        $validate = new ActivityValidate();
        if (!$validate->scene('detailSDK')->check($param)) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }
        $activity = (new ActivityService())->detail($param);
        return json(success($activity));
    }

    /**
     * 活动编辑
     * @return Json
     */
    public function update(): Json
    {
        try {
            $param    = $this->param;
            $activity = (new ActivityService())->update($param);
            if ($activity) {
                return json(success(['活动编辑成功']));
            }
            return json(fail());
        } catch (\Exception $e) {
            return json(fail(Response::DATA_NOT_FOUND, $e->getMessage()));
        }
    }

    /**
     * 活动删除
     * @return Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function softDelete(): Json
    {
        try {
            $param    = $this->param;
            $activity = (new ActivityService())->softDelete($param);
            if ($activity) {
                return json(success(['活动删除成功']));
            }
            return json(fail());
        } catch (\Exception $e) {
            return json(fail(Response::DATA_NOT_FOUND, $e->getMessage()));
        }
    }
}
