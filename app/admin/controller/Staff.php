<?php

namespace app\admin\controller;

use app\common\controller\Base;
use app\common\enum\Response;
use app\common\service\StaffService;
use app\admin\validate\StaffAudit as StaffAuditValidate;
use app\admin\validate\StaffInsure as StaffInsureValidate;
use app\admin\validate\StaffWorkCalendar as StaffWorkCalendarValidate;
use app\common\service\StaffCacheService;
use think\response\Json;

class Staff extends Base
{
    // 获取列表
    public function index(): Json
    {
        $shopId = $this->param['shop_id'] ?? 0;
        if ($shopId == 0) {
            return json(fail(Response::REQUEST_PARAM_ERROR, 'shop_id不能为空'));
        }
        $service = new StaffService();
        return json(success($service->getList($this->param)));
    }

    // 获取工作日历
    public function getWorkCalendar(): Json
    {
        $validate = new StaffWorkCalendarValidate();
        $result = $validate->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }
        $service = new StaffService();
        return json(success($service->getWorkCalendar($this->param)));
    }

    // 获取门店技师列表, 带搜索, 后台筛选用
    public function getStaffBySearchName(): Json
    {
        $shopId = $this->param['shop_id'] ?? 0;
        if ($shopId == 0) {
            return json(fail(Response::REQUEST_PARAM_ERROR, 'shop_id不能为空'));
        }
        $service = new StaffService();
        return json(success($service->getShopStaff($this->param)));
    }

    // 根据id获取详情
    public function detail(): Json
    {
        $id = intval($this->param['id'] ?? 0);
        $staff = (new StaffCacheService($id))->getData();
        if (empty($staff)) {
            return json(fail(Response::DATA_NOT_FOUND));
        }
        if ($staff['shop_id'] != $this->param['shop_id']) {
            return json(fail(Response::NOT_PERMISSION));
        }
        return json(success($staff));
    }

    // 审核技师
    public function audit(): Json
    {
        $validate = new StaffAuditValidate();
        $result = $validate->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }
        $service = new StaffService();
        return json($service->edit($this->param));
    }

    // 审核认证信息
    public function authnAudit(): Json
    {
        $validate = new StaffAuditValidate();
        $result = $validate->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }
        $service = new StaffService();
        return json($service->editAuthImage($this->param));
    }

    // 审核培训信息
    public function trainAudit(): Json
    {
        $validate = new StaffAuditValidate();
        $result = $validate->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }
        $service = new StaffService();
        return json($service->editTrain($this->param));
    }

    // 上传保险
    public function addInsure(): Json
    {
        $validate = new StaffInsureValidate();
        $result = $validate->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }
        $service = new StaffService();
        return json($service->addInsure($this->param));
    }

    // 删除保险
    public function deleteInsure(): Json
    {
        $service = new StaffService();
        return json($service->delInsure($this->param));
    }

    public function setWorker(): Json
    {
        $service = new StaffService();
        if (isset($this->param['worker_codes'])) {
            $res = $service->setWorker($this->param);
            if ($res) {
                return json(success([], '职业类型修改成功'));
            }
            return json(fail(Response::ERROR, '职业类型修改失败'));
        }

        $res = $service->setWorkerSort($this->param);
        if ($res) {
            return json(success([], '职业排序值修改成功'));
        }
        return json(fail(Response::ERROR, '职业排序值修改失败'));
    }

    public function getCommissionList(): Json
    {
        $service = new StaffService();
        $list = $service->getCommissionList($this->param);
        return json(success($list));
    }
}
