<?php
declare(strict_types=1);

namespace app\admin\controller;

use app\common\controller\Base;
use app\common\enum\Response;
use app\common\service\CouponService;
use app\common\validate\Coupon as CouponValidate;
use app\common\validate\CouponTemplate as CouponTemplateValidate;
use think\facade\Db;
use think\response\Json;

class Coupon extends Base
{
    const TENANT_TYPE = 'store'; //租户类型（ysa-auth）,sdk模块下传值platform;admin模块下传值store

    /**
     * 优惠券模板添加
     * @return Json
     */
    public function createCouponTemplate(): Json
    {
        $params = $this->param;
        $data   = [
            'tenant_type'      => self::TENANT_TYPE,
            'type'             => $params['type'] ?? 'PLATFORM', //类型：PLATFORM-平台券，SHOP-店铺券
            'name'             => $params['name'] ?? '', //模板名称
            'shop_id'          => $params['shop_id'] ?? '', //店铺券关联商家ID（平台券为NULL）
            'amount'           => $params['amount'] ?? '', //面值/折扣率（如50.00为满减金额，0.8为8折）
            'min_order_amount' => $params['min_order_amount'] ?? '', //使用门槛金额
            'total_count'      => $params['total_count'] ?? '', //总发放数量（-1表示不限量）
            'user_limit'       => $params['user_limit'] ?? '', //单用户领取上限
            'cost_ratio'       => $params['cost_ratio'] ?? '', //成本分摊比例（格式：平台:商家，如70:30）
            'valid_type'       => $params['valid_type'] ?? '', //有效期类型：1-固定时间，2-领取后N天有效
            'start_time'       => $params['start_time'] ?? '', //固定有效期开始时间
            'end_time'         => $params['end_time'] ?? '', //固定有效期结束时间
            'valid_days'       => $params['valid_days'] ?? '', //领取后有效天数（相对有效期时生效）
            'scope_type'       => $params['scope_type'] ?? '', //适用范围：1-全场通用，2-指定品类，3-指定商品
        ];
        //添加参数验证器
        $validate = new CouponTemplateValidate();
        if (!$validate->scene('createCouponTemplate')->check($params)) {
            return json(fail(Response::REQUEST_ERROR, $validate->getError()));
        }

        $token  = $this->request->token;
        $result = CouponService::createCouponTemplate($data, $token, "admin");
        if ($result['code'] != 1) {
            return json(fail(Response::REQUEST_ERROR, $result['msg']));
        }

        return json(success($result['data'] ?? []));
    }

    /**
     * 优惠券模板列表
     * @return Json
     */
    public function couponTemplateList(): Json
    {
        $shopName = $this->param['shop_name'] ?? ''; //公司名称
        $shopIds  = '';
        if (!empty($shopName)) {
            // shop_name 模糊查询
            $where   = [
                ['shop_name', 'like', '%' . $shopName . '%'],
                ['status', '=', '1']
            ];
            $shops   = Db::name('shop')->where($where)->column('id');
            $shopIds = empty($shops) ? '' : implode(',', $shops);
        }
        $data   = [
            'tenant_type' => self::TENANT_TYPE,
            'page'        => $this->param['page'] ?? 1,
            'page_size'   => $this->param['page_size'] ?? 10,
            //搜索条件
            'name'        => $this->param['name'] ?? '', //模板名称
            'status'      => $this->param['status'] ?? '',//模板状态
            'shop_ids'    => $shopIds,
        ];
        $token  = $this->request->token;
        $result = CouponService::couponTemplateList($data, $token, "admin");
        if ($result['code'] != 1) {
            return json(fail(Response::REQUEST_ERROR, $result['msg']));
        }

        $couponTemplates = $result['data']['rows'];
        if (!empty($couponTemplates)) {
            foreach ($couponTemplates as $key => $couponTemplate) {
                // 处理shop_name 字段数据
                $couponTemplates[$key]['shop_name'] = '';
                if (!empty($couponTemplate['shop_id'])) {
                    $couponTemplates[$key]['shop_name'] = Db::name('shop')->where('id', $couponTemplate['shop_id'])->value('shop_name') ?? '';
                }
            }
        }
        $result['data']['rows'] = $couponTemplates;
        return json(success($result['data'] ?? []));
    }


    /**
     * 优惠券模板更新
     * @return Json
     */
    public function updateCouponTemplate(): Json
    {
        $params = $this->param;
        $data   = [
            'tenant_type'      => self::TENANT_TYPE,
            'id'               => $params['id'],
            'type'             => $params['type'] ?? 'PLATFORM', //类型：PLATFORM-平台券，SHOP-店铺券
            'name'             => $params['name'] ?? '', //模板名称
            'shop_id'          => $params['shop_id'] ?? '', //店铺券关联商家ID（平台券为NULL）
            'amount'           => $params['amount'] ?? '', //面值/折扣率（如50.00为满减金额，0.8为8折）
            'min_order_amount' => $params['min_order_amount'] ?? '', //使用门槛金额
            'total_count'      => $params['total_count'] ?? '', //总发放数量（-1表示不限量）
            'user_limit'       => $params['user_limit'] ?? '', //单用户领取上限
            'cost_ratio'       => $params['cost_ratio'] ?? '', //成本分摊比例（格式：平台:商家，如70:30）
            'valid_type'       => $params['valid_type'] ?? '', //有效期类型：1-固定时间，2-领取后N天有效
            'start_time'       => $params['start_time'] ?? '', //固定有效期开始时间
            'end_time'         => $params['end_time'] ?? '', //固定有效期结束时间
            'valid_days'       => $params['valid_days'] ?? '', //领取后有效天数（相对有效期时生效）
            'scope_type'       => $params['scope_type'] ?? '', //适用范围：1-全场通用，2-指定品类，3-指定商品
            //'status'         => $params['status'] ?? '',//状态：PENDING-待审核，ACTIVE-生效，INACTIVE-禁用
        ];
        //添加参数验证器
        $validate = new CouponTemplateValidate();
        if (!$validate->scene('updateCouponTemplate')->check($params)) {
            return json(fail(Response::REQUEST_ERROR, $validate->getError()));
        }
        $token  = $this->request->token;
        $result = CouponService::updateCouponTemplate($data, $token, "admin");
        if ($result['code'] != 1) {
            return json(fail(Response::REQUEST_ERROR, $result['msg']));
        }

        return json(success($result['data'] ?? []));
    }


    /**
     * 优惠券模板详情
     * @return Json
     */
    public function getCouponTemplateById(): Json
    {
        $params = [
            'tenant_type' => self::TENANT_TYPE,
            'id'          => $this->param['id'] ?? 0
        ];
        //添加参数验证器
        $validate = new CouponTemplateValidate();
        if (!$validate->scene('id')->check($params)) {
            return json(fail(Response::REQUEST_ERROR, $validate->getError()));
        }
        $token  = $this->request->token;
        $result = CouponService::getCouponTemplateById($params, $token, "admin");
        if ($result['code'] != 1) {
            return json(fail(Response::REQUEST_ERROR, $result['msg']));
        }
        $couponTemplate = $result['data'];
        if (empty($couponTemplate)) {
            return json(success($couponTemplate ?? []));
        }
        if (empty($couponTemplate['shop_id'])) {
            $couponTemplate['shop_name'] = '';
        } else {
            $where                       = [
                'id'     => $couponTemplate['shop_id'],
                'status' => 1,
            ];
            $couponTemplate['shop_name'] = Db::name('shop')->where($where)->value('shop_name');
        }

        return json(success($couponTemplate ?? []));
    }

    /**
     * 优惠券模板删除
     * @return Json
     */
    public function deleteCouponTemplate(): Json
    {
        $params = [
            'tenant_type' => self::TENANT_TYPE,
            'id'          => $this->param['id'] ?? 0
        ];
        //添加参数验证器
        $validate = new CouponTemplateValidate();
        if (!$validate->scene('id')->check($params)) {
            return json(fail(Response::REQUEST_ERROR, $validate->getError()));
        }
        $token  = $this->request->token;
        $result = CouponService::deleteCouponTemplate($params, $token, "admin");
        if ($result['code'] != 1) {
            return json(fail(Response::REQUEST_ERROR, $result['msg']));
        }

        return json(success($result['data'] ?? []));
    }

    //优惠券池添加
    public function createCouponPool(): Json
    {
        $data   = [
            'tenant_type' => self::TENANT_TYPE,
        ];
        $token  = $this->request->token;
        $result = CouponService::createCouponPool($data, $token, "admin");
        if ($result['code'] != 1) {
            return json(fail(Response::REQUEST_ERROR, $result['msg']));
        }

        return json(success($result['data'] ?? []));
    }

    /**
     * 优惠券添加 （手动发放优惠券）
     * @return Json
     */
    public function createCoupon(): Json
    {
        $params = $this->param;
        $data   = [
            'tenant_type' => self::TENANT_TYPE,
            'template_id' => $params['template_id'], //关联模板ID
            'shop_id'     => $params['shop_id'] ?? '', //店铺券关联商家ID（平台券为NULL）
            'member_id'   => $params['member_id'] ?? '', //领取用户ID

        ];
        //添加参数验证器
        $validate = new CouponValidate();
        if (!$validate->scene('createCoupon')->check($data)) {
            return json(fail(Response::REQUEST_ERROR, $validate->getError()));
        }
        $token  = $this->request->token;
        $result = CouponService::createCoupon($data, $token, "admin");
        if ($result['code'] != 1) {
            return json(fail(Response::REQUEST_ERROR, $result['msg']));
        }

        return json(success($result['data'] ?? []));
    }

    /**
     * 优惠券列表
     * @return Json
     */
    public function couponList(): Json
    {
        $data   = [
            'tenant_type' => self::TENANT_TYPE,
            'page'        => $this->param['page'] ?? 1,
            'page_size'   => $this->param['page_size'] ?? 10,
        ];
        $token  = $this->request->token;
        $result = CouponService::couponList($data, $token, "admin");
        if ($result['code'] != 1) {
            return json(fail(Response::REQUEST_ERROR, $result['msg']));
        }
        $couponList = &$result['data']['rows'];
        if (empty($couponList)) {
            return json(success($result['data'] ?? []));
        }
        foreach ($couponList as $key => $value) {
            if (empty($value['shop_id'])) {
                $couponList[$key]['shop_name'] = '';
            } else {
                $where                         = [
                    'id'     => $value['shop_id'],
                    'status' => 1,
                ];
                $couponList[$key]['shop_name'] = Db::name('shop')->where($where)->value('shop_name');
            }
        }
        return json(success($result['data'] ?? []));
    }

    /**
     * 优惠券详情
     * @return Json
     */
    public function getCouponById(): Json
    {
        $data  = [
            'tenant_type' => self::TENANT_TYPE,
            'id'          => $this->param['id'] ?? 0
        ];
        $token = $this->request->token;
        //添加参数验证器
        $validate = new CouponValidate();
        if (!$validate->scene('id')->check($data)) {
            return json(fail(Response::REQUEST_ERROR, $validate->getError()));
        }
        $result = CouponService::getCouponById($data, $token, "admin");
        if ($result['code'] != 1) {
            return json(fail(Response::REQUEST_ERROR, $result['msg']));
        }

        $coupon = $result['data'];
        if (empty($coupon)) {
            return json(success($coupon ?? []));
        }
        if (empty($coupon['shop_id'])) {
            $coupon['shop_name'] = '';
        } else {
            $where               = [
                'id'     => $coupon['shop_id'],
                'status' => 1,
            ];
            $coupon['shop_name'] = Db::name('shop')->where($where)->value('shop_name');
        }

        return json(success($coupon ?? []));
    }
}