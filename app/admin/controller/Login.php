<?php

declare(strict_types=1);

namespace app\admin\controller;

use think\response\Json;
use app\admin\validate\Login as LoginValidate;
use app\common\controller\Base;
use app\common\enum\Response;
use app\common\service\AdminPowerService;
use app\common\service\AdminService;
use app\common\service\LoginService;
use app\common\service\ShopService;
use think\facade\Db;
use app\admin\validate\ResetPassword as ResetPasswordValidate;

class Login extends Base
{
    public function index(): Json
    {
        $validate = new LoginValidate();
        $result = $validate->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $validate->getError()));
        }
        $config = config("admin.services");
        $param = [
            'username' => $this->param['username'],
            'password' => $this->param['password'],
            'tenant_type' => $config['TenantType'],
        ];
        $url = $config['Auth']['BaseURL'] . $config['Auth']['Api']['Login'];
        $res = curlPostApiContentByUrlAndParams($url, $param);
        if ($res['code'] != 1) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $res['msg']));
        }
        return json(success($res['data']));
    }

    public function logout(): Json
    {
        $service = new LoginService();
        $service->loginOut('admin', $this->request->adminAuth['id'], $this->request->token);
        return json(success());
    }

    public function detail(): Json
    {
        $loginAdmin = $this->request->adminAuth;
        $role = $this->request->adminRole;

        $service = new ShopService();
        $shop = $service->getShopData($this->param['shop_id']);
        if (isset($shop['code']) && $shop['code'] != Response::SUCCESS) {
            return json($shop);
        }
        // 菜单处理
        $config = config('admin.services');
        $res =  curlGetApiContentByUrl($config['Auth']['BaseURL'] . $config['Auth']['Api']['GetRouters'], [], ['Authorization: Bearer ' . $this->request->token]);
        if ($res['code'] != 1) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $res['msg']));
        }
        $menus = $res['data'];

        // 不使用权限系统的菜单
        // $adminPowerService = new AdminPowerService();
        // $menus = $adminPowerService->getAdminMenus();

        // 日期
        $statRes = [
            'order_count' => 0,
            'order_amount' => 0,
            'pay_amount' => 0,
            'pay_count' => 0,
            'refund_amount' => 0,
            'refund_count' => 0,
            'staff_count' => 0,
            'interview_count' => 0,
            'start_order_count' => 0,
            'finish_order_count' => 0,
            'wait_order_count' => 0,
            'service_count' => 0,
            'wait_interview_count' => 0,
        ];
        $stat = Db::table('shop_stat')
            ->field(array_keys($statRes))
            ->where('shop_id', $this->param['shop_id'])
            ->where('daliy', date('Ymd'))
            ->find();
        if (!empty($stat)) {
            $statRes =   $stat;
        }
        $service = new AdminService();
        $data = [
            'user_id' => $loginAdmin['id'],
            'user' => $service->getAdminData(intval($loginAdmin['id'])),
            'role' => $role,
            'menu' => $menus,
            // 'rule' => $role['rule'],
            'shop' => [
                'shop_id' => $this->param['shop_id'],
                'logo_id' => $shop['logo_id'] ?? '',
                'format_logo' => $shop['format_logo'] ?? '',
                'shop_name' => $shop['shop_name'] ?? '',
                'contact_phone' => $shop['contact_phone'] ?? '',
                'opening_hours' => $shop['opening_hours'] ?? '',
            ],
            'stat' => $statRes,
        ];
        return json(success($data));
    }

    public function resetPassword(): Json
    {
        $token = $this->request->token;
        $validate = new ResetPasswordValidate();
        $result   = $validate->check($this->param);
        if (!$result) {
            return json(fail(Response::REQUEST_PARAM_ERROR, (string)$validate->getError()));
        }
        // $service = new LoginService();
        // return json($service->resetPassword($this->param));
        $config = config("admin.services");
        $param = [
            'oldPassword' => $this->param['old_passwd'],
            'newPassword' => $this->param['new_passwd'],
            'confirmPwd' => $this->param['new_passwd'],
            'tenant_type' => $config['TenantType'],
        ];
        $url = $config['Auth']['BaseURL'] . $config['Auth']['Api']['UpdatePwd'];
        $res = curlPostApiContentByUrlAndParams($url, $param, ['Authorization: Bearer ' . $token], true);
        if ($res['code'] != 1) {
            return json(fail(Response::REQUEST_PARAM_ERROR, $res['msg']));
        }
        return json(success());
    }
}
