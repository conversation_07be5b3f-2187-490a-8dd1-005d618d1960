<?php
declare(strict_types=1);

namespace app\admin\controller;

use app\admin\validate\FinanceOrderBasic;
use app\api\controller\Base;
use app\common\enum\Response;
use app\common\service\FinanceService;
use think\response\Json;

class Finance extends Base
{

    /**
     * 获取店铺统计
     * @return array|\think\response\Json
     */
    public function getShopStat(): Json
    {
        $shopId = $this->param['shop_id'] ?? 0;
        if (empty($shopId)) {
            return json(success([]));
        }
        $response = FinanceService::getShopStat($shopId);
        return json(success($response));
    }

    //分类（商品二级分类）占比统计
    public function getShopCategoryStat(): Json
    {
        return json(success([]));
    }

    //订单基础数据列表
    public function shopReceivableStat(): Json
    {
        return json(success([]));
    }

    //应收账款统计
    public function getFinanceOrderBasic(): <PERSON><PERSON>
    {
        $shopId                        = $this->param['shop_id'] ?? 0;
        $param                         = $this->param['page'] ?? 1;
        $queryArray['order_no']        = $this->param['order_no'] ?? "";
        $queryArray['member_nickname'] = $this->param['member_nickname'] ?? "";
        $queryArray['member_mobile']   = $this->param['member_mobile'] ?? "";
        $result                        = FinanceService::getFinanceOrderBasic((int)$shopId, (array)$param, $queryArray);
        if ($result) {
            return json(success($result));
        }
        return json(fail(Response::DATA_NOT_FOUND));
    }

    //订单基础数据详情
    public function getFinanceOrderBasicById(): Json
    {
        $id = $this->param['id'] ?? 0; //记录ID
        if (empty($id)) {
            return json(success([]));
        }
        $result = FinanceService::getFinanceOrderBasicById((int)$id);
        if ($result) {
            return json(success($result));
        }
        return json(fail(Response::DATA_NOT_FOUND));
    }

    /**
     * 订单基础数据更新
     * @return Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function updateFinanceOrderBasic(): Json
    {
        $id = $this->param['id'] ?? 0;
        //$shopId               = $this->param['shop_id'] ?? 0;
        $params               = $this->request->put();
        $update['cost_price'] = $params['cost_price'] ?? 0;//成本价
        //$update['tail_amount']     = $params['tail_amount'] ?? 0;//尾款金额
        //$update['discount']        = $params['discount'] ?? 0;//优惠金额（积分、优惠券、减免金额等），json格式存储
        $update['staff_salary']    = $params['staff_salary'] ?? 0;//员工工资（本订单支付的工资）
        $update['staff_bonus']     = $params['staff_bonus'] ?? 0;//员工奖金（绩效/项目奖金）
        $update['staff_deduction'] = $params['staff_deduction'] ?? 0;//员工罚款（考勤/违规扣款）
        $update['material']        = $params['material'] ?? 0;//项目耗材
        $update['material_remark'] = $params['material_remark'] ?? '';//项目耗材说明
        $update['transport']       = $params['transport'] ?? 0;//交通费
        $update['insurance']       = $params['insurance'] ?? 0;//保险费
        $update['other']           = $params['other'] ?? '';//其他收费项，json格式存储
        if (!empty($id)) {
            $result = FinanceService::updateFinanceOrderBasic((int)$id, $update);
            if ($result) {
                return json(success([$result]));
            }
        }
        return json(fail(Response::DATA_NOT_FOUND));
    }

    //获取分类销售统计数据
    public function getFinanceShopCategoryStat(): Json
    {
        $shopId   = $this->param['shop_id'] ?? 0;
        $statDate = $this->param['stat_date'] ?? date('Y-m-01');
        $result   = FinanceService::getFinanceShopCategoryStat((int)$shopId, $statDate);
        return json(success($result));
    }

    //获取订单尾款统计数据
    public function getFinanceShopReceivableStat(): Json
    {
        $shopId    = $this->param['shop_id'] ?? 0;
        $startDate = $this->param['start_date'] ?? date('Y-m-01', strtotime('-5 month +1 second', strtotime(date('Y-m-d H:i:s')))); // 当前日期开始5个月前
        $endDate   = $this->param['end_date'] ?? date('Y-m-01', strtotime('+6 month -1 second', strtotime(date('Y-m-d H:i:s')))); // 当前日期开始6个月后;
        $result    = FinanceService::getFinanceShopReceivableStat((int)$shopId,$startDate,$endDate);
        return json(success($result));
    }
}