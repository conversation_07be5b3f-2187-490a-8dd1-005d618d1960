# 升级版event任务
*/1 * * * * /usr/local/bin/php /www/ysa-shop/think transaction >> /dev/null 2>&1

# 定时查询微信订单回调, 超时3分钟至10分钟的订单
*/1 * * * * /usr/local/bin/php /www/ysa-shop/think order:notify >> /dev/null 2>&1
# 自动分账
*/3 * * * * /usr/local/bin/php /www/ysa-shop/think order:profitsharing >> /dev/null 2>&1
# 自动分账后结算
###*/3 * * * * /usr/local/bin/php /www/ysa-shop/think order:settlement >> /dev/null 2>&1

# 门店统计
*/30 * * * *  /usr/local/bin/php /www/ysa-shop/think shop:statistics >> /dev/null 2>&1

# 超时3分钟至10分钟的订单,定时请求回调
*/1 * * * * /usr/local/bin/php /www/ysa-shop/think order:notify >> /dev/null 2>&1

# 超时半小时, 自动取消订单
*/1 * * * * /usr/local/bin/php /www/ysa-shop/think order:paytimeout >> /dev/null 2>&1

# 每月30号清除非当前月项目runtime目录下数据
#0 0 30 * * /home/<USER>/ysa-shop/delRuntime.sh >> /dev/null 2>&1

# 每月30号清除非当前月项目runtime目录下数据
#0 0 30 * * /home/<USER>/ysa-shop/delRuntime.sh >> /dev/null 2>&1

# 给尾款没有支付的用户发送短信
*/1 * * * * /usr/local/bin/php /www/ysa-shop/think order:unpayOrderNotify >> /dev/null 2>&1

# 财务管理，门店统计，每小时执行一次
* */1 * * * /usr/local/bin/php /www/ysa-shop/think finance:shopStat >> /dev/null 2>&1

# 财务管理，门店订单数据统计,每6小时执行一次
* */6 * * * /usr/local/bin/php /www/ysa-shop/think finance:orderBasic >> /dev/null 2>&1

# 财务管理，门店分类销售统计，每天1点执行一次
0 1 * * * /usr/local/bin/php /www/ysa-shop/think finance:shopCategoryStat >> /dev/null 2>&1

# 财务管理，门店订单尾款统计，每天1点30分执行一次
30 1 * * * /usr/local/bin/php /www/ysa-shop/think finance:shopReceivableStat >> /dev/null 2>&1


#优惠券池创建，每天凌晨2点30分执行一次
30 2 * * * /home/<USER>/ysa-member/coupon-pool-create -config /home/<USER>/ysa-member/configs/test
#优惠券过期检查
1 */3 * * * /home/<USER>/ysa-member/coupon-check-expire -config /home/<USER>/ysa-member/configs/test

#### 最后一行不要删除, 所有的定时脚本加在上面
