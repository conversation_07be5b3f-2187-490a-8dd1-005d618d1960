<?php
return [
    'limit' => 10, //分页限制
    'cacheSwitch' => true, //缓存是否开启 true-开启
    'timeout'     => 3600, //缓存时长 单位秒
    'junziqian' => [
        'BaseURL' => env('JZQ_API', 'https://api.sandbox.junziqian.com'), //测试环境
        'NotifyUrl' => env('JZQ_CALLBACK', 'http://api-test.jiangsuyishengai.com'), //回调地址
        'appKey' => env('JZQ_APPKEY', '6cf285ced4ec1265'),
        'appSecret' => env('JZQ_APPSECRET', 'f7cd27696cf285ced4ec12650d22074c'),
        'Timeout' => 10,
        'MaxRetries' => 3,
        'encryMethod' => 'md5', //加密方式
        'Api' => [
            'OrganizationCreate' => '/v2/user/organizationCreate', //企业实名认证上传
            'OrganizationReapply' => '/v2/user/organizationReapply', //企业实名认证重新上传
            'UploadEntSign' => '/v2/user/uploadEntSign', //上传企业自定义印章
            'CloudCertiPerInfo' => '/v2/user/cloudCertiPerInfo', //个人实名认证上传
            'FileApplySign' => '/v2/sign/applySign', //签约发起-文件类api
            'GetLinkNew' => '/v2/sign/link', //获取用户签署链接地址， url有效期72小时
            'GetLinkAnonyDetail' => '/v2/sign/linkAnonyDetail', // 获取签约查看链接
        ],
        'Callback' => [
            'OrganizationCallback' => '/admin/shop/enterpriseAuthn/callback', // 企业认证回调
            'SignCallback' => '/api/contract/signNotify', //签署回调
        ],
    ],
    'services' => [
        'Member' => [ //ysa-member, 主要是会员服务
            'BaseURL' => env('YSA_MEMBER_DOMAIN'),
            'Timeout' => 10,
            'MaxRetries' => 3,
            'Api' => [
                'PostMemberWish' => '/api/wish', //发布会员愿望
                'GetMemberWish' => '/api/wish/index', //获取会员愿望
                'GetMyWishList' => '/api/wish/my', //获取会员愿望列表
                'GetMemberIntegral' => '/api/member/integral', //获取会员积分

                'MemberSign' => '/api/member/signin', // 会员签到
                'MemberActivity' => '/api/member/activity', // 活动报名
                'GetOrderRewards' => '/api/member/order/rewards', // 下单奖励(无登录)
                'OrderDeductIntegral' => '/api/member/order', // 下单扣积分
                'OrderCancelIntegral' => '/api/member/order-cancel', // 取消订单退积分
                'GetMemberCalendar' => '/api/member/signin/calendar', //获取签到日历
                'GetMemberTask' => '/api/member/integral/task', // 获取任务列表
                'GetMemberExchange' => '/api/member/integral/exchange', // 获取兑换列表
                'GetMemberEarn' => '/api/member/integral/earn', // 积分获取明细列表
                'GetMemberConsume' => '/api/member/integral/consume', // 积分消费明细列表

                'NewMember' => '/api/member/invite', // 注册新用户(邀请和注册发放积分)
                'MemberExchange' => '/api/member/exchange', //兑换礼品

                'LotteryWap' => '/api/lottery/wap', //抽奖活动
                'DoLotteryPrize' => '/api/lottery/prize', //点击抽奖
                'AddChance' => '/api/lottery/chance', //增加抽奖次数
                'GetChance' => '/api/lottery/chance', //获取抽奖次数列表
                'PrizeClaim' => '/api/lottery/prize/%s/claim', //领取奖品
                'GetMyPrizes' => '/api/lottery/my-prizes', //获取中奖记录
            ],
        ],
        'SelfMeeting' => [ //自搭视频会议
            'BaseURL' => env('YSA_MEETING_DOMAIN'),
            'Timeout' => 10,
            'MaxRetries' => 3,
            'Api' => [
                'CreateRoom' => '/webrtc-api/v1/rooms', //创建房间
                'CancelRoom' => '/webrtc-api/v1/rooms/%s/cancel', //取消房间
                'EndRoom' => '/webrtc-api/v1/rooms/%s/end', //结束房间
                'GetRoomInfo' => '/webrtc-api/v1/rooms/%s', //获取会议详情
            ],
        ]
        // 其他服务...
    ],
];
