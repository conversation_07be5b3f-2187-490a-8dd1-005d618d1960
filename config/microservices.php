<?php
// +----------------------------------------------------------------------
// | 微服务设置
// +----------------------------------------------------------------------
// 同一个微服务，原则上只又一个对应的key，key的取值与微服务名一致（git仓库名称）

return [
    'ysa-college' => [
        //对应微服务的请求domain，在env文件中配置
        'domain'    => \think\facade\Env::get('YSA_COLLEGE_DOMAIN'),
        'interface' => [
            // key : 动词（GET、POST、PUT、DELETE）+ value值，使用小驼峰命名规范
            // value : 微服务请求接口名称

            // 顶部导航
            'getIndexTopMenu'            => '/index/topMenu',// GET,顶部导航

            // 课程
            'getCourseIndex'             => '/course/index',// GET,课程中心首页
            'postCourse'                 => '/course/',// POST,添加
            'getCourse'                  => '/course/',// GET,列表
            'getArticleList'             => '/course/tree',// GET,文章列表（宝妈智库列表）
            'getCourseList'              => '/course/list',// GET,课程列表（技能培训列表）
            'getFreeCourseList'          => '/course/free',// GET,课程列表（好课试听列表）
            'getHotCourseList'           => '/course/hot',// GET,课程列表（热门好课列表）
            'getStoryList'               => '/course/story',// GET,文章列表（就业故事）
            'getCourseById'              => '/course',// GET,详情
            'getSdkCourseById'           => '/course/basic',// GET,详情（基础数据结构）
            'putCourse'                  => '/course',// PUT,更新
            'putCourseSort'              => '/course/sort',// PUT,更新排序
            'putCourseViewCount'         => '/course/view_count',// PUT,更新阅读次数
            'deleteCourse'               => '/course',// PUT,删除

            // 课程章节
            'getCourseSectionByCourseId' => '/course_section',// GET,列表,通过课程ID获取章节列表
            'getCourseSectionById'       => '/course_section/detail',// GET,通过章节ID获取详情
            'postCourseSection'          => '/course_section/',// POST,添加
            'putCourseSection'           => '/course_section',// PUT,更新
            'putCourseSectionSort'       => '/course_section/sort',// PUT,更新排序
            'deleteCourseSection'        => '/course_section',// PUT,删除

            // 分类
            'postCategory'               => '/category/',// POST,添加
            'getCategory'                => '/category/',// GET,列表
            'getCategoryById'            => '/category',// GET,详情
            'putCategory'                => '/category',// PUT,更新
            'putCategorySort'            => '/category/sort',// PUT,更新排序
            'deleteCategory'             => '/category',// PUT,删除

            // 课程学习
            'postMemberCourseRecord'     => '/member_course_record/',// POST,添加
            'getMemberCourseRecord'      => '/member_course_record/',// GET,列表
            'getMemberCourseRecordById'  => '/member_course_record',// GET,详情
            'putMemberCourseRecord'      => '/member_course_record',// PUT,更新
            'putMemberCourseRecordSort'  => '/member_course_record/sort',// PUT,更新排序
            'deleteMemberCourseRecord'   => '/member_course_record',// PUT,删除

            // 专家
            'postExpert'                 => '/expert/',// POST,添加
            'getExpert'                  => '/expert/',// GET,列表  ===
            'getExpertCategoryList'      => '/expert/categories',// GET,分类列表（全科专家团）  ===
            'getHotExpertList'           => '/expert/hot',// GET,列表（热门专家）  ===
            'getTreeExpertList'          => '/expert/tree',// GET,列表（全科专家团分类列表）  ===
            'getExpertById'              => '/expert',// GET,详情
            'getExpertCourseById'        => '/expert/course',// GET,详情（课程列表&文章列表）  ===
            'putExpert'                  => '/expert',// PUT,更新
            'putExpertSort'              => '/expert/sort',// PUT,更新排序
            'deleteExpert'               => '/expert',// PUT,删除

            // 证书
            'postCertificate'            => '/certificate/',// POST,添加
            'getCertificate'             => '/certificate/',// GET,列表
            'getHotCertificateList'      => '/certificate/hot',// GET,列表（热门证书） ===
            'getTreeCertificateList'     => '/certificate/tree',// GET,列表 ===
            'getCertificateById'         => '/certificate',// GET,详情
            'getCertificateCourseById'   => '/certificate/course',// GET,详情(包含课程列表)  ===
            'putCertificate'             => '/certificate',// PUT,更新
            'putCertificateSort'         => '/certificate/sort',// PUT,更新排序
            'deleteCertificate'          => '/certificate',// PUT,删除
        ]
    ],

    'ysa-member' => [
        'domain'    => \think\facade\Env::get('YSA_MEMBER_DOMAIN'),
        'interface' => [
            //适用于管理后台（总部、门店……）
            'admin' => [
                // 优惠券模板
                'postCouponTemplate'      => '/admin/coupon/template',// POST,添加
                'getCouponTemplate'       => '/admin/coupon/template/list',// GET,列表
                'putCouponTemplate'       => '/admin/coupon/template',// PUT,更新（除状态字段）
                'putCouponTemplateStatus' => '/admin/coupon/template/status',// PUT,更新优惠券模板状态
                'getCouponTemplateById'   => '/admin/coupon/template',// GET,详情
                'deleteCouponTemplate'    => '/admin/coupon/template',// DELETE,删除
                // 优惠券
                'postCoupon'              => '/admin/coupon',// POST,添加（手工补发）
                'getCouponList'           => '/admin/coupon/list',// GET,列表
                'getCouponById'           => '/admin/coupon',// GET,详情
            ],
            // 使用于移动端（小程序端）
            'api'   => [
                // 优惠券
                'postCoupon'           => '/api/coupon',// POST,添加（用户领取）
                'getCouponList'        => '/api/coupon/list',// GET,列表,通过用户ID获取列表（我的）
                'getCouponOrderList'   => '/api/coupon/order/list',// GET,列表,通过用户ID获取列表（下单）
                'putCoupon'            => '/api/coupon',// PUT,更新（状态变更）
                'getCouponCount'       => '/api/coupon/count',//GET,获取指定用户可用优惠券的数量
            ]
        ]
    ]
];
